package unit

import (
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"

	"wallet-platform/internal/config"
	"wallet-platform/internal/middleware"
)

type MiddlewareTestSuite struct {
	suite.Suite
	router *gin.Engine
}

func (suite *MiddlewareTestSuite) SetupTest() {
	gin.SetMode(gin.TestMode)
	suite.router = gin.New()

	// Set test environment variable
	os.Setenv("INTERNAL_API_KEY", "test-internal-key")
}

func (suite *MiddlewareTestSuite) TearDownTest() {
	os.Unsetenv("INTERNAL_API_KEY")
}

func (suite *MiddlewareTestSuite) TestInternalAuth_ValidKey() {
	// Setup route with internal auth middleware
	suite.router.GET("/test", middleware.InternalAuth(), func(c *gin.Context) {
		c.<PERSON>(http.StatusOK, gin.H{"message": "success"})
	})

	// Create request with valid internal key
	req, _ := http.NewRequest("GET", "/test", nil)
	req.Header.Set("X-Internal-Key", "test-internal-key")
	req.Header.Set("X-Service-Name", "test-service")
	req.Header.Set("X-User-ID", "user123")

	// Perform request
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(suite.T(), http.StatusOK, w.Code)
	assert.Contains(suite.T(), w.Body.String(), "success")
}

func (suite *MiddlewareTestSuite) TestInternalAuth_InvalidKey() {
	// Setup route with internal auth middleware
	suite.router.GET("/test", middleware.InternalAuth(), func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "success"})
	})

	// Create request with invalid internal key
	req, _ := http.NewRequest("GET", "/test", nil)
	req.Header.Set("X-Internal-Key", "invalid-key")

	// Perform request
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(suite.T(), http.StatusUnauthorized, w.Code)
	assert.Contains(suite.T(), w.Body.String(), "Invalid internal API key")
}

func (suite *MiddlewareTestSuite) TestInternalAuth_MissingKey() {
	// Setup route with internal auth middleware
	suite.router.GET("/test", middleware.InternalAuth(), func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "success"})
	})

	// Create request without internal key
	req, _ := http.NewRequest("GET", "/test", nil)

	// Perform request
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(suite.T(), http.StatusUnauthorized, w.Code)
}

func (suite *MiddlewareTestSuite) TestInternalAuth_MissingEnvironmentKey() {
	// Remove environment variable
	os.Unsetenv("INTERNAL_API_KEY")

	// Setup route with internal auth middleware
	suite.router.GET("/test", middleware.InternalAuth(), func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "success"})
	})

	// Create request with internal key
	req, _ := http.NewRequest("GET", "/test", nil)
	req.Header.Set("X-Internal-Key", "test-key")

	// Perform request
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(suite.T(), http.StatusInternalServerError, w.Code)
	assert.Contains(suite.T(), w.Body.String(), "Internal API key not configured")
}

func (suite *MiddlewareTestSuite) TestOptionalInternalAuth_WithValidKey() {
	// Setup route with optional internal auth middleware
	suite.router.GET("/test", middleware.OptionalInternalAuth(), func(c *gin.Context) {
		authType, exists := c.Get("auth_type")
		if exists {
			c.JSON(http.StatusOK, gin.H{"auth_type": authType})
		} else {
			c.JSON(http.StatusOK, gin.H{"auth_type": "none"})
		}
	})

	// Create request with valid internal key
	req, _ := http.NewRequest("GET", "/test", nil)
	req.Header.Set("X-Internal-Key", "test-internal-key")

	// Perform request
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(suite.T(), http.StatusOK, w.Code)
	assert.Contains(suite.T(), w.Body.String(), "internal")
}

func (suite *MiddlewareTestSuite) TestOptionalInternalAuth_WithoutKey() {
	// Setup route with optional internal auth middleware
	suite.router.GET("/test", middleware.OptionalInternalAuth(), func(c *gin.Context) {
		authType, exists := c.Get("auth_type")
		if exists {
			c.JSON(http.StatusOK, gin.H{"auth_type": authType})
		} else {
			c.JSON(http.StatusOK, gin.H{"auth_type": "none"})
		}
	})

	// Create request without internal key
	req, _ := http.NewRequest("GET", "/test", nil)

	// Perform request
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(suite.T(), http.StatusOK, w.Code)
	assert.Contains(suite.T(), w.Body.String(), "none")
}

func (suite *MiddlewareTestSuite) TestRateLimit() {
	// Create mock rate limit config
	rateLimitConfig := config.RateLimitConfig{
		Enabled: false, // Disable for testing
		Limit:   100,
		Window:  60,
	}

	// Setup route with rate limiting
	suite.router.GET("/test", middleware.RateLimit(nil, rateLimitConfig), func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "success"})
	})

	// Test multiple requests within rate limit
	for i := 0; i < 5; i++ {
		req, _ := http.NewRequest("GET", "/test", nil)
		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)
		assert.Equal(suite.T(), http.StatusOK, w.Code)
	}
}

func (suite *MiddlewareTestSuite) TestCORS() {
	// Create mock CORS config
	corsConfig := config.CORSConfig{
		AllowOrigins: []string{"http://localhost:3000"},
		AllowMethods: []string{"GET", "POST", "OPTIONS"},
		AllowHeaders: []string{"Content-Type", "Authorization"},
	}

	// Setup route with CORS middleware
	suite.router.GET("/test", middleware.CORS(corsConfig), func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "success"})
	})

	// Create OPTIONS request
	req, _ := http.NewRequest("OPTIONS", "/test", nil)
	req.Header.Set("Origin", "http://localhost:3000")

	// Perform request
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(suite.T(), http.StatusOK, w.Code)
	assert.Equal(suite.T(), "*", w.Header().Get("Access-Control-Allow-Origin"))
}

func (suite *MiddlewareTestSuite) TestContentType() {
	// Setup route with content type middleware
	suite.router.POST("/test", middleware.ContentType("application/json"), func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "success"})
	})

	// Create request with correct content type
	req, _ := http.NewRequest("POST", "/test", nil)
	req.Header.Set("Content-Type", "application/json")

	// Perform request
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(suite.T(), http.StatusOK, w.Code)
}

func (suite *MiddlewareTestSuite) TestContentType_Invalid() {
	// Setup route with content type middleware
	suite.router.POST("/test", middleware.ContentType("application/json"), func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "success"})
	})

	// Create request with incorrect content type
	req, _ := http.NewRequest("POST", "/test", nil)
	req.Header.Set("Content-Type", "text/plain")

	// Perform request
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(suite.T(), http.StatusUnsupportedMediaType, w.Code)
}

func TestMiddlewareTestSuite(t *testing.T) {
	suite.Run(t, new(MiddlewareTestSuite))
}
