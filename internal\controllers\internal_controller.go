package controllers

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"wallet-platform/internal/services"

	"github.com/gin-gonic/gin"
)

// InternalController handles internal API requests for service-to-service communication
type InternalController struct {
	container *services.Container
}

// NewInternalController creates a new internal controller
func NewInternalController(container *services.Container) *InternalController {
	return &InternalController{
		container: container,
	}
}

// InternalWebhookRequest represents internal webhook request payload
type InternalWebhookRequest struct {
	Event     string                 `json:"event" binding:"required"`
	Data      map[string]interface{} `json:"data" binding:"required"`
	Source    string                 `json:"source" binding:"required"`
	Target    string                 `json:"target"`
	Timestamp string                 `json:"timestamp"`
}

// Wallet Operations

// GetWalletByPhone retrieves wallet by phone number (internal use)
func (ic *InternalController) GetWalletByPhone(c *gin.Context) {
	phoneNumber := c.<PERSON>m("phone")
	if phoneNumber == "" {
		c.<PERSON>(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_PHONE",
			Message: "Phone number is required",
			Code:    "400",
		})
		return
	}

	// Get wallet directly by phone number
	wallet, err := ic.container.WalletService.GetWalletByPhone(phoneNumber)
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{
			Error:   "WALLET_NOT_FOUND",
			Message: "Wallet with this phone number not found",
			Code:    "404",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    wallet,
	})
}

// Card Operations

// GetCardsByWallet retrieves all cards for a specific wallet (internal use)
func (ic *InternalController) GetCardsByWallet(c *gin.Context) {
	walletIDStr := c.Param("wallet_id")
	walletID, err := strconv.ParseUint(walletIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_WALLET_ID",
			Message: "Invalid wallet ID",
			Code:    "400",
		})
		return
	}

	// Get cards for the wallet
	cards, err := ic.container.PayCardService.GetCardsByWallet(uint(walletID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "FETCH_FAILED",
			Message: err.Error(),
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    cards,
	})
}

// Webhook Operations

// SendInternalWebhook sends webhook to internal services
func (ic *InternalController) SendInternalWebhook(c *gin.Context) {
	var req InternalWebhookRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	// Validate required fields
	if req.Event == "" || req.Source == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "MISSING_FIELDS",
			Message: "Event and source are required",
			Code:    "400",
		})
		return
	}

	// Process the internal webhook
	err := ic.processInternalWebhook(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "WEBHOOK_PROCESSING_FAILED",
			Message: err.Error(),
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Internal webhook processed successfully",
		"data": gin.H{
			"event":  req.Event,
			"source": req.Source,
			"target": req.Target,
		},
	})
}

// Helper Methods

// processInternalWebhook processes internal webhook events
func (ic *InternalController) processInternalWebhook(req *InternalWebhookRequest) error {
	// Log the webhook event
	ic.container.Logger.LogSystem("internal_webhook_received", req.Source, "",
		"Received internal webhook: "+req.Event)

	// Process different types of internal webhook events
	switch req.Event {
	case "wallet.created":
		return ic.handleWalletCreatedWebhook(req)
	case "wallet.updated":
		return ic.handleWalletUpdatedWebhook(req)
	case "card.created":
		return ic.handleCardCreatedWebhook(req)
	case "card.blocked":
		return ic.handleCardBlockedWebhook(req)
	case "transaction.completed":
		return ic.handleTransactionCompletedWebhook(req)
	case "transaction.failed":
		return ic.handleTransactionFailedWebhook(req)
	case "fraud.detected":
		return ic.handleFraudDetectedWebhook(req)
	case "service.subscribed":
		return ic.handleServiceSubscribedWebhook(req)
	case "service.cancelled":
		return ic.handleServiceCancelledWebhook(req)
	default:
		ic.container.Logger.LogSystem("unknown_webhook_event", req.Source, "",
			"Unknown webhook event: "+req.Event)
		return nil // Don't fail for unknown events
	}
}

// Webhook event handlers

func (ic *InternalController) handleWalletCreatedWebhook(req *InternalWebhookRequest) error {
	// TODO: Implement wallet creation webhook handling
	// This could trigger notifications, analytics updates, etc.
	ic.container.Logger.LogSystem("wallet_created_webhook", req.Source, "",
		"Processing wallet creation webhook")
	return nil
}

func (ic *InternalController) handleWalletUpdatedWebhook(req *InternalWebhookRequest) error {
	// TODO: Implement wallet update webhook handling
	ic.container.Logger.LogSystem("wallet_updated_webhook", req.Source, "",
		"Processing wallet update webhook")
	return nil
}

func (ic *InternalController) handleCardCreatedWebhook(req *InternalWebhookRequest) error {
	// TODO: Implement card creation webhook handling
	ic.container.Logger.LogSystem("card_created_webhook", req.Source, "",
		"Processing card creation webhook")
	return nil
}

func (ic *InternalController) handleCardBlockedWebhook(req *InternalWebhookRequest) error {
	// TODO: Implement card blocking webhook handling
	ic.container.Logger.LogSystem("card_blocked_webhook", req.Source, "",
		"Processing card blocking webhook")
	return nil
}

func (ic *InternalController) handleTransactionCompletedWebhook(req *InternalWebhookRequest) error {
	// TODO: Implement transaction completion webhook handling
	// This could update analytics, send notifications, etc.
	ic.container.Logger.LogSystem("transaction_completed_webhook", req.Source, "",
		"Processing transaction completion webhook")
	return nil
}

func (ic *InternalController) handleTransactionFailedWebhook(req *InternalWebhookRequest) error {
	// TODO: Implement transaction failure webhook handling
	ic.container.Logger.LogSystem("transaction_failed_webhook", req.Source, "",
		"Processing transaction failure webhook")
	return nil
}

func (ic *InternalController) handleFraudDetectedWebhook(req *InternalWebhookRequest) error {
	// TODO: Implement fraud detection webhook handling
	// This could trigger security alerts, block cards, etc.
	ic.container.Logger.LogSystem("fraud_detected_webhook", req.Source, "",
		"Processing fraud detection webhook")
	return nil
}

func (ic *InternalController) handleServiceSubscribedWebhook(req *InternalWebhookRequest) error {
	// TODO: Implement service subscription webhook handling
	ic.container.Logger.LogSystem("service_subscribed_webhook", req.Source, "",
		"Processing service subscription webhook")
	return nil
}

func (ic *InternalController) handleServiceCancelledWebhook(req *InternalWebhookRequest) error {
	// TODO: Implement service cancellation webhook handling
	ic.container.Logger.LogSystem("service_cancelled_webhook", req.Source, "",
		"Processing service cancellation webhook")
	return nil
}

// Health Check for Internal Services

// InternalHealthCheck provides health status for internal service monitoring
func (ic *InternalController) InternalHealthCheck(c *gin.Context) {
	// Check if this is an internal service request
	serviceName := c.GetHeader("X-Service-Name")
	if serviceName == "" {
		serviceName = "unknown"
	}

	// Get system health
	health, err := ic.container.MonitoringService.GetSystemHealth()
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "HEALTH_CHECK_FAILED",
			Message: err.Error(),
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":      true,
		"service_name": serviceName,
		"timestamp":    health.Timestamp,
		"status":       health.OverallStatus,
		"data":         health,
	})
}

// Batch Operations for Internal Services

// BatchWalletLookup allows internal services to lookup multiple wallets at once
func (ic *InternalController) BatchWalletLookup(c *gin.Context) {
	var requestBody struct {
		WalletIDs    []uint   `json:"wallet_ids"`
		PhoneNumbers []string `json:"phone_numbers"`
	}

	if err := c.ShouldBindJSON(&requestBody); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	results := make(map[string]interface{})

	// Lookup by wallet IDs
	if len(requestBody.WalletIDs) > 0 {
		wallets := make([]interface{}, 0)
		for _, walletID := range requestBody.WalletIDs {
			wallet, err := ic.container.WalletService.GetWallet(walletID)
			if err == nil {
				wallets = append(wallets, wallet)
			}
		}
		results["wallets"] = wallets
	}

	// Lookup by phone numbers
	if len(requestBody.PhoneNumbers) > 0 {
		phoneResults := make([]interface{}, 0)
		for _, phone := range requestBody.PhoneNumbers {
			wallet, err := ic.container.WalletService.GetWalletByPhone(phone)
			if err == nil {
				phoneResults = append(phoneResults, gin.H{
					"phone_number": phone,
					"wallet":       wallet,
				})
			}
		}
		results["phone_lookups"] = phoneResults
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    results,
	})
}

// BulkLookup performs batch wallet lookup by phone numbers
func (ic *InternalController) BulkLookup(c *gin.Context) {
	var req struct {
		PhoneNumbers []string `json:"phone_numbers" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	results := make([]map[string]interface{}, 0)
	found := 0
	notFound := 0

	for _, phone := range req.PhoneNumbers {
		wallet, err := ic.container.WalletService.GetWalletByPhone(phone)
		if err == nil {
			results = append(results, map[string]interface{}{
				"phone_number": phone,
				"found":        true,
				"wallet":       wallet,
			})
			found++
		} else {
			results = append(results, map[string]interface{}{
				"phone_number": phone,
				"found":        false,
				"wallet":       nil,
			})
			notFound++
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"results": results,
			"summary": gin.H{
				"total_requested": len(req.PhoneNumbers),
				"found":           found,
				"not_found":       notFound,
			},
		},
	})
}

// CollectPlatformFee collects platform fees
func (ic *InternalController) CollectPlatformFee(c *gin.Context) {
	var req struct {
		TransactionID  string  `json:"transaction_id" binding:"required"`
		FeeAmount      float64 `json:"fee_amount" binding:"required,gt=0"`
		FeeType        string  `json:"fee_type" binding:"required"`
		SourceWalletID string  `json:"source_wallet_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	// Placeholder implementation
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Platform fee collected successfully",
		"data": gin.H{
			"fee_collection_id":       "fee_" + fmt.Sprintf("%d", time.Now().Unix()),
			"transaction_id":          req.TransactionID,
			"fee_amount":              req.FeeAmount,
			"fee_type":                req.FeeType,
			"platform_wallet_balance": 50000.00 + req.FeeAmount,
			"collected_at":            time.Now().Format(time.RFC3339),
		},
	})
}

// CreateSubscription creates a new subscription
func (ic *InternalController) CreateSubscription(c *gin.Context) {
	var req struct {
		WalletID  string  `json:"wallet_id" binding:"required"`
		ServiceID string  `json:"service_id" binding:"required"`
		PlanType  string  `json:"plan_type" binding:"required"`
		Amount    float64 `json:"amount" binding:"required,gt=0"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	// Placeholder implementation
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Subscription created successfully",
		"data": gin.H{
			"subscription_id":   "sub_" + fmt.Sprintf("%d", time.Now().Unix()),
			"wallet_id":         req.WalletID,
			"service_id":        req.ServiceID,
			"plan_type":         req.PlanType,
			"amount":            req.Amount,
			"status":            "active",
			"billing_cycle":     "monthly",
			"next_billing_date": time.Now().AddDate(0, 1, 0).Format(time.RFC3339),
			"created_at":        time.Now().Format(time.RFC3339),
		},
	})
}

// GetWalletSubscriptions gets subscriptions for a wallet
func (ic *InternalController) GetWalletSubscriptions(c *gin.Context) {
	walletID := c.Param("wallet_id")
	if walletID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_WALLET_ID",
			Message: "Wallet ID is required",
			Code:    "400",
		})
		return
	}

	// Placeholder implementation
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"wallet_id": walletID,
			"subscriptions": []gin.H{
				{
					"subscription_id":   "sub_123",
					"service_id":        "service_456",
					"service_name":      "Premium Analytics",
					"plan_type":         "premium",
					"amount":            29.99,
					"status":            "active",
					"billing_cycle":     "monthly",
					"next_billing_date": time.Now().AddDate(0, 1, 0).Format(time.RFC3339),
					"created_at":        time.Now().AddDate(0, -1, 0).Format(time.RFC3339),
				},
			},
			"total_subscriptions": 1,
			"total_monthly_cost":  29.99,
		},
	})
}

// CancelSubscription cancels a subscription
func (ic *InternalController) CancelSubscription(c *gin.Context) {
	subscriptionID := c.Param("id")
	if subscriptionID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_SUBSCRIPTION_ID",
			Message: "Subscription ID is required",
			Code:    "400",
		})
		return
	}

	var req struct {
		Reason        string `json:"reason" binding:"required"`
		Notes         string `json:"notes"`
		EffectiveDate string `json:"effective_date"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	effectiveDate := req.EffectiveDate
	if effectiveDate == "" {
		effectiveDate = time.Now().Format(time.RFC3339)
	}

	// Placeholder implementation
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Subscription cancelled successfully",
		"data": gin.H{
			"subscription_id":     subscriptionID,
			"status":              "cancelled",
			"cancellation_reason": req.Reason,
			"cancelled_at":        time.Now().Format(time.RFC3339),
			"effective_date":      effectiveDate,
			"refund_amount":       0.00,
		},
	})
}

// HealthCheck provides internal health check
func (ic *InternalController) HealthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"status":    "healthy",
			"service":   "wallet-platform-internal",
			"version":   "4.0.0",
			"timestamp": time.Now().Format(time.RFC3339),
			"uptime":    86400, // Placeholder
			"dependencies": gin.H{
				"database":       "healthy",
				"payment_engine": "healthy",
				"cache":          "healthy",
			},
		},
	})
}

// ValidateAPIKey validates the internal API key
func (ic *InternalController) ValidateAPIKey(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"success":    true,
		"message":    "Internal API key is valid",
		"service":    "frontend-service",
		"request_id": c.GetHeader("X-Request-ID"),
		"timestamp":  time.Now().Format(time.RFC3339),
	})
}

// GetServiceInfo returns service information
func (ic *InternalController) GetServiceInfo(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"service": "locked-payments-service",
		"version": "4.0.0",
		"endpoints": gin.H{
			"wallets":       10,
			"cards":         11,
			"analytics":     3,
			"webhooks":      2,
			"platform":      2,
			"subscriptions": 3,
			"system":        3,
			"total":         34,
		},
		"capabilities": []string{
			"wallet_management",
			"paycard_operations",
			"analytics_reporting",
			"webhook_handling",
			"platform_operations",
			"subscription_management",
		},
		"authentication": "internal_api_key",
		"request_id":     c.GetHeader("X-Request-ID"),
		"timestamp":      time.Now().Format(time.RFC3339),
	})
}
