package services

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"wallet-platform/pkg/logger"

	"gorm.io/gorm"
)

// WebhookService handles webhook operations
type WebhookService struct {
	db         *gorm.DB
	logger     *logger.Logger
	httpClient *http.Client
}

// NewWebhookService creates a new webhook service
func NewWebhookService(db *gorm.DB, logger *logger.Logger) *WebhookService {
	return &WebhookService{
		db:     db,
		logger: logger,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// WebhookEvent represents a webhook event
type WebhookEvent struct {
	ID          uint                   `json:"id" gorm:"primaryKey"`
	EventType   string                 `json:"event_type" gorm:"not null"`
	ResourceID  string                 `json:"resource_id"`
	Data        map[string]interface{} `json:"data" gorm:"type:json"`
	Status      string                 `json:"status" gorm:"default:'pending'"` // pending, delivered, failed
	RetryCount  int                    `json:"retry_count" gorm:"default:0"`
	NextAttempt *time.Time             `json:"next_attempt"`
	LastAttempt *time.Time             `json:"last_attempt"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// WebhookEndpoint represents a webhook endpoint configuration
type WebhookEndpoint struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	URL         string    `json:"url" gorm:"not null"`
	Secret      string    `json:"secret" gorm:"not null"`
	Events      []string  `json:"events" gorm:"type:json"`
	Active      bool      `json:"active" gorm:"default:true"`
	Description string    `json:"description"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// SendWebhook sends a webhook event to all registered endpoints
func (ws *WebhookService) SendWebhook(eventType, resourceID string, data map[string]interface{}) error {
	// Create webhook event record
	event := WebhookEvent{
		EventType:   eventType,
		ResourceID:  resourceID,
		Data:        data,
		Status:      "pending",
		RetryCount:  0,
		NextAttempt: &time.Time{},
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	if err := ws.db.Create(&event).Error; err != nil {
		ws.logger.LogError(err, map[string]interface{}{
			"action":      "create_webhook_event",
			"event_type":  eventType,
			"resource_id": resourceID,
		})
		return err
	}

	// Get all active webhook endpoints that listen to this event type
	var endpoints []WebhookEndpoint
	if err := ws.db.Where("active = ? AND ? = ANY(events)", true, eventType).Find(&endpoints).Error; err != nil {
		ws.logger.LogError(err, map[string]interface{}{
			"action":     "get_webhook_endpoints",
			"event_type": eventType,
		})
		return err
	}

	// Send webhook to each endpoint asynchronously
	for _, endpoint := range endpoints {
		go ws.deliverWebhook(&event, &endpoint)
	}

	return nil
}

// deliverWebhook delivers a webhook to a specific endpoint
func (ws *WebhookService) deliverWebhook(event *WebhookEvent, endpoint *WebhookEndpoint) {
	payload := map[string]interface{}{
		"id":          event.ID,
		"event_type":  event.EventType,
		"resource_id": event.ResourceID,
		"data":        event.Data,
		"timestamp":   event.CreatedAt,
	}

	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		ws.handleWebhookFailure(event, fmt.Errorf("failed to marshal payload: %w", err))
		return
	}

	// Generate signature
	signature := ws.generateSignature(payloadBytes, endpoint.Secret)

	// Create HTTP request
	req, err := http.NewRequest("POST", endpoint.URL, bytes.NewBuffer(payloadBytes))
	if err != nil {
		ws.handleWebhookFailure(event, fmt.Errorf("failed to create request: %w", err))
		return
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Webhook-Signature", signature)
	req.Header.Set("X-Event-Type", event.EventType)
	req.Header.Set("X-Event-ID", fmt.Sprintf("%d", event.ID))
	req.Header.Set("User-Agent", "WalletPlatform-Webhook/1.0")

	// Send request
	resp, err := ws.httpClient.Do(req)
	if err != nil {
		ws.handleWebhookFailure(event, fmt.Errorf("request failed: %w", err))
		return
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode >= 200 && resp.StatusCode < 300 {
		// Success
		event.Status = "delivered"
		event.LastAttempt = &time.Time{}
		*event.LastAttempt = time.Now()
		event.UpdatedAt = time.Now()

		if err := ws.db.Save(event).Error; err != nil {
			ws.logger.LogError(err, map[string]interface{}{
				"action":   "update_webhook_success",
				"event_id": event.ID,
			})
		}

		ws.logger.LogTransaction(
			event.ResourceID,
			fmt.Sprintf("webhook_%d", event.ID),
			"webhook_delivered",
			0,
			fmt.Sprintf("Webhook delivered successfully: %s", event.EventType),
		)
	} else {
		ws.handleWebhookFailure(event, fmt.Errorf("webhook delivery failed with status %d", resp.StatusCode))
	}
}

// handleWebhookFailure handles webhook delivery failures with retry logic
func (ws *WebhookService) handleWebhookFailure(event *WebhookEvent, err error) {
	event.RetryCount++
	now := time.Now()
	event.LastAttempt = &now

	// Exponential backoff: 1min, 2min, 4min, 8min, 16min
	backoffMinutes := 1 << uint(event.RetryCount-1)
	if backoffMinutes > 16 {
		backoffMinutes = 16
	}

	nextAttempt := time.Now().Add(time.Duration(backoffMinutes) * time.Minute)
	event.NextAttempt = &nextAttempt

	// Mark as failed after 5 attempts
	if event.RetryCount >= 5 {
		event.Status = "failed"
	} else {
		event.Status = "pending"
	}

	event.UpdatedAt = time.Now()

	if dbErr := ws.db.Save(event).Error; dbErr != nil {
		ws.logger.LogError(dbErr, map[string]interface{}{
			"action":   "update_webhook_failure",
			"event_id": event.ID,
		})
	}

	ws.logger.LogError(err, map[string]interface{}{
		"action":       "webhook_delivery_failed",
		"event_id":     event.ID,
		"event_type":   event.EventType,
		"retry_count":  event.RetryCount,
		"next_attempt": nextAttempt,
	})
}

// generateSignature generates HMAC signature for webhook payload
func (ws *WebhookService) generateSignature(payload []byte, secret string) string {
	h := hmac.New(sha256.New, []byte(secret))
	h.Write(payload)
	return hex.EncodeToString(h.Sum(nil))
}

// RegisterWebhookEndpoint registers a new webhook endpoint
func (ws *WebhookService) RegisterWebhookEndpoint(url, secret, description string, events []string) (*WebhookEndpoint, error) {
	endpoint := WebhookEndpoint{
		URL:         url,
		Secret:      secret,
		Events:      events,
		Active:      true,
		Description: description,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	if err := ws.db.Create(&endpoint).Error; err != nil {
		ws.logger.LogError(err, map[string]interface{}{
			"action": "register_webhook_endpoint",
			"url":    url,
		})
		return nil, err
	}

	return &endpoint, nil
}

// GetWebhookEvents retrieves webhook events with pagination
func (ws *WebhookService) GetWebhookEvents(limit, offset int, status string) ([]WebhookEvent, error) {
	var events []WebhookEvent
	query := ws.db.Order("created_at DESC").Limit(limit).Offset(offset)

	if status != "" {
		query = query.Where("status = ?", status)
	}

	if err := query.Find(&events).Error; err != nil {
		return nil, err
	}

	return events, nil
}

// RetryFailedWebhooks retries failed webhook deliveries
func (ws *WebhookService) RetryFailedWebhooks() error {
	var events []WebhookEvent
	now := time.Now()

	if err := ws.db.Where("status = ? AND next_attempt <= ?", "pending", now).Find(&events).Error; err != nil {
		return err
	}

	for _, event := range events {
		var endpoints []WebhookEndpoint
		if err := ws.db.Where("active = ? AND ? = ANY(events)", true, event.EventType).Find(&endpoints).Error; err != nil {
			continue
		}

		for _, endpoint := range endpoints {
			go ws.deliverWebhook(&event, &endpoint)
		}
	}

	return nil
}
