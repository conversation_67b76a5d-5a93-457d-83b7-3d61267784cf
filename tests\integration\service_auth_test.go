package integration

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"

	"wallet-platform/internal/config"
	"wallet-platform/internal/controllers"
	"wallet-platform/internal/database"
	"wallet-platform/internal/routes"
	"wallet-platform/internal/services"
	"wallet-platform/pkg/logger"
)

type ServiceAuthTestSuite struct {
	suite.Suite
	app    *gin.Engine
	db     *gorm.DB
	config *config.Config
	logger *logger.Logger
}

func (suite *ServiceAuthTestSuite) SetupSuite() {
	// Set test environment
	os.Setenv("APP_ENVIRONMENT", "test")
	os.Setenv("INTERNAL_API_KEY", "test-internal-api-key")

	// Setup test database
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	assert.NoError(suite.T(), err)

	// Auto-migrate all models
	err = database.AutoMigrate(db)
	assert.NoError(suite.T(), err)

	suite.db = db
	suite.logger = logger.New()

	// Setup test configuration
	suite.config = &config.Config{
		App: config.AppConfig{
			Environment: "test",
			Debug:       true,
		},
		Server: config.ServerConfig{
			Port: 8086,
			Host: "localhost",
		},
	}

	// Setup services
	container := services.NewContainer(db, suite.logger, suite.config)

	// Setup controllers
	walletController := controllers.NewWalletController(container)
	payCardController := controllers.NewPayCardController(container)
	analyticsController := controllers.NewAnalyticsController(container)
	webhookController := controllers.NewWebhookController(container)

	// Setup Gin app
	gin.SetMode(gin.TestMode)
	suite.app = gin.New()

	// Setup routes
	routes.SetupRoutes(suite.app, walletController, payCardController, analyticsController, webhookController)
}

func (suite *ServiceAuthTestSuite) TearDownSuite() {
	sqlDB, _ := suite.db.DB()
	sqlDB.Close()

	os.Unsetenv("APP_ENVIRONMENT")
	os.Unsetenv("INTERNAL_API_KEY")
}

func (suite *ServiceAuthTestSuite) SetupTest() {
	// Clean database before each test
	suite.db.Exec("DELETE FROM wallet_transactions")
	suite.db.Exec("DELETE FROM card_transactions")
	suite.db.Exec("DELETE FROM pay_cards")
	suite.db.Exec("DELETE FROM wallets")
}

// Helper function to make service-to-service requests
func (suite *ServiceAuthTestSuite) makeServiceRequest(method, url string, body interface{}, serviceName, userID, walletID string) *httptest.ResponseRecorder {
	var reqBody *bytes.Buffer
	if body != nil {
		jsonData, _ := json.Marshal(body)
		reqBody = bytes.NewBuffer(jsonData)
	} else {
		reqBody = bytes.NewBuffer([]byte{})
	}

	req, _ := http.NewRequest(method, url, reqBody)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Internal-Key", "test-internal-api-key")

	if serviceName != "" {
		req.Header.Set("X-Service-Name", serviceName)
	}
	if userID != "" {
		req.Header.Set("X-User-ID", userID)
	}
	if walletID != "" {
		req.Header.Set("X-Wallet-ID", walletID)
	}

	w := httptest.NewRecorder()
	suite.app.ServeHTTP(w, req)
	return w
}

func (suite *ServiceAuthTestSuite) TestServiceAuth_PaymentEngineIntegration() {
	// Simulate payment engine creating a wallet for a user
	payload := map[string]interface{}{
		"phone_number": "+256701234567",
		"wallet_type":  "individual",
	}

	w := suite.makeServiceRequest("POST", "/api/v1/internal/wallets", payload, "payment-engine", "user123", "")

	assert.Equal(suite.T(), http.StatusCreated, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.True(suite.T(), response["success"].(bool))

	data := response["data"].(map[string]interface{})
	assert.Equal(suite.T(), "+256701234567", data["phone_number"])
	assert.Equal(suite.T(), "individual", data["wallet_type"])
}

func (suite *ServiceAuthTestSuite) TestServiceAuth_UserServiceIntegration() {
	// First create a wallet
	createPayload := map[string]interface{}{
		"phone_number": "+256701234567",
		"wallet_type":  "individual",
	}
	createResp := suite.makeServiceRequest("POST", "/api/v1/internal/wallets", createPayload, "user-service", "user123", "")
	assert.Equal(suite.T(), http.StatusCreated, createResp.Code)

	var createResponse map[string]interface{}
	json.Unmarshal(createResp.Body.Bytes(), &createResponse)
	data := createResponse["data"].(map[string]interface{})
	walletID := data["id"].(float64)

	// Simulate user service getting wallet balance
	w := suite.makeServiceRequest("GET", "/api/v1/internal/wallets/phone/+256701234567", nil, "user-service", "user123", "")

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.True(suite.T(), response["success"].(bool))

	responseData := response["data"].(map[string]interface{})
	assert.Equal(suite.T(), walletID, responseData["id"].(float64))
}

func (suite *ServiceAuthTestSuite) TestServiceAuth_NotificationServiceIntegration() {
	// Create a wallet and card
	walletPayload := map[string]interface{}{
		"phone_number": "+256701234567",
		"wallet_type":  "individual",
	}
	walletResp := suite.makeServiceRequest("POST", "/api/v1/internal/wallets", walletPayload, "notification-service", "user123", "")
	assert.Equal(suite.T(), http.StatusCreated, walletResp.Code)

	var walletResponse map[string]interface{}
	json.Unmarshal(walletResp.Body.Bytes(), &walletResponse)
	walletData := walletResponse["data"].(map[string]interface{})
	walletID := walletData["id"].(float64)

	// Create a card
	cardPayload := map[string]interface{}{
		"wallet_id": walletID,
		"card_type": "virtual",
		"card_name": "Test Card",
	}
	cardResp := suite.makeServiceRequest("POST", "/api/v1/internal/cards", cardPayload, "notification-service", "user123", "")
	assert.Equal(suite.T(), http.StatusCreated, cardResp.Code)

	var cardResponse map[string]interface{}
	json.Unmarshal(cardResp.Body.Bytes(), &cardResponse)
	cardData := cardResponse["data"].(map[string]interface{})
	cardID := cardData["id"].(float64)

	// Simulate notification service getting cards for a wallet
	w := suite.makeServiceRequest("GET", "/api/v1/internal/cards/wallet/"+fmt.Sprintf("%.0f", walletID), nil, "notification-service", "user123", "")

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.True(suite.T(), response["success"].(bool))

	cards := response["data"].([]interface{})
	assert.Len(suite.T(), cards, 1)

	card := cards[0].(map[string]interface{})
	assert.Equal(suite.T(), cardID, card["id"].(float64))
}

func (suite *ServiceAuthTestSuite) TestServiceAuth_InvalidAPIKey() {
	payload := map[string]interface{}{
		"phone_number": "+256701234567",
		"wallet_type":  "individual",
	}

	// Make request with invalid API key
	req, _ := http.NewRequest("POST", "/api/v1/internal/wallets", bytes.NewBuffer([]byte{}))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Internal-Key", "invalid-api-key")
	req.Header.Set("X-Service-Name", "test-service")

	jsonData, _ := json.Marshal(payload)
	req.Body = http.NoBody
	req.Body = bytes.NewReader(jsonData)

	w := httptest.NewRecorder()
	suite.app.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusUnauthorized, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.False(suite.T(), response["success"].(bool))
	assert.Contains(suite.T(), response["error"].(map[string]interface{})["message"], "Invalid internal API key")
}

func (suite *ServiceAuthTestSuite) TestServiceAuth_MissingAPIKey() {
	payload := map[string]interface{}{
		"phone_number": "+256701234567",
		"wallet_type":  "individual",
	}

	// Make request without API key
	jsonData, _ := json.Marshal(payload)
	req, _ := http.NewRequest("POST", "/api/v1/internal/wallets", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	suite.app.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusUnauthorized, w.Code)
}

func (suite *ServiceAuthTestSuite) TestServiceAuth_ContextPropagation() {
	// Test that user context is properly propagated
	payload := map[string]interface{}{
		"phone_number": "+256701234567",
		"wallet_type":  "individual",
	}

	w := suite.makeServiceRequest("POST", "/api/v1/internal/wallets", payload, "payment-engine", "user456", "")

	assert.Equal(suite.T(), http.StatusCreated, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.True(suite.T(), response["success"].(bool))

	// The user context should be available in the service layer
	// This would be verified through logs or database records in a real implementation
}

func (suite *ServiceAuthTestSuite) TestServiceAuth_RateLimiting() {
	// Test that internal services have higher rate limits
	payload := map[string]interface{}{
		"phone_number": "+256701234567",
		"wallet_type":  "individual",
	}

	// Make multiple requests rapidly (should not hit rate limit for internal services)
	for i := 0; i < 10; i++ {
		phoneNumber := fmt.Sprintf("+25670123456%d", i)
		testPayload := map[string]interface{}{
			"phone_number": phoneNumber,
			"wallet_type":  "individual",
		}

		w := suite.makeServiceRequest("POST", "/api/v1/internal/wallets", testPayload, "payment-engine", "user123", "")
		assert.Equal(suite.T(), http.StatusCreated, w.Code)
	}
}

func (suite *ServiceAuthTestSuite) TestServiceAuth_MultipleServices() {
	// Test that different services can access the same resources
	payload := map[string]interface{}{
		"phone_number": "+256701234567",
		"wallet_type":  "individual",
	}

	// Payment engine creates wallet
	w1 := suite.makeServiceRequest("POST", "/api/v1/internal/wallets", payload, "payment-engine", "user123", "")
	assert.Equal(suite.T(), http.StatusCreated, w1.Code)

	// User service accesses the same wallet
	w2 := suite.makeServiceRequest("GET", "/api/v1/internal/wallets/phone/+256701234567", nil, "user-service", "user123", "")
	assert.Equal(suite.T(), http.StatusOK, w2.Code)

	// Notification service also accesses the wallet
	w3 := suite.makeServiceRequest("GET", "/api/v1/internal/wallets/phone/+256701234567", nil, "notification-service", "user123", "")
	assert.Equal(suite.T(), http.StatusOK, w3.Code)

	// All responses should return the same wallet data
	var resp1, resp2, resp3 map[string]interface{}
	json.Unmarshal(w1.Body.Bytes(), &resp1)
	json.Unmarshal(w2.Body.Bytes(), &resp2)
	json.Unmarshal(w3.Body.Bytes(), &resp3)

	data1 := resp1["data"].(map[string]interface{})
	data2 := resp2["data"].(map[string]interface{})
	data3 := resp3["data"].(map[string]interface{})

	assert.Equal(suite.T(), data1["id"], data2["id"])
	assert.Equal(suite.T(), data1["id"], data3["id"])
}

func (suite *ServiceAuthTestSuite) TestServiceAuth_ErrorHandling() {
	// Test error handling with service authentication
	payload := map[string]interface{}{
		"phone_number": "invalid-phone",
		"wallet_type":  "individual",
	}

	w := suite.makeServiceRequest("POST", "/api/v1/internal/wallets", payload, "payment-engine", "user123", "")

	assert.Equal(suite.T(), http.StatusBadRequest, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.False(suite.T(), response["success"].(bool))
	assert.NotNil(suite.T(), response["error"])
}

func TestServiceAuthTestSuite(t *testing.T) {
	suite.Run(t, new(ServiceAuthTestSuite))
}
