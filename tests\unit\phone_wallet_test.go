package unit

import (
	"testing"
	"wallet-platform/internal/models"
	"wallet-platform/internal/services"
	"wallet-platform/pkg/logger"

	"wallet-platform/pkg/redis"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// TestPhoneWalletOperations tests phone-based wallet operations
func TestPhoneWalletOperations(t *testing.T) {
	// Setup in-memory database for testing
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		t.Fatalf("Failed to connect to test database: %v", err)
	}

	// Auto-migrate the schema
	err = db.AutoMigrate(&models.Wallet{}, &models.WalletTransaction{}, &models.WalletTransfer{})
	if err != nil {
		t.Fatalf("Failed to migrate database: %v", err)
	}

	// Create mock dependencies
	redisClient := &redis.Client{} // Mock redis client
	log := &logger.Logger{}        // Mock logger

	// Create wallet service
	walletService := services.NewWalletService(db, redisClient, log)

	t.Run("Phone Number Uniqueness", func(t *testing.T) {
		phoneNumber := "+1234567890"

		// Create first wallet
		wallet1, err := walletService.CreateWallet(phoneNumber, "individual")
		if err != nil {
			t.Fatalf("Failed to create first wallet: %v", err)
		}
		if wallet1.PhoneNumber != phoneNumber {
			t.Errorf("Expected phone number %s, got %s", phoneNumber, wallet1.PhoneNumber)
		}

		// Try to create second wallet with same phone number
		_, err = walletService.CreateWallet(phoneNumber, "individual")
		if err == nil {
			t.Error("Expected error when creating wallet with duplicate phone number")
		}
	})

	t.Run("Transfer By Phone", func(t *testing.T) {
		// Create two wallets
		senderPhone := "+1111111111"
		receiverPhone := "+2222222222"

		senderWallet, err := walletService.CreateWallet(senderPhone, "individual")
		if err != nil {
			t.Fatalf("Failed to create sender wallet: %v", err)
		}

		_, err = walletService.CreateWallet(receiverPhone, "individual")
		if err != nil {
			t.Fatalf("Failed to create receiver wallet: %v", err)
		}

		// Add some balance to sender wallet (simulate topup)
		initialBalance := 1000.0
		senderWallet.Balance = initialBalance
		db.Save(senderWallet)

		// Test transfer by phone
		transferAmount := 100.0
		transfer, err := walletService.TransferByPhone(senderPhone, receiverPhone, transferAmount, "Test transfer")
		if err != nil {
			t.Fatalf("Failed to transfer by phone: %v", err)
		}

		if transfer.Amount != transferAmount {
			t.Errorf("Expected transfer amount %f, got %f", transferAmount, transfer.Amount)
		}

		// Verify balances
		senderBalance, err := walletService.GetBalanceByPhone(senderPhone)
		if err != nil {
			t.Fatalf("Failed to get sender balance: %v", err)
		}

		receiverBalance, err := walletService.GetBalanceByPhone(receiverPhone)
		if err != nil {
			t.Fatalf("Failed to get receiver balance: %v", err)
		}

		expectedSenderBalance := initialBalance - transferAmount - transfer.Fee
		if senderBalance != expectedSenderBalance {
			t.Errorf("Expected sender balance %f, got %f", expectedSenderBalance, senderBalance)
		}

		if receiverBalance != transferAmount {
			t.Errorf("Expected receiver balance %f, got %f", transferAmount, receiverBalance)
		}
	})

	t.Run("Get Balance By Phone", func(t *testing.T) {
		phoneNumber := "+3333333333"

		wallet, err := walletService.CreateWallet(phoneNumber, "individual")
		if err != nil {
			t.Fatalf("Failed to create wallet: %v", err)
		}

		// Test getting balance by phone
		balance, err := walletService.GetBalanceByPhone(phoneNumber)
		if err != nil {
			t.Fatalf("Failed to get balance by phone: %v", err)
		}

		if balance != wallet.Balance {
			t.Errorf("Expected balance %f, got %f", wallet.Balance, balance)
		}
	})

	t.Run("Topup Wallet By Phone", func(t *testing.T) {
		phoneNumber := "+**********"

		_, err := walletService.CreateWallet(phoneNumber, "individual")
		if err != nil {
			t.Fatalf("Failed to create wallet: %v", err)
		}

		// Test topup by phone
		topupAmount := 500.0
		transaction, err := walletService.TopupWalletByPhone(phoneNumber, topupAmount, "bank_transfer", "REF123")
		if err != nil {
			t.Fatalf("Failed to topup wallet by phone: %v", err)
		}

		if transaction.Amount != topupAmount {
			t.Errorf("Expected topup amount %f, got %f", topupAmount, transaction.Amount)
		}

		// Verify balance after topup
		balance, err := walletService.GetBalanceByPhone(phoneNumber)
		if err != nil {
			t.Fatalf("Failed to get balance after topup: %v", err)
		}

		if balance != topupAmount {
			t.Errorf("Expected balance after topup %f, got %f", topupAmount, balance)
		}
	})

	t.Run("Self Transfer Prevention", func(t *testing.T) {
		phoneNumber := "+**********"

		_, err := walletService.CreateWallet(phoneNumber, "individual")
		if err != nil {
			t.Fatalf("Failed to create wallet: %v", err)
		}

		// Try to transfer to same phone number
		_, err = walletService.TransferByPhone(phoneNumber, phoneNumber, 100.0, "Self transfer")
		if err == nil {
			t.Error("Expected error when transferring to same phone number")
		}
	})

	t.Run("Transfer To Non-Existent Phone", func(t *testing.T) {
		senderPhone := "+6666666666"
		nonExistentPhone := "+9999999999"

		_, err := walletService.CreateWallet(senderPhone, "individual")
		if err != nil {
			t.Fatalf("Failed to create sender wallet: %v", err)
		}

		// Try to transfer to non-existent phone number
		_, err = walletService.TransferByPhone(senderPhone, nonExistentPhone, 100.0, "Transfer to non-existent")
		if err == nil {
			t.Error("Expected error when transferring to non-existent phone number")
		}
	})
}
