package services

import (
	"fmt"
	"time"

	"wallet-platform/internal/clients"
	"wallet-platform/internal/models"
	"wallet-platform/pkg/logger"

	"gorm.io/gorm"
)

// EventService handles event-driven communication
type EventService struct {
	db                  *gorm.DB
	logger              *logger.Logger
	webhookService      *WebhookService
	paymentEngineClient *clients.PaymentEngineClient
}

// NewEventService creates a new event service
func NewEventService(db *gorm.DB, logger *logger.Logger, webhookService *WebhookService, paymentEngineClient *clients.PaymentEngineClient) *EventService {
	return &EventService{
		db:                  db,
		logger:              logger,
		webhookService:      webhookService,
		paymentEngineClient: paymentEngineClient,
	}
}

// Event types
const (
	// Wallet events
	EventWalletCreated        = "wallet.created"
	EventWalletUpdated        = "wallet.updated"
	EventWalletBalanceUpdated = "wallet.balance.updated"
	EventWalletTransfer       = "wallet.transfer.completed"
	EventWalletTopup          = "wallet.topup.completed"

	// PayCard events
	EventPayCardCreated           = "paycard.created"
	EventPayCardUpdated           = "paycard.updated"
	EventPayCardTransaction       = "paycard.transaction.processed"
	EventPayCardPhysicalRequested = "paycard.physical.requested"
	EventPayCardActivated         = "paycard.activated"
	EventPayCardBlocked           = "paycard.blocked"

	// Security events
	EventSecurityAlert    = "security.alert"
	EventFraudDetected    = "fraud.detected"
	EventDeviceRegistered = "device.registered"
	EventLoginAttempt     = "login.attempt"

	// System events
	EventSystemError       = "system.error"
	EventSystemMaintenance = "system.maintenance"
)

// PublishWalletCreated publishes a wallet created event
func (es *EventService) PublishWalletCreated(wallet *models.Wallet) error {
	data := map[string]interface{}{
		"wallet_id":      wallet.ID,
		"phone_number":   wallet.PhoneNumber,
		"account_number": wallet.AccountNumber,
		"wallet_type":    wallet.WalletType,
		"is_verified":    wallet.IsVerified,
		"created_at":     wallet.CreatedAt,
	}

	// Send webhook
	if err := es.webhookService.SendWebhook(EventWalletCreated, fmt.Sprintf("%d", wallet.ID), data); err != nil {
		es.logger.LogError(err, map[string]interface{}{
			"action":    "publish_wallet_created",
			"wallet_id": wallet.ID,
		})
	}

	// Notify payment engine
	if err := es.paymentEngineClient.NotifyPaymentEngine(EventWalletCreated, data); err != nil {
		es.logger.LogError(err, map[string]interface{}{
			"action":    "notify_payment_engine_wallet_created",
			"wallet_id": wallet.ID,
		})
	}

	return nil
}

// PublishWalletBalanceUpdated publishes a wallet balance updated event
func (es *EventService) PublishWalletBalanceUpdated(walletID uint, oldBalance, newBalance float64, transactionType, reference string) error {
	data := map[string]interface{}{
		"wallet_id":        walletID,
		"old_balance":      oldBalance,
		"new_balance":      newBalance,
		"transaction_type": transactionType,
		"reference":        reference,
		"timestamp":        time.Now(),
	}

	// Send webhook
	if err := es.webhookService.SendWebhook(EventWalletBalanceUpdated, fmt.Sprintf("%d", walletID), data); err != nil {
		es.logger.LogError(err, map[string]interface{}{
			"action":    "publish_wallet_balance_updated",
			"wallet_id": walletID,
		})
	}

	// Notify payment engine
	if err := es.paymentEngineClient.NotifyPaymentEngine(EventWalletBalanceUpdated, data); err != nil {
		es.logger.LogError(err, map[string]interface{}{
			"action":    "notify_payment_engine_balance_updated",
			"wallet_id": walletID,
		})
	}

	return nil
}

// PublishWalletTransfer publishes a wallet transfer event
func (es *EventService) PublishWalletTransfer(transfer *models.WalletTransfer) error {
	data := map[string]interface{}{
		"transfer_id":    transfer.ID,
		"from_wallet_id": transfer.FromWalletID,
		"to_wallet_id":   transfer.ToWalletID,
		"amount":         transfer.Amount,
		"fee":            transfer.Fee,
		"total_amount":   transfer.TotalAmount,
		"reference":      transfer.Reference,
		"status":         transfer.Status,
		"description":    transfer.Description,
		"transfer_type":  transfer.TransferType,
		"created_at":     transfer.CreatedAt,
	}

	// Send webhook
	if err := es.webhookService.SendWebhook(EventWalletTransfer, fmt.Sprintf("%d", transfer.ID), data); err != nil {
		es.logger.LogError(err, map[string]interface{}{
			"action":      "publish_wallet_transfer",
			"transfer_id": transfer.ID,
		})
	}

	// Notify payment engine
	if err := es.paymentEngineClient.NotifyPaymentEngine(EventWalletTransfer, data); err != nil {
		es.logger.LogError(err, map[string]interface{}{
			"action":      "notify_payment_engine_transfer",
			"transfer_id": transfer.ID,
		})
	}

	return nil
}

// PublishPayCardCreated publishes a paycard created event
func (es *EventService) PublishPayCardCreated(card *models.PayCard) error {
	data := map[string]interface{}{
		"card_id":          card.ID,
		"wallet_id":        card.WalletID,
		"card_number":      card.CardNumber,
		"card_holder_name": card.CardHolderName,
		"card_type":        card.CardType,
		"spending_limit":   card.SpendingLimit,
		"status":           card.Status,
		"created_at":       card.CreatedAt,
	}

	// Send webhook
	if err := es.webhookService.SendWebhook(EventPayCardCreated, fmt.Sprintf("%d", card.ID), data); err != nil {
		es.logger.LogError(err, map[string]interface{}{
			"action":  "publish_paycard_created",
			"card_id": card.ID,
		})
	}

	// Notify payment engine
	if err := es.paymentEngineClient.NotifyPaymentEngine(EventPayCardCreated, data); err != nil {
		es.logger.LogError(err, map[string]interface{}{
			"action":  "notify_payment_engine_paycard_created",
			"card_id": card.ID,
		})
	}

	return nil
}

// PublishPayCardTransaction publishes a paycard transaction event
func (es *EventService) PublishPayCardTransaction(transaction *models.PayCardTransaction) error {
	data := map[string]interface{}{
		"transaction_id":   transaction.ID,
		"card_id":          transaction.CardID,
		"amount":           transaction.Amount,
		"currency":         transaction.Currency,
		"merchant_name":    transaction.MerchantName,
		"merchant_id":      transaction.MerchantID,
		"transaction_type": transaction.TransactionType,
		"status":           transaction.Status,
		"reference":        transaction.Reference,
		"created_at":       transaction.CreatedAt,
	}

	// Send webhook
	if err := es.webhookService.SendWebhook(EventPayCardTransaction, fmt.Sprintf("%d", transaction.ID), data); err != nil {
		es.logger.LogError(err, map[string]interface{}{
			"action":         "publish_paycard_transaction",
			"transaction_id": transaction.ID,
		})
	}

	// Notify payment engine
	if err := es.paymentEngineClient.NotifyPaymentEngine(EventPayCardTransaction, data); err != nil {
		es.logger.LogError(err, map[string]interface{}{
			"action":         "notify_payment_engine_paycard_transaction",
			"transaction_id": transaction.ID,
		})
	}

	return nil
}

// PublishSecurityAlert publishes a security alert event
func (es *EventService) PublishSecurityAlert(alertType, description string, metadata map[string]interface{}) error {
	data := map[string]interface{}{
		"alert_type":  alertType,
		"description": description,
		"metadata":    metadata,
		"timestamp":   time.Now(),
		"severity":    "high",
	}

	// Send webhook
	if err := es.webhookService.SendWebhook(EventSecurityAlert, alertType, data); err != nil {
		es.logger.LogError(err, map[string]interface{}{
			"action":     "publish_security_alert",
			"alert_type": alertType,
		})
	}

	// Notify payment engine
	if err := es.paymentEngineClient.NotifyPaymentEngine(EventSecurityAlert, data); err != nil {
		es.logger.LogError(err, map[string]interface{}{
			"action":     "notify_payment_engine_security_alert",
			"alert_type": alertType,
		})
	}

	return nil
}

// PublishFraudDetected publishes a fraud detection event
func (es *EventService) PublishFraudDetected(resourceType, resourceID string, riskScore float64, reasons []string) error {
	data := map[string]interface{}{
		"resource_type": resourceType,
		"resource_id":   resourceID,
		"risk_score":    riskScore,
		"reasons":       reasons,
		"timestamp":     time.Now(),
		"action_taken":  "blocked",
	}

	// Send webhook
	if err := es.webhookService.SendWebhook(EventFraudDetected, resourceID, data); err != nil {
		es.logger.LogError(err, map[string]interface{}{
			"action":        "publish_fraud_detected",
			"resource_type": resourceType,
			"resource_id":   resourceID,
		})
	}

	// Notify payment engine
	if err := es.paymentEngineClient.NotifyPaymentEngine(EventFraudDetected, data); err != nil {
		es.logger.LogError(err, map[string]interface{}{
			"action":        "notify_payment_engine_fraud_detected",
			"resource_type": resourceType,
			"resource_id":   resourceID,
		})
	}

	return nil
}

// PublishSystemError publishes a system error event
func (es *EventService) PublishSystemError(errorType, message string, metadata map[string]interface{}) error {
	data := map[string]interface{}{
		"error_type": errorType,
		"message":    message,
		"metadata":   metadata,
		"timestamp":  time.Now(),
		"severity":   "error",
	}

	// Send webhook
	if err := es.webhookService.SendWebhook(EventSystemError, errorType, data); err != nil {
		es.logger.LogError(err, map[string]interface{}{
			"action":     "publish_system_error",
			"error_type": errorType,
		})
	}

	// Notify payment engine
	if err := es.paymentEngineClient.NotifyPaymentEngine(EventSystemError, data); err != nil {
		es.logger.LogError(err, map[string]interface{}{
			"action":     "notify_payment_engine_system_error",
			"error_type": errorType,
		})
	}

	return nil
}
