# Fee Configuration Management

## Overview

The wallet platform uses a flexible fee configuration system that allows administrators to set different fee structures for various environments (development, staging, production) and update fees without code changes.

## Configuration Methods

### **1. Environment Variables**
Fees can be configured using environment variables for maximum flexibility:

```bash
# Card Creation Fees (in SZL)
CARD_CREATION_FEE_STANDARD=25.00
CARD_CREATION_FEE_PREMIUM=50.00
CARD_CREATION_FEE_BUSINESS=100.00

# Transaction Fees (percentages)
WITHDRAWAL_FEE_PERCENTAGE=1.0
TRANSACTION_FEE_PERCENTAGE=0.5
INTERNATIONAL_TRANSACTION_FEE_PERCENTAGE=2.5

# Fixed Fees (in SZL)
MONTHLY_MAINTENANCE_FEE=10.00
CARD_REPLACEMENT_FEE=15.00
ATM_WITHDRAWAL_FEE=5.00

# Card Limits Configuration
# Standard Card Limits
CARD_LIMITS_STANDARD_SPENDING_LIMIT=5000.00
CARD_LIMITS_STANDARD_DAILY_SPENDING_LIMIT=1000.00
CARD_LIMITS_STANDARD_MONTHLY_SPENDING_LIMIT=10000.00
CARD_LIMITS_STANDARD_MAX_CARDS_PER_WALLET=3

# Premium Card Limits
CARD_LIMITS_PREMIUM_SPENDING_LIMIT=15000.00
CARD_LIMITS_PREMIUM_DAILY_SPENDING_LIMIT=3000.00
CARD_LIMITS_PREMIUM_MONTHLY_SPENDING_LIMIT=30000.00
CARD_LIMITS_PREMIUM_MAX_CARDS_PER_WALLET=5

# Business Card Limits
CARD_LIMITS_BUSINESS_SPENDING_LIMIT=50000.00
CARD_LIMITS_BUSINESS_DAILY_SPENDING_LIMIT=10000.00
CARD_LIMITS_BUSINESS_MONTHLY_SPENDING_LIMIT=100000.00
CARD_LIMITS_BUSINESS_MAX_CARDS_PER_WALLET=10

# Card Creation Limits
CARD_CREATION_MAX_CARDS_PER_DAY=3
CARD_CREATION_COOLDOWN_HOURS=24

# Transaction Limits
CARD_TRANSACTION_MIN_AMOUNT=1.00
CARD_TRANSACTION_MAX_AMOUNT_STANDARD=5000.00
CARD_TRANSACTION_MAX_AMOUNT_PREMIUM=15000.00
CARD_TRANSACTION_MAX_AMOUNT_BUSINESS=50000.00

# ATM Withdrawal Limits
CARD_ATM_DAILY_LIMIT_STANDARD=2000.00
CARD_ATM_DAILY_LIMIT_PREMIUM=5000.00
CARD_ATM_DAILY_LIMIT_BUSINESS=10000.00
CARD_ATM_MONTHLY_LIMIT_STANDARD=20000.00
CARD_ATM_MONTHLY_LIMIT_PREMIUM=50000.00
CARD_ATM_MONTHLY_LIMIT_BUSINESS=100000.00
```

### **2. YAML Configuration Files**
Environment-specific configuration files in the `configs/` directory:

#### **Production Fees** (`configs/config.production.yaml`)
```yaml
fees:
  card_creation:
    standard: 25.00    # Standard card creation fee
    premium: 50.00     # Premium card creation fee
    business: 100.00   # Business card creation fee
  withdrawal_percentage: 1.0              # 1% withdrawal fee
  transaction_percentage: 0.5             # 0.5% transaction fee
  monthly_maintenance: 10.00              # Monthly maintenance fee
  card_replacement: 15.00                 # Card replacement fee
  international_transaction_percentage: 2.5  # 2.5% international transaction fee
  atm_withdrawal: 5.00                    # Fixed ATM withdrawal fee
```

#### **Development Fees** (`configs/config.development.yaml`)
```yaml
fees:
  card_creation:
    standard: 5.00     # Reduced for testing
    premium: 10.00     # Reduced for testing
    business: 20.00    # Reduced for testing
  withdrawal_percentage: 0.5             # 0.5% withdrawal fee (reduced)
  transaction_percentage: 0.25           # 0.25% transaction fee (reduced)
  monthly_maintenance: 2.00              # Reduced for testing
  card_replacement: 5.00                 # Reduced for testing
  international_transaction_percentage: 1.0  # 1% international transaction fee (reduced)
  atm_withdrawal: 2.00                   # Reduced for testing
```

## Fee Types

### **Card Creation Fees**
Fixed fees charged when creating new payment cards:
- **Standard Card**: Basic debit card with standard features
- **Premium Card**: Enhanced features and higher limits
- **Business Card**: Corporate cards with business features

### **Transaction Fees**
Percentage-based fees on financial transactions:
- **Withdrawal Fee**: Applied to all withdrawal operations
- **Transaction Fee**: Applied to transfers between wallets
- **International Transaction Fee**: Additional fee for cross-border transactions

### **Service Fees**
Fixed fees for various services:
- **Monthly Maintenance**: Recurring monthly account maintenance
- **Card Replacement**: Fee for replacing lost/damaged cards
- **ATM Withdrawal**: Fixed fee for ATM cash withdrawals

## Implementation Details

### **Configuration Loading**
```go
// Fee configuration is loaded during application startup
type FeesConfig struct {
    CardCreation struct {
        Standard float64 `mapstructure:"standard"`
        Premium  float64 `mapstructure:"premium"`
        Business float64 `mapstructure:"business"`
    } `mapstructure:"card_creation"`
    WithdrawalPercentage               float64 `mapstructure:"withdrawal_percentage"`
    TransactionPercentage              float64 `mapstructure:"transaction_percentage"`
    MonthlyMaintenance                 float64 `mapstructure:"monthly_maintenance"`
    CardReplacement                    float64 `mapstructure:"card_replacement"`
    InternationalTransactionPercentage float64 `mapstructure:"international_transaction_percentage"`
    ATMWithdrawal                      float64 `mapstructure:"atm_withdrawal"`
}
```

### **Fee Calculation Examples**

#### **Card Creation Fee**
```go
func (s *PayCardService) getCardCreationFee(cardType string) float64 {
    switch cardType {
    case "standard":
        return s.config.Fees.CardCreation.Standard
    case "premium":
        return s.config.Fees.CardCreation.Premium
    case "business":
        return s.config.Fees.CardCreation.Business
    default:
        return s.config.Fees.CardCreation.Standard
    }
}
```

#### **Withdrawal Fee**
```go
func (s *WalletService) calculateWithdrawalFee(amount float64) float64 {
    feePercentage := s.config.Fees.WithdrawalPercentage / 100.0
    return amount * feePercentage
}
```

## Configuration Priority

The system uses the following priority order for configuration:

1. **Environment Variables** (highest priority)
2. **Environment-specific YAML files** (e.g., `config.production.yaml`)
3. **Default YAML file** (`config.yaml`)
4. **Hard-coded defaults** (lowest priority)

## Environment-Specific Settings

### **Development Environment**
- **Lower Fees**: Reduced fees for easier testing
- **Flexible Limits**: Higher transaction limits for testing
- **Debug Mode**: Additional logging for fee calculations

### **Production Environment**
- **Full Fees**: Complete fee structure as per business requirements
- **Strict Limits**: Production-appropriate transaction limits
- **Audit Logging**: Comprehensive fee collection logging

### **Staging Environment**
- **Production-like Fees**: Similar to production for realistic testing
- **Enhanced Monitoring**: Additional monitoring for fee collection
- **Test Data**: Safe environment for fee structure testing

## Fee Collection Process

### **1. Fee Calculation**
- Fees calculated based on transaction type and amount
- Configuration values loaded at service initialization
- Fallback to default values if configuration unavailable

### **2. Platform Wallet Collection**
- All fees collected to dedicated platform wallet
- Atomic database transactions ensure consistency
- Separate transaction records for audit trail

### **3. Error Handling**
- Fee collection failures don't prevent main operations
- Comprehensive logging for failed fee collections
- Manual reconciliation procedures for failed collections

## Monitoring & Analytics

### **Fee Collection Metrics**
- Total fees collected by type
- Fee collection success rates
- Revenue analytics by time period
- Failed fee collection tracking

### **Configuration Monitoring**
- Configuration change tracking
- Fee structure version control
- Environment-specific fee comparison
- Real-time fee calculation validation

## Best Practices

### **Fee Configuration Management**
1. **Version Control**: Keep configuration files in version control
2. **Environment Separation**: Use different fee structures per environment
3. **Gradual Rollout**: Test fee changes in staging before production
4. **Documentation**: Document all fee changes with business justification

### **Fee Updates**
1. **Planned Updates**: Schedule fee changes during maintenance windows
2. **Communication**: Notify users of fee changes in advance
3. **Rollback Plan**: Maintain ability to quickly revert fee changes
4. **Monitoring**: Monitor fee collection after changes

### **Security Considerations**
1. **Access Control**: Restrict fee configuration access to authorized personnel
2. **Audit Trail**: Log all fee configuration changes
3. **Validation**: Validate fee values before applying
4. **Backup**: Maintain backups of fee configurations

## Troubleshooting

### **Common Issues**

#### **Configuration Not Loading**
- Check environment variable names and values
- Verify YAML file syntax and structure
- Ensure configuration files are in correct directory
- Check application logs for configuration errors

#### **Incorrect Fee Calculations**
- Verify percentage values are correct (1.0 = 1%, not 100%)
- Check for configuration caching issues
- Validate fee calculation logic in code
- Review transaction logs for fee amounts

#### **Fee Collection Failures**
- Check platform wallet existence and status
- Verify database transaction handling
- Review error logs for specific failure reasons
- Check wallet balance sufficiency for fee collection

### **Debugging Commands**

#### **Check Current Configuration**
```bash
# View current fee configuration
curl -H "X-Admin-Key: your_admin_key" \
     http://localhost:8086/api/v1/platform/wallet
```

#### **Validate Configuration File**
```bash
# Validate YAML syntax
go run scripts/validate-config.go configs/config.production.yaml
```

#### **Test Fee Calculations**
```bash
# Test fee calculation with specific amounts
go run scripts/test-fees.go --amount=100 --type=withdrawal
```

## Future Enhancements

### **Planned Features**
1. **Dynamic Fee Updates**: Update fees without application restart
2. **Fee Scheduling**: Time-based fee adjustments
3. **User-Specific Fees**: Different fee structures for different user types
4. **Fee Promotions**: Temporary fee reductions and promotions
5. **Multi-Currency Fees**: Different fee structures per currency

### **API Enhancements**
1. **Fee Management API**: RESTful API for fee configuration
2. **Fee Preview API**: Preview fees before transaction execution
3. **Fee History API**: Historical fee structure tracking
4. **Fee Analytics API**: Detailed fee collection analytics

## Support

For fee configuration support:
1. Check this documentation first
2. Review application logs for configuration errors
3. Test configuration changes in development environment
4. Contact system administrators for production changes
