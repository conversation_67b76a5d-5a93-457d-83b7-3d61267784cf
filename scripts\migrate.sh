#!/bin/bash

# Wallet Platform Migration Script
# This script provides easy access to migration operations

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
OPERATION=""
CONFIG_PATH="config/config.yaml"
SOURCE_DB=""
TARGET_DB=""
DRY_RUN=false
VERBOSE=false

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Wallet Platform Migration Script"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Operations:"
    echo "  migrate              - Perform complete migration"
    echo "  migrate-wallets      - Migrate only wallet data"
    echo "  migrate-cards        - Migrate only paycard data"
    echo "  migrate-transactions - Migrate only transaction data"
    echo "  validate             - Validate migrated data"
    echo "  rollback             - Rollback migration (destructive)"
    echo "  status               - Show migration status"
    echo ""
    echo "Options:"
    echo "  -o, --operation OPERATION    Migration operation to perform"
    echo "  -c, --config PATH           Path to configuration file (default: config/config.yaml)"
    echo "  -s, --source DSN            Source database connection string"
    echo "  -t, --target DSN            Target database connection string"
    echo "  -d, --dry-run               Perform dry run without making changes"
    echo "  -v, --verbose               Enable verbose logging"
    echo "  -h, --help                  Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 -o migrate                                    # Complete migration"
    echo "  $0 -o migrate-wallets -d                         # Dry run wallet migration"
    echo "  $0 -o migrate -s 'user:pass@tcp(host:3306)/db'   # Custom source DB"
    echo "  $0 -o validate -v                                # Verbose validation"
    echo ""
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -o|--operation)
            OPERATION="$2"
            shift 2
            ;;
        -c|--config)
            CONFIG_PATH="$2"
            shift 2
            ;;
        -s|--source)
            SOURCE_DB="$2"
            shift 2
            ;;
        -t|--target)
            TARGET_DB="$2"
            shift 2
            ;;
        -d|--dry-run)
            DRY_RUN=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate operation
if [[ -z "$OPERATION" ]]; then
    print_error "Operation is required"
    show_usage
    exit 1
fi

# Valid operations
VALID_OPERATIONS=("migrate" "migrate-wallets" "migrate-cards" "migrate-transactions" "validate" "rollback" "status")
if [[ ! " ${VALID_OPERATIONS[@]} " =~ " ${OPERATION} " ]]; then
    print_error "Invalid operation: $OPERATION"
    print_info "Valid operations: ${VALID_OPERATIONS[*]}"
    exit 1
fi

# Check if Go is installed
if ! command -v go &> /dev/null; then
    print_error "Go is not installed or not in PATH"
    exit 1
fi

# Check if config file exists
if [[ ! -f "$CONFIG_PATH" ]]; then
    print_warning "Configuration file not found: $CONFIG_PATH"
    print_info "Using default configuration"
fi

# Build migration command
CMD="go run scripts/migrate.go -operation=$OPERATION"

if [[ -n "$CONFIG_PATH" ]]; then
    CMD="$CMD -config=$CONFIG_PATH"
fi

if [[ -n "$SOURCE_DB" ]]; then
    CMD="$CMD -source='$SOURCE_DB'"
fi

if [[ -n "$TARGET_DB" ]]; then
    CMD="$CMD -target='$TARGET_DB'"
fi

if [[ "$DRY_RUN" == true ]]; then
    CMD="$CMD -dry-run"
fi

if [[ "$VERBOSE" == true ]]; then
    CMD="$CMD -verbose"
fi

# Show operation details
print_info "Migration Operation: $OPERATION"
if [[ "$DRY_RUN" == true ]]; then
    print_warning "DRY RUN MODE - No changes will be made"
fi
if [[ "$VERBOSE" == true ]]; then
    print_info "Verbose logging enabled"
fi

# Confirmation for destructive operations
if [[ "$OPERATION" == "rollback" && "$DRY_RUN" != true ]]; then
    print_warning "DESTRUCTIVE OPERATION: This will delete all migrated data"
    read -p "Are you sure you want to proceed? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "Operation cancelled"
        exit 0
    fi
fi

# Pre-migration checks
print_info "Performing pre-migration checks..."

# Check database connectivity (if custom connections provided)
if [[ -n "$SOURCE_DB" ]]; then
    print_info "Testing source database connection..."
    # Add database connectivity test here if needed
fi

if [[ -n "$TARGET_DB" ]]; then
    print_info "Testing target database connection..."
    # Add database connectivity test here if needed
fi

# Execute migration
print_info "Executing migration command..."
print_info "Command: $CMD"
echo ""

# Change to wallet-platform directory
cd "$(dirname "$0")/.."

# Execute the command
if eval "$CMD"; then
    print_success "Migration operation completed successfully"
    
    # Post-migration recommendations
    case $OPERATION in
        migrate|migrate-*)
            if [[ "$DRY_RUN" != true ]]; then
                print_info "Recommended next steps:"
                print_info "1. Run validation: $0 -o validate"
                print_info "2. Check migration status: $0 -o status"
                print_info "3. Test application functionality"
            fi
            ;;
        validate)
            print_info "If validation passed, your migration is complete!"
            ;;
    esac
else
    print_error "Migration operation failed"
    print_info "Check the logs above for error details"
    print_info "You may need to run rollback if data was partially migrated"
    exit 1
fi
