package models

import (
	"time"

	"gorm.io/datatypes"
)

// SecurityEvent represents a security-related event
type SecurityEvent struct {
	ID          uint    `gorm:"primaryKey" json:"id"`
	WalletID    *uint   `gorm:"index" json:"wallet_id"`
	Wallet      *Wallet `gorm:"constraint:OnDelete:CASCADE" json:"wallet"`
	EventType   string  `gorm:"type:varchar(50);index;not null" json:"event_type"`
	Severity    string  `gorm:"type:varchar(20);index;not null" json:"severity"` // low, medium, high, critical
	Description string  `gorm:"type:text;not null" json:"description"`

	// Request details
	IPAddress string `gorm:"type:varchar(45);index" json:"ip_address"`
	UserAgent string `gorm:"type:text" json:"user_agent"`
	Endpoint  string `gorm:"type:varchar(255)" json:"endpoint"`
	Method    string `gorm:"type:varchar(10)" json:"method"`

	// Device and location
	DeviceFingerprint string `gorm:"type:varchar(64);index" json:"device_fingerprint"`
	Location          string `gorm:"type:varchar(255)" json:"location"`

	// Event metadata
	Metadata datatypes.JSON `gorm:"type:json" json:"metadata"`
	Reason   string         `gorm:"type:text" json:"reason"`

	// Resolution
	Resolved   bool       `gorm:"default:false" json:"resolved"`
	ResolvedBy *uint      `json:"resolved_by"`
	ResolvedAt *time.Time `json:"resolved_at"`
	Resolution string     `gorm:"type:text" json:"resolution"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// FraudAlert represents a fraud detection alert
type FraudAlert struct {
	ID                uint   `gorm:"primaryKey" json:"id"`
	WalletID          uint   `gorm:"index;not null" json:"wallet_id"`
	Wallet            Wallet `gorm:"constraint:OnDelete:CASCADE" json:"wallet"`
	TransactionID     *uint  `gorm:"index" json:"transaction_id,omitempty"`
	CardTransactionID *uint  `gorm:"index" json:"card_transaction_id,omitempty"`

	// Risk assessment
	RiskLevel  string  `gorm:"type:varchar(20);index;not null" json:"risk_level"` // low, medium, high, critical
	RiskScore  float64 `gorm:"type:decimal(5,2);not null" json:"risk_score"`
	Confidence float64 `gorm:"type:decimal(5,2);default:0" json:"confidence"`

	// Alert details
	AlertType      string         `gorm:"type:varchar(50);not null" json:"alert_type"`
	Reasons        datatypes.JSON `gorm:"type:json;not null" json:"reasons"`
	Recommendation string         `gorm:"type:varchar(50);not null" json:"recommendation"` // allow, block, review, require_2fa

	// Context information
	DeviceFingerprint string `gorm:"type:varchar(64);index" json:"device_fingerprint"`
	IPAddress         string `gorm:"type:varchar(45);index" json:"ip_address"`
	Location          string `gorm:"type:varchar(255)" json:"location"`
	UserAgent         string `gorm:"type:text" json:"user_agent"`

	// Processing status
	Status      string     `gorm:"type:varchar(20);default:'pending';index" json:"status"` // pending, reviewed, resolved, false_positive
	ReviewedBy  *uint      `json:"reviewed_by,omitempty"`
	ReviewedAt  *time.Time `json:"reviewed_at,omitempty"`
	ReviewNotes string     `gorm:"type:text" json:"review_notes"`

	// Actions taken
	ActionTaken    string         `gorm:"type:varchar(50)" json:"action_taken"`
	ActionMetadata datatypes.JSON `json:"action_metadata"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// DeviceRegistration represents registered devices for users
type DeviceRegistration struct {
	ID                uint   `gorm:"primaryKey" json:"id"`
	WalletID          uint   `gorm:"index;not null" json:"wallet_id"`
	Wallet            Wallet `gorm:"constraint:OnDelete:CASCADE" json:"wallet"`
	DeviceFingerprint string `gorm:"type:varchar(64);index;not null" json:"device_fingerprint"`
	DeviceID          string `gorm:"type:varchar(255);index" json:"device_id"`
	DeviceName        string `gorm:"type:varchar(100)" json:"device_name"`
	DeviceType        string `gorm:"type:varchar(50)" json:"device_type"` // mobile, desktop, tablet

	// Device details
	OSName     string `gorm:"type:varchar(50)" json:"os_name"`
	OSVersion  string `gorm:"type:varchar(50)" json:"os_version"`
	AppVersion string `gorm:"type:varchar(50)" json:"app_version"`
	UserAgent  string `gorm:"type:text" json:"user_agent"`

	// Network and location
	IPAddress string `gorm:"type:varchar(45)" json:"ip_address"`
	Location  string `gorm:"type:varchar(255)" json:"location"`

	// Trust and security
	IsTrusted          bool       `gorm:"default:false" json:"is_trusted"`
	TrustLevel         string     `gorm:"type:varchar(20);default:'unknown'" json:"trust_level"` // unknown, low, medium, high
	VerificationCode   string     `gorm:"type:varchar(10)" json:"verification_code"`
	VerificationExpiry *time.Time `json:"verification_expiry"`
	IsVerified         bool       `gorm:"default:false" json:"is_verified"`

	// Usage tracking
	LastUsed   *time.Time `json:"last_used"`
	UsageCount int        `gorm:"default:0" json:"usage_count"`

	// Status
	Status   string         `gorm:"type:varchar(20);default:'active'" json:"status"` // active, suspended, revoked
	Metadata datatypes.JSON `json:"metadata"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// TwoFactorAuth represents 2FA settings for users
type TwoFactorAuth struct {
	ID        uint   `gorm:"primaryKey" json:"id"`
	WalletID  uint   `gorm:"uniqueIndex;not null" json:"wallet_id"`
	Wallet    Wallet `gorm:"constraint:OnDelete:CASCADE" json:"wallet"`
	IsEnabled bool   `gorm:"default:false" json:"is_enabled"`
	Method    string `gorm:"type:varchar(20);not null" json:"method"` // sms, email, totp, push

	// TOTP specific
	Secret      string         `gorm:"type:varchar(255)" json:"secret,omitempty"` // For TOTP
	BackupCodes datatypes.JSON `gorm:"type:json" json:"backup_codes,omitempty"`

	// SMS/Email specific
	PhoneNumber string `gorm:"type:varchar(20)" json:"phone_number,omitempty"`
	Email       string `gorm:"type:varchar(100)" json:"email,omitempty"`

	// Usage tracking
	LastUsed   *time.Time `json:"last_used"`
	UsageCount int        `gorm:"default:0" json:"usage_count"`

	// Recovery
	RecoveryEnabled bool           `gorm:"default:true" json:"recovery_enabled"`
	RecoveryMethod  string         `gorm:"type:varchar(20)" json:"recovery_method"`
	RecoveryData    datatypes.JSON `json:"recovery_data"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// TransactionFlag represents flagged transactions for review
type TransactionFlag struct {
	ID                uint   `gorm:"primaryKey" json:"id"`
	TransactionID     *uint  `gorm:"index" json:"transaction_id"`
	CardTransactionID *uint  `gorm:"index" json:"card_transaction_id"`
	WalletID          uint   `gorm:"index;not null" json:"wallet_id"`
	Wallet            Wallet `gorm:"constraint:OnDelete:CASCADE" json:"wallet"`

	// Flag details
	FlagType string `gorm:"type:varchar(50);not null" json:"flag_type"` // suspicious, high_risk, manual_review, compliance
	Severity string `gorm:"type:varchar(20);not null" json:"severity"`  // low, medium, high, critical
	Reason   string `gorm:"type:text;not null" json:"reason"`

	// Flagging information
	FlaggedBy     *uint  `json:"flagged_by"`                                               // Admin user ID or system
	FlaggedByType string `gorm:"type:varchar(20);default:'system'" json:"flagged_by_type"` // system, admin, user
	AutoFlag      bool   `gorm:"default:true" json:"auto_flag"`

	// Processing status
	Status     string     `gorm:"type:varchar(20);default:'active';index" json:"status"` // active, resolved, dismissed, escalated
	Priority   string     `gorm:"type:varchar(20);default:'medium'" json:"priority"`     // low, medium, high, urgent
	ResolvedBy *uint      `json:"resolved_by,omitempty"`
	ResolvedAt *time.Time `json:"resolved_at,omitempty"`
	Resolution string     `gorm:"type:text" json:"resolution"`

	// Additional context
	Notes    string         `gorm:"type:text" json:"notes"`
	Metadata datatypes.JSON `json:"metadata"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// SecuritySettings represents user security preferences
type SecuritySettings struct {
	ID       uint   `gorm:"primaryKey" json:"id"`
	WalletID uint   `gorm:"uniqueIndex;not null" json:"wallet_id"`
	Wallet   Wallet `gorm:"constraint:OnDelete:CASCADE" json:"wallet"`

	// Authentication settings
	RequireDeviceAuth     bool `gorm:"default:false" json:"require_device_auth"`
	Require2FAForTransfer bool `gorm:"default:false" json:"require_2fa_for_transfer"`
	Require2FAForCards    bool `gorm:"default:false" json:"require_2fa_for_cards"`
	SessionTimeout        int  `gorm:"default:30" json:"session_timeout"` // minutes

	// Notification preferences
	TransferNotifications        bool `gorm:"default:true" json:"transfer_notifications"`
	LoginNotifications           bool `gorm:"default:true" json:"login_notifications"`
	SuspiciousActivity           bool `gorm:"default:true" json:"suspicious_activity"`
	CardTransactionNotifications bool `gorm:"default:true" json:"card_transaction_notifications"`

	// Transaction limits and controls
	MaxDailyTransfers    int     `gorm:"default:10" json:"max_daily_transfers"`
	MaxDailyAmount       float64 `gorm:"type:decimal(15,2);default:5000" json:"max_daily_amount"`
	RequireApprovalAbove float64 `gorm:"type:decimal(15,2);default:1000" json:"require_approval_above"`

	// Geographic and time restrictions
	AllowedCountries datatypes.JSON `json:"allowed_countries"`
	BlockedCountries datatypes.JSON `json:"blocked_countries"`
	AllowedTimeZones datatypes.JSON `json:"allowed_time_zones"`
	QuietHoursStart  string         `gorm:"type:varchar(5)" json:"quiet_hours_start"` // HH:MM format
	QuietHoursEnd    string         `gorm:"type:varchar(5)" json:"quiet_hours_end"`   // HH:MM format

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// SecurityAuditLog represents security audit trail
type SecurityAuditLog struct {
	ID       uint    `gorm:"primaryKey" json:"id"`
	WalletID *uint   `gorm:"index" json:"wallet_id"`
	Wallet   *Wallet `gorm:"constraint:OnDelete:CASCADE" json:"wallet"`

	// Action details
	Action     string `gorm:"type:varchar(100);not null" json:"action"`
	Resource   string `gorm:"type:varchar(100);not null" json:"resource"`
	ResourceID *uint  `json:"resource_id,omitempty"`

	// Change tracking
	OldValue datatypes.JSON `gorm:"type:json" json:"old_value,omitempty"`
	NewValue datatypes.JSON `gorm:"type:json" json:"new_value,omitempty"`

	// Request context
	IPAddress         string `gorm:"type:varchar(45);not null" json:"ip_address"`
	UserAgent         string `gorm:"type:text" json:"user_agent"`
	DeviceFingerprint string `gorm:"type:varchar(64)" json:"device_fingerprint"`

	// Result
	Success       bool   `gorm:"default:true" json:"success"`
	FailureReason string `gorm:"type:text" json:"failure_reason,omitempty"`

	// Additional context
	Metadata datatypes.JSON `json:"metadata"`

	CreatedAt time.Time `json:"created_at"`
}

// RiskProfile represents a user's risk profile for fraud detection
type RiskProfile struct {
	ID       uint   `gorm:"primaryKey" json:"id"`
	WalletID uint   `gorm:"uniqueIndex;not null" json:"wallet_id"`
	Wallet   Wallet `gorm:"constraint:OnDelete:CASCADE" json:"wallet"`

	// Risk scoring
	RiskScore          float64   `gorm:"type:decimal(5,2);default:0" json:"risk_score"`
	RiskLevel          string    `gorm:"type:varchar(20);default:'low'" json:"risk_level"` // low, medium, high, critical
	LastRiskAssessment time.Time `json:"last_risk_assessment"`

	// Transaction patterns
	TransactionCount         int     `gorm:"default:0" json:"transaction_count"`
	AverageTransactionAmount float64 `gorm:"type:decimal(15,2);default:0" json:"average_transaction_amount"`
	MaxTransactionAmount     float64 `gorm:"type:decimal(15,2);default:0" json:"max_transaction_amount"`

	// Behavioral patterns
	TypicalTransactionHours datatypes.JSON `json:"typical_transaction_hours"`
	TypicalLocations        datatypes.JSON `json:"typical_locations"`
	TypicalDevices          datatypes.JSON `json:"typical_devices"`

	// Security incidents
	SuspiciousActivityCount int        `gorm:"default:0" json:"suspicious_activity_count"`
	FraudAlertCount         int        `gorm:"default:0" json:"fraud_alert_count"`
	LastIncidentDate        *time.Time `json:"last_incident_date"`

	// Risk factors
	RiskFactors       datatypes.JSON `json:"risk_factors"`
	ProtectiveFactors datatypes.JSON `json:"protective_factors"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// SecurityRequest represents common security-related requests
type SecurityRequest struct {
	WalletID   uint   `json:"wallet_id" binding:"required"`
	DeviceID   string `json:"device_id"`
	DeviceName string `json:"device_name"`
	DeviceType string `json:"device_type"`
	OSName     string `json:"os_name"`
	OSVersion  string `json:"os_version"`
	AppVersion string `json:"app_version"`
}

// DeviceVerificationRequest represents a device verification request
type DeviceVerificationRequest struct {
	DeviceFingerprint string `json:"device_fingerprint" binding:"required"`
	VerificationCode  string `json:"verification_code" binding:"required"`
}

// TwoFactorSetupRequest represents a 2FA setup request
type TwoFactorSetupRequest struct {
	Method      string `json:"method" binding:"required,oneof=sms email totp push"`
	PhoneNumber string `json:"phone_number"`
	Email       string `json:"email"`
}

// TwoFactorVerifyRequest represents a 2FA verification request
type TwoFactorVerifyRequest struct {
	Code   string `json:"code" binding:"required"`
	Method string `json:"method" binding:"required"`
}

// SecuritySettingsUpdateRequest represents a security settings update request
type SecuritySettingsUpdateRequest struct {
	RequireDeviceAuth     *bool    `json:"require_device_auth"`
	Require2FAForTransfer *bool    `json:"require_2fa_for_transfer"`
	Require2FAForCards    *bool    `json:"require_2fa_for_cards"`
	SessionTimeout        *int     `json:"session_timeout"`
	TransferNotifications *bool    `json:"transfer_notifications"`
	LoginNotifications    *bool    `json:"login_notifications"`
	SuspiciousActivity    *bool    `json:"suspicious_activity"`
	MaxDailyTransfers     *int     `json:"max_daily_transfers"`
	MaxDailyAmount        *float64 `json:"max_daily_amount"`
	RequireApprovalAbove  *float64 `json:"require_approval_above"`
}
