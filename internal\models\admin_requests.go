package models

// Admin-specific request and response models

// UserListRequest represents request for listing users
type UserListRequest struct {
	Page     int    `form:"page" binding:"min=1"`
	Limit    int    `form:"limit" binding:"min=1,max=100"`
	Status   string `form:"status"`
	Search   string `form:"search"`
	KYCLevel string `form:"kyc_level"`
	SortBy   string `form:"sort_by"`
	SortDir  string `form:"sort_dir" binding:"oneof=asc desc"`
}

// WalletListRequest represents request for listing wallets
type WalletListRequest struct {
	Page       int    `form:"page" binding:"min=1"`
	Limit      int    `form:"limit" binding:"min=1,max=100"`
	Status     string `form:"status"`
	WalletType string `form:"wallet_type"`
	KYCLevel   string `form:"kyc_level"`
	SortBy     string `form:"sort_by"`
	SortDir    string `form:"sort_dir" binding:"oneof=asc desc"`
}

// ServiceListRequest represents request for listing services
type ServiceListRequest struct {
	Page     int    `form:"page" binding:"min=1"`
	Limit    int    `form:"limit" binding:"min=1,max=100"`
	Category string `form:"category"`
	Provider string `form:"provider"`
	IsActive *bool  `form:"is_active"`
	IsPublic *bool  `form:"is_public"`
	SortBy   string `form:"sort_by"`
	SortDir  string `form:"sort_dir" binding:"oneof=asc desc"`
}

// ReportListRequest represents request for listing reports
type ReportListRequest struct {
	Page       int    `form:"page" binding:"min=1"`
	Limit      int    `form:"limit" binding:"min=1,max=100"`
	ReportType string `form:"type"`
	Status     string `form:"status"`
	StartDate  string `form:"start_date"`
	EndDate    string `form:"end_date"`
	SortBy     string `form:"sort_by"`
	SortDir    string `form:"sort_dir" binding:"oneof=asc desc"`
}

// AdminAnalyticsRequest represents request for analytics data (admin-specific)
type AdminAnalyticsRequest struct {
	Period    string                 `form:"period" binding:"required,oneof=daily weekly monthly quarterly yearly"`
	StartDate string                 `form:"start_date"`
	EndDate   string                 `form:"end_date"`
	Filters   map[string]interface{} `form:"filters"`
	GroupBy   string                 `form:"group_by"`
}

// ReportGenerationRequest represents request for generating reports
type ReportGenerationRequest struct {
	ReportType string                 `json:"report_type" binding:"required,oneof=revenue users transactions services security"`
	Period     string                 `json:"period" binding:"required,oneof=daily weekly monthly quarterly yearly"`
	StartDate  string                 `json:"start_date"`
	EndDate    string                 `json:"end_date"`
	Filters    map[string]interface{} `json:"filters"`
	Format     string                 `json:"format" binding:"oneof=json csv pdf"`
	Email      string                 `json:"email"`
}

// SystemConfigUpdateRequest represents system configuration update
type SystemConfigUpdateRequest struct {
	MaintenanceMode   *bool                  `json:"maintenance_mode"`
	MaxDailyTransfers *int                   `json:"max_daily_transfers"`
	MaxTransferAmount *float64               `json:"max_transfer_amount"`
	KYCRequired       *bool                  `json:"kyc_required"`
	Settings          map[string]interface{} `json:"settings"`
}

// ReportResponse represents report data in admin responses
type ReportResponse struct {
	ID          uint                   `json:"id"`
	Type        string                 `json:"type"`
	Period      string                 `json:"period"`
	Status      string                 `json:"status"`
	Format      string                 `json:"format"`
	FileURL     string                 `json:"file_url,omitempty"`
	Filters     map[string]interface{} `json:"filters"`
	CreatedAt   string                 `json:"created_at"`
	CompletedAt string                 `json:"completed_at,omitempty"`
	CreatedBy   uint                   `json:"created_by"`
}

// AdminAnalyticsResponse represents analytics data response (admin-specific)
type AdminAnalyticsResponse struct {
	Period    string                 `json:"period"`
	StartDate string                 `json:"start_date"`
	EndDate   string                 `json:"end_date"`
	Data      map[string]interface{} `json:"data"`
	Summary   map[string]interface{} `json:"summary"`
}

// SystemStatusResponse represents system status response
type SystemStatusResponse struct {
	MaintenanceMode   bool                   `json:"maintenance_mode"`
	SystemHealth      string                 `json:"system_health"`
	ActiveUsers       int                    `json:"active_users"`
	TotalTransactions int64                  `json:"total_transactions"`
	TotalRevenue      float64                `json:"total_revenue"`
	Settings          map[string]interface{} `json:"settings"`
	LastUpdated       string                 `json:"last_updated"`
}

// BulkActionRequest represents bulk action request
type BulkActionRequest struct {
	Action string      `json:"action" binding:"required"`
	IDs    []uint      `json:"ids" binding:"required,min=1"`
	Data   interface{} `json:"data"`
}

// BulkActionResponse represents bulk action response
type BulkActionResponse struct {
	Success   bool                   `json:"success"`
	Message   string                 `json:"message"`
	Processed int                    `json:"processed"`
	Failed    int                    `json:"failed"`
	Results   []BulkActionResult     `json:"results"`
	Errors    map[string]interface{} `json:"errors,omitempty"`
}

// BulkActionResult represents individual bulk action result
type BulkActionResult struct {
	ID      uint   `json:"id"`
	Success bool   `json:"success"`
	Error   string `json:"error,omitempty"`
}
