package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"wallet-platform/internal/config"
	"wallet-platform/internal/database"
	"wallet-platform/internal/middleware"

	"wallet-platform/internal/routes"
	"wallet-platform/internal/services"
	"wallet-platform/pkg/logger"
	"wallet-platform/pkg/redis"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// @title Wallet Platform API
// @version 1.0
// @description Standalone Wallet and PayCard Platform API
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>

// @license.name MIT
// @license.url https://opensource.org/licenses/MIT

// @host localhost:8086
// @BasePath /api/v1

// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description Type "Bearer" followed by a space and JWT token.

func main() {
	fmt.Println("=== APPLICATION STARTUP ===")
	fmt.Println("Step 1: Loading configuration...")

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		fmt.Printf("FATAL: Failed to load configuration: %v\n", err)
		log.Fatalf("Failed to load configuration: %v", err)
	}
	fmt.Println("✓ Configuration loaded successfully")

	// Initialize logger
	fmt.Println("Step 2: Initializing logger...")
	logger := logger.NewLogger(cfg.Log.Level, cfg.Log.Format)
	if logger == nil {
		fmt.Println("FATAL: Logger initialization returned nil")
		log.Fatal("Logger initialization failed")
	}
	fmt.Println("✓ Logger initialized successfully")
	logger.Info("Starting Wallet Platform API Server")

	// Initialize database (if enabled)
	var db *gorm.DB
	fmt.Printf("MAIN DEBUG: Database enabled: %v\n", cfg.Database.Enabled)

	if cfg.Database.Enabled {
		fmt.Printf("MAIN DEBUG: Initializing database connection\n")
		db, err = database.Initialize(cfg.Database)
		if err != nil {
			fmt.Printf("MAIN ERROR: Database initialization failed: %v\n", err)
			logger.Fatalf("Failed to initialize database: %v", err)
		}
		fmt.Printf("MAIN DEBUG: Database connection established successfully\n")
		logger.Info("Database connection established")
	} else {
		fmt.Printf("MAIN DEBUG: Database disabled, running in mock mode\n")
		logger.Info("Database disabled in configuration - running in mock mode")
		// For local development without database
		db = nil
	}

	// Initialize Redis (optional)
	fmt.Println("Step 3: Initializing Redis...")
	var redisClient *redis.Client
	if cfg.Redis.Enabled {
		fmt.Printf("MAIN DEBUG: Redis enabled, attempting connection\n")
		redisClient, err = redis.NewClient(cfg.Redis)
		if err != nil {
			fmt.Printf("MAIN WARNING: Redis connection failed: %v\n", err)
			logger.Warnf("Failed to initialize Redis: %v. Rate limiting will be disabled.", err)
			redisClient = nil
		} else {
			fmt.Printf("MAIN DEBUG: Redis connection established\n")
			logger.Info("Redis connection established")
		}
	} else {
		fmt.Printf("MAIN DEBUG: Redis disabled in configuration\n")
		logger.Info("Redis disabled in configuration")
		redisClient = nil
	}
	fmt.Printf("MAIN DEBUG: Redis client is nil: %v\n", redisClient == nil)

	// Initialize services
	fmt.Println("Step 4: Creating service container...")
	fmt.Printf("MAIN DEBUG: Creating service container\n")
	serviceContainer := services.NewContainer(db, redisClient, cfg, logger)
	if serviceContainer == nil {
		fmt.Printf("MAIN ERROR: Service container creation returned nil\n")
		logger.Fatal("Failed to create service container")
	}
	fmt.Printf("MAIN DEBUG: Service container created\n")

	// Check if WalletService was created properly
	if serviceContainer.WalletService == nil {
		fmt.Printf("MAIN WARNING: WalletService is nil in container\n")
	} else {
		fmt.Printf("MAIN DEBUG: WalletService is available in container\n")
	}

	fmt.Println("✓ Services initialized successfully")
	logger.Info("Services initialized")

	// Set Gin mode
	fmt.Println("Step 5: Setting up Gin router...")
	if cfg.Server.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
		fmt.Println("✓ Gin set to release mode")
	} else {
		fmt.Println("✓ Gin running in debug mode")
	}

	// Initialize Gin router
	router := gin.New()
	if router == nil {
		fmt.Println("FATAL: Gin router creation failed")
		logger.Fatal("Gin router creation failed")
	}
	fmt.Println("✓ Gin router created")

	// Add middleware
	router.Use(middleware.Logger(logger))
	router.Use(middleware.Recovery(logger))
	router.Use(middleware.CORS(cfg.Server.CORS))
	router.Use(middleware.Security())
	router.Use(middleware.RateLimit(redisClient, cfg.RateLimit))

	// Health check endpoint
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "healthy",
			"timestamp": time.Now().UTC(),
			"version":   cfg.App.Version,
		})
	})

	// Setup API routes
	fmt.Println("Step 6: Setting up API routes...")
	routes.SetupRoutes(router, serviceContainer)
	fmt.Println("✓ API routes configured successfully")

	// Create HTTP server
	server := &http.Server{
		Addr:         fmt.Sprintf(":%d", cfg.Server.Port),
		Handler:      router,
		ReadTimeout:  time.Duration(cfg.Server.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(cfg.Server.WriteTimeout) * time.Second,
		IdleTimeout:  time.Duration(cfg.Server.IdleTimeout) * time.Second,
	}

	// Start server in a goroutine
	fmt.Printf("Step 7: Starting server on port %d...\n", cfg.Server.Port)
	fmt.Println("=== APPLICATION STARTUP COMPLETE ===")
	go func() {
		logger.Infof("Server starting on port %d", cfg.Server.Port)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			fmt.Printf("FATAL: Server failed to start: %v\n", err)
			logger.Fatalf("Failed to start server: %v", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("Shutting down server...")

	// Create a deadline for shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Shutdown server
	if err := server.Shutdown(ctx); err != nil {
		logger.Errorf("Server forced to shutdown: %v", err)
	}

	// Close database connection
	sqlDB, err := db.DB()
	if err == nil {
		sqlDB.Close()
	}

	// Close Redis connection
	redisClient.Close()

	logger.Info("Server exited")
}
