# Production Deployment Guide

## ⚠️ CRITICAL WARNING

**DO NOT DEPLOY TO PRODUCTION WITHOUT ADDRESSING CRITICAL SECURITY ISSUES**

This platform has identified critical security vulnerabilities that MUST be resolved before production deployment. See [Security Documentation](SECURITY.md) and [Code Quality Issues](CODE_QUALITY_ISSUES.md) for details.

## Current Production Readiness: 45/100

### 🚨 Blocking Issues for Production
1. **Admin Authentication Disabled** - No admin functionality available
2. **2FA Not Implemented** - Security bypass vulnerability
3. **PIN Management Incomplete** - Card security vulnerability
4. **Input Validation Gaps** - Potential injection vulnerabilities

## Pre-Production Checklist

### Security Requirements (CRITICAL)
- [ ] Implement proper admin role-based access control
- [ ] Complete 2FA implementation with TOTP/SMS verification
- [ ] Implement secure PIN hashing and verification
- [ ] Integrate comprehensive input validation across all endpoints
- [ ] Generate secure random values for all production secrets
- [ ] Conduct security penetration testing
- [ ] Complete security code review

### Infrastructure Requirements
- [ ] Production database with SSL/TLS encryption
- [ ] Redis cluster for high availability
- [ ] Load balancer with SSL termination
- [ ] Monitoring and alerting system
- [ ] Log aggregation and analysis
- [ ] Backup and disaster recovery plan
- [ ] SSL certificates for HTTPS

### Configuration Requirements
- [ ] Environment-specific configuration files
- [ ] Secure secret management (HashiCorp Vault, AWS Secrets Manager, etc.)
- [ ] Database connection pooling and optimization
- [ ] Rate limiting configuration
- [ ] CORS policy configuration
- [ ] Logging configuration for production

## Environment Setup

### 1. Infrastructure Requirements

#### Minimum Production Requirements
- **CPU**: 4 cores
- **RAM**: 8GB
- **Storage**: 100GB SSD
- **Network**: 1Gbps
- **OS**: Ubuntu 20.04 LTS or CentOS 8

#### Database Requirements
- **MySQL**: 8.0+ with InnoDB engine
- **Storage**: 500GB+ with automated backups
- **Connections**: 100+ concurrent connections
- **SSL**: Required for production

#### Redis Requirements
- **Version**: 6.0+
- **Memory**: 4GB+
- **Persistence**: RDB + AOF enabled
- **Clustering**: Recommended for high availability

### 2. Security Configuration

#### Generate Production Secrets
```bash
# JWT Secret (256-bit)
openssl rand -base64 32

# Encryption Key (128-bit hex)
openssl rand -hex 16

# Hash Salt (64-bit hex)
openssl rand -hex 8

# Internal API Key (256-bit)
openssl rand -base64 32

# Webhook Secret
openssl rand -base64 32
```

#### Environment Variables
Copy `.env.production.example` to `.env.production` and update with secure values:

```bash
# CRITICAL: Replace ALL placeholder values with secure random values
cp .env.production.example .env.production

# Set environment
export APP_ENVIRONMENT=production
```

### 3. Database Setup

#### Create Production Database
```sql
-- Create database
CREATE DATABASE wallet_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create user with limited privileges
CREATE USER 'wallet_prod_user'@'%' IDENTIFIED BY 'SECURE_RANDOM_PASSWORD';
GRANT SELECT, INSERT, UPDATE, DELETE ON wallet_platform.* TO 'wallet_prod_user'@'%';
FLUSH PRIVILEGES;

-- Enable SSL (required for production)
-- Configure my.cnf with SSL settings
```

#### Run Migrations
```bash
# Set production environment
export APP_ENVIRONMENT=production

# Run database migrations
go run cmd/migrate/main.go
```

### 4. SSL/TLS Configuration

#### Obtain SSL Certificates
```bash
# Using Let's Encrypt (recommended)
certbot certonly --standalone -d your-api-domain.com

# Or use your organization's certificates
```

#### Configure Load Balancer
```nginx
# Nginx configuration example
server {
    listen 443 ssl http2;
    server_name your-api-domain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";
    
    location / {
        proxy_pass http://localhost:8086;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## Deployment Methods

### 1. Docker Deployment (Recommended)

#### Build Production Image
```bash
# Build optimized production image
docker build -t wallet-platform:production -f Dockerfile.production .
```

#### Docker Compose Production
```yaml
# docker-compose.production.yml
version: '3.8'
services:
  wallet-platform:
    image: wallet-platform:production
    environment:
      - APP_ENVIRONMENT=production
    env_file:
      - .env.production
    ports:
      - "8086:8086"
    depends_on:
      - mysql
      - redis
    restart: unless-stopped
    
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_DATABASE: wallet_platform
      MYSQL_USER: wallet_prod_user
      MYSQL_PASSWORD: ${DATABASE_PASSWORD}
      MYSQL_ROOT_PASSWORD: ${DATABASE_ROOT_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/conf.d:/etc/mysql/conf.d
    restart: unless-stopped
    
  redis:
    image: redis:6-alpine
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:
```

#### Deploy with Docker Compose
```bash
docker-compose -f docker-compose.production.yml up -d
```

### 2. Kubernetes Deployment

#### Create Kubernetes Manifests
```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: wallet-platform
spec:
  replicas: 3
  selector:
    matchLabels:
      app: wallet-platform
  template:
    metadata:
      labels:
        app: wallet-platform
    spec:
      containers:
      - name: wallet-platform
        image: wallet-platform:production
        ports:
        - containerPort: 8086
        env:
        - name: APP_ENVIRONMENT
          value: "production"
        envFrom:
        - secretRef:
            name: wallet-platform-secrets
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
```

#### Deploy to Kubernetes
```bash
# Create secrets
kubectl create secret generic wallet-platform-secrets --from-env-file=.env.production

# Deploy application
kubectl apply -f k8s/
```

## Monitoring and Observability

### 1. Health Checks
```bash
# Application health
curl https://your-api-domain.com/health

# Database connectivity
curl https://your-api-domain.com/health/db

# Redis connectivity
curl https://your-api-domain.com/health/redis
```

### 2. Metrics Collection
```bash
# Prometheus metrics
curl https://your-api-domain.com/metrics
```

### 3. Log Aggregation
Configure log shipping to your preferred log aggregation service:
- ELK Stack (Elasticsearch, Logstash, Kibana)
- Splunk
- AWS CloudWatch
- Google Cloud Logging

## Security Hardening

### 1. Network Security
- Configure firewall rules to restrict access
- Use VPC/private networks for database connections
- Implement DDoS protection
- Enable intrusion detection

### 2. Application Security
- Enable rate limiting
- Configure CORS policies
- Implement request size limits
- Enable security headers

### 3. Database Security
- Use SSL/TLS for all database connections
- Implement database firewall rules
- Enable audit logging
- Regular security updates

## Backup and Disaster Recovery

### 1. Database Backups
```bash
# Automated daily backups
mysqldump --single-transaction --routines --triggers \
  -h $DB_HOST -u $DB_USER -p$DB_PASSWORD wallet_platform \
  | gzip > backup_$(date +%Y%m%d_%H%M%S).sql.gz
```

### 2. Redis Backups
```bash
# Redis backup
redis-cli --rdb backup_$(date +%Y%m%d_%H%M%S).rdb
```

### 3. Application Backups
- Configuration files
- SSL certificates
- Application logs
- Monitoring data

## Performance Optimization

### 1. Database Optimization
- Implement proper indexing
- Configure connection pooling
- Enable query caching
- Regular performance monitoring

### 2. Application Optimization
- Enable Redis caching
- Implement response compression
- Configure connection timeouts
- Monitor memory usage

### 3. Load Balancing
- Implement horizontal scaling
- Configure session affinity if needed
- Monitor response times
- Implement circuit breakers

## Troubleshooting

### Common Issues
1. **Database Connection Errors**: Check SSL configuration and credentials
2. **Redis Connection Errors**: Verify password and network connectivity
3. **Authentication Failures**: Verify JWT secret configuration
4. **Rate Limiting Issues**: Check rate limit configuration
5. **SSL Certificate Errors**: Verify certificate validity and configuration

### Log Analysis
```bash
# Check application logs
docker logs wallet-platform

# Check system logs
journalctl -u wallet-platform

# Check database logs
tail -f /var/log/mysql/error.log
```

## Support and Maintenance

### Regular Maintenance Tasks
- [ ] Security updates and patches
- [ ] Database maintenance and optimization
- [ ] Log rotation and cleanup
- [ ] Certificate renewal
- [ ] Backup verification
- [ ] Performance monitoring
- [ ] Security audits

### Emergency Procedures
- Incident response plan
- Rollback procedures
- Emergency contacts
- Escalation procedures

## Conclusion

**IMPORTANT**: This deployment guide assumes all critical security issues have been resolved. Do not deploy to production until:

1. All security vulnerabilities are fixed
2. Comprehensive testing is completed
3. Security audit is performed
4. Monitoring and alerting are configured
5. Backup and disaster recovery plans are tested

For support, contact the development team or refer to the troubleshooting documentation.
