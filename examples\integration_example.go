package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"time"
)

// Example demonstrating how the payment engine communicates with the wallet platform

// PaymentEngineClient simulates the payment engine making API calls to wallet platform
type PaymentEngineClient struct {
	baseURL string
	apiKey  string
	client  *http.Client
}

func NewPaymentEngineClient(baseURL, apiKey string) *PaymentEngineClient {
	return &PaymentEngineClient{
		baseURL: baseURL,
		apiKey:  apiKey,
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// TopupWallet demonstrates how payment engine tops up a wallet after successful payment
func (c *PaymentEngineClient) TopupWallet(phoneNumber string, amount float64, reference string) error {
	payload := map[string]interface{}{
		"amount":         amount,
		"payment_method": "mobile_money",
		"provider_code":  "MTN_MOMO",
		"reference":      reference,
		"metadata": map[string]interface{}{
			"source":      "payment_engine",
			"provider":    "MTN",
			"external_id": reference,
		},
	}

	return c.makeRequest("POST", fmt.Sprintf("/api/v1/wallets/%s/topup", phoneNumber), payload)
}

// CreateWallet demonstrates how payment engine creates a wallet
func (c *PaymentEngineClient) CreateWallet(phoneNumber, walletType string) error {
	payload := map[string]interface{}{
		"phone_number": phoneNumber,
		"wallet_type":  walletType,
		"is_verified":  true,
		"metadata": map[string]interface{}{
			"created_by": "payment_engine",
			"source":     "automatic_creation",
		},
	}

	return c.makeRequest("POST", "/api/v1/wallets", payload)
}

// ProcessCardPayment demonstrates how payment engine processes a card payment
func (c *PaymentEngineClient) ProcessCardPayment(cardNumber string, amount float64, merchantInfo map[string]interface{}) error {
	payload := map[string]interface{}{
		"card_number":   cardNumber,
		"amount":        amount,
		"merchant_info": merchantInfo,
		"metadata": map[string]interface{}{
			"processed_by": "payment_engine",
			"timestamp":    time.Now(),
		},
	}

	return c.makeRequest("POST", "/api/v1/cards/contactless/pay", payload)
}

// SendWebhook demonstrates how payment engine sends webhooks to wallet platform
func (c *PaymentEngineClient) SendWebhook(eventType string, data map[string]interface{}) error {
	payload := map[string]interface{}{
		"id":          time.Now().Unix(),
		"event_type":  eventType,
		"resource_id": fmt.Sprintf("%v", data["resource_id"]),
		"data":        data,
		"timestamp":   time.Now(),
	}

	return c.makeRequest("POST", "/api/v1/webhooks/receive", payload)
}

func (c *PaymentEngineClient) makeRequest(method, endpoint string, payload interface{}) error {
	url := c.baseURL + endpoint
	
	jsonData, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("failed to marshal payload: %w", err)
	}

	req, err := http.NewRequest(method, url, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-API-Key", c.apiKey)
	req.Header.Set("Accept", "application/vnd.walletplatform.v1+json")

	resp, err := c.client.Do(req)
	if err != nil {
		return fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return fmt.Errorf("request failed with status %d", resp.StatusCode)
	}

	return nil
}

// WalletPlatformWebhookHandler simulates how wallet platform sends webhooks to payment engine
type WalletPlatformWebhookHandler struct {
	paymentEngineURL string
	apiKey           string
	client           *http.Client
}

func NewWalletPlatformWebhookHandler(paymentEngineURL, apiKey string) *WalletPlatformWebhookHandler {
	return &WalletPlatformWebhookHandler{
		paymentEngineURL: paymentEngineURL,
		apiKey:           apiKey,
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// NotifyWalletCreated sends wallet creation notification to payment engine
func (h *WalletPlatformWebhookHandler) NotifyWalletCreated(walletID uint, phoneNumber string) error {
	payload := map[string]interface{}{
		"event_type":  "wallet.created",
		"resource_id": fmt.Sprintf("%d", walletID),
		"data": map[string]interface{}{
			"wallet_id":    walletID,
			"phone_number": phoneNumber,
			"status":       "active",
			"created_at":   time.Now(),
		},
		"timestamp": time.Now(),
		"source":    "wallet_platform",
	}

	return h.sendWebhook(payload)
}

// NotifyTransactionCompleted sends transaction completion notification
func (h *WalletPlatformWebhookHandler) NotifyTransactionCompleted(transactionID, transactionType string, amount float64) error {
	payload := map[string]interface{}{
		"event_type":  "transaction.completed",
		"resource_id": transactionID,
		"data": map[string]interface{}{
			"transaction_id":   transactionID,
			"transaction_type": transactionType,
			"amount":           amount,
			"status":           "completed",
			"completed_at":     time.Now(),
		},
		"timestamp": time.Now(),
		"source":    "wallet_platform",
	}

	return h.sendWebhook(payload)
}

func (h *WalletPlatformWebhookHandler) sendWebhook(payload interface{}) error {
	url := h.paymentEngineURL + "/api/v1/webhooks/receive"
	
	jsonData, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("failed to marshal webhook payload: %w", err)
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create webhook request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-API-Key", h.apiKey)
	req.Header.Set("X-Webhook-Source", "wallet_platform")

	resp, err := h.client.Do(req)
	if err != nil {
		return fmt.Errorf("webhook request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return fmt.Errorf("webhook failed with status %d", resp.StatusCode)
	}

	return nil
}

// Example usage demonstrating the communication flow
func main() {
	// Initialize clients
	paymentEngineClient := NewPaymentEngineClient("http://localhost:8081", "payment_engine_api_key")
	walletWebhookHandler := NewWalletPlatformWebhookHandler("http://localhost:8080", "wallet_platform_api_key")

	// Example 1: Payment Engine creates a wallet
	fmt.Println("Example 1: Payment Engine creates wallet...")
	if err := paymentEngineClient.CreateWallet("71234567", "individual"); err != nil {
		log.Printf("Failed to create wallet: %v", err)
	} else {
		fmt.Println("✓ Wallet created successfully")
	}

	// Example 2: Payment Engine tops up wallet after successful payment
	fmt.Println("\nExample 2: Payment Engine tops up wallet...")
	if err := paymentEngineClient.TopupWallet("71234567", 100.0, "TXN123456"); err != nil {
		log.Printf("Failed to topup wallet: %v", err)
	} else {
		fmt.Println("✓ Wallet topped up successfully")
	}

	// Example 3: Payment Engine sends webhook notification
	fmt.Println("\nExample 3: Payment Engine sends webhook...")
	webhookData := map[string]interface{}{
		"resource_id": "payment_123",
		"amount":      100.0,
		"status":      "completed",
	}
	if err := paymentEngineClient.SendWebhook("payment.completed", webhookData); err != nil {
		log.Printf("Failed to send webhook: %v", err)
	} else {
		fmt.Println("✓ Webhook sent successfully")
	}

	// Example 4: Wallet Platform notifies Payment Engine
	fmt.Println("\nExample 4: Wallet Platform sends notification...")
	if err := walletWebhookHandler.NotifyWalletCreated(123, "71234567"); err != nil {
		log.Printf("Failed to notify payment engine: %v", err)
	} else {
		fmt.Println("✓ Payment Engine notified successfully")
	}

	// Example 5: Process card payment
	fmt.Println("\nExample 5: Process card payment...")
	merchantInfo := map[string]interface{}{
		"merchant_name": "Test Store",
		"merchant_id":   "MERCHANT123",
		"location":      "Kampala, Uganda",
	}
	if err := paymentEngineClient.ProcessCardPayment("1234567890123456", 50.0, merchantInfo); err != nil {
		log.Printf("Failed to process card payment: %v", err)
	} else {
		fmt.Println("✓ Card payment processed successfully")
	}

	fmt.Println("\n🎉 Integration examples completed!")
	fmt.Println("\nThis demonstrates the communication patterns between:")
	fmt.Println("- Payment Engine → Wallet Platform (API calls)")
	fmt.Println("- Wallet Platform → Payment Engine (webhooks)")
	fmt.Println("- Bidirectional event-driven communication")
}
