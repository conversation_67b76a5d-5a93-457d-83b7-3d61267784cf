package services

import (
	"encoding/json"
	"fmt"
	"math"
	"strings"
	"sync"
	"time"

	"wallet-platform/internal/models"
	"wallet-platform/pkg/logger"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// FraudDetectionService handles advanced fraud detection
type FraudDetectionService struct {
	db     *gorm.DB
	logger *logger.Logger

	// In-memory caches for performance
	deviceCache  map[string]*DeviceProfile
	ipCache      map[string]*IPProfile
	userPatterns map[uint]*UserBehaviorPattern
	mu           sync.RWMutex

	// Configuration
	config *FraudConfig
}

// FraudConfig holds fraud detection configuration
type FraudConfig struct {
	VelocityThreshold       int     `json:"velocity_threshold"`        // Max transactions per minute
	AmountAnomalyMultiplier float64 `json:"amount_anomaly_multiplier"` // Standard deviation multiplier
	TimeAnomalyThreshold    float64 `json:"time_anomaly_threshold"`    // Unusual time threshold
	GeographicRiskThreshold float64 `json:"geographic_risk_threshold"` // Geographic risk threshold
	DeviceRiskThreshold     float64 `json:"device_risk_threshold"`     // Device risk threshold

	// Risk score thresholds
	LowRiskThreshold      float64 `json:"low_risk_threshold"`
	MediumRiskThreshold   float64 `json:"medium_risk_threshold"`
	HighRiskThreshold     float64 `json:"high_risk_threshold"`
	CriticalRiskThreshold float64 `json:"critical_risk_threshold"`
}

// DeviceProfile represents device behavior profile
type DeviceProfile struct {
	Fingerprint      string    `json:"fingerprint"`
	FirstSeen        time.Time `json:"first_seen"`
	LastSeen         time.Time `json:"last_seen"`
	TransactionCount int       `json:"transaction_count"`
	TotalAmount      float64   `json:"total_amount"`
	Locations        []string  `json:"locations"`
	UserAgents       []string  `json:"user_agents"`
	IsTrusted        bool      `json:"is_trusted"`
	RiskScore        float64   `json:"risk_score"`
}

// IPProfile represents IP address behavior profile
type IPProfile struct {
	IPAddress        string    `json:"ip_address"`
	FirstSeen        time.Time `json:"first_seen"`
	LastSeen         time.Time `json:"last_seen"`
	TransactionCount int       `json:"transaction_count"`
	UniqueDevices    int       `json:"unique_devices"`
	Location         string    `json:"location"`
	ISP              string    `json:"isp"`
	IsVPN            bool      `json:"is_vpn"`
	IsTor            bool      `json:"is_tor"`
	RiskScore        float64   `json:"risk_score"`
}

// UserBehaviorPattern represents user transaction patterns
type UserBehaviorPattern struct {
	WalletID                uint      `json:"wallet_id"`
	TypicalTransactionHours []int     `json:"typical_transaction_hours"`
	TypicalDays             []int     `json:"typical_days"`
	AverageAmount           float64   `json:"average_amount"`
	StandardDeviation       float64   `json:"standard_deviation"`
	MaxAmount               float64   `json:"max_amount"`
	TypicalLocations        []string  `json:"typical_locations"`
	TypicalDevices          []string  `json:"typical_devices"`
	LastTransactionTime     time.Time `json:"last_transaction_time"`
	TransactionVelocity     float64   `json:"transaction_velocity"`
	SuspiciousActivityCount int       `json:"suspicious_activity_count"`
}

// FraudAnalysisResult represents the result of fraud analysis
type FraudAnalysisResult struct {
	RiskLevel      string                 `json:"risk_level"`
	RiskScore      float64                `json:"risk_score"`
	Confidence     float64                `json:"confidence"`
	Reasons        []string               `json:"reasons"`
	Recommendation string                 `json:"recommendation"`
	Metadata       map[string]interface{} `json:"metadata"`
	RequireAction  bool                   `json:"require_action"`
}

// NewFraudDetectionService creates a new fraud detection service
func NewFraudDetectionService(db *gorm.DB, log *logger.Logger) *FraudDetectionService {
	config := &FraudConfig{
		VelocityThreshold:       5,   // 5 transactions per minute
		AmountAnomalyMultiplier: 3.0, // 3 standard deviations
		TimeAnomalyThreshold:    0.1, // 10% of usual activity
		GeographicRiskThreshold: 5.0, // Geographic risk threshold
		DeviceRiskThreshold:     7.0, // Device risk threshold
		LowRiskThreshold:        20.0,
		MediumRiskThreshold:     40.0,
		HighRiskThreshold:       70.0,
		CriticalRiskThreshold:   90.0,
	}

	return &FraudDetectionService{
		db:           db,
		logger:       log,
		deviceCache:  make(map[string]*DeviceProfile),
		ipCache:      make(map[string]*IPProfile),
		userPatterns: make(map[uint]*UserBehaviorPattern),
		config:       config,
	}
}

// AnalyzeTransaction performs comprehensive fraud analysis on a transaction
func (f *FraudDetectionService) AnalyzeTransaction(walletID uint, amount float64, transactionData map[string]interface{}) (*FraudAnalysisResult, error) {
	result := &FraudAnalysisResult{
		RiskLevel:      "LOW",
		RiskScore:      0.0,
		Confidence:     0.0,
		Reasons:        []string{},
		Recommendation: "ALLOW",
		Metadata:       make(map[string]interface{}),
		RequireAction:  false,
	}

	// Extract transaction context
	deviceFingerprint := f.extractString(transactionData, "device_fingerprint")
	ipAddress := f.extractString(transactionData, "ip_address")
	location := f.extractString(transactionData, "location")
	_ = f.extractString(transactionData, "user_agent")       // userAgent - not used yet
	_ = f.extractString(transactionData, "transaction_type") // transactionType - not used yet

	// Get or create user behavior pattern
	pattern, err := f.getUserBehaviorPattern(walletID)
	if err != nil {
		f.logger.LogError(err, map[string]interface{}{
			"action":    "get_user_pattern",
			"wallet_id": walletID,
		})
		// Continue with default pattern
		pattern = &UserBehaviorPattern{WalletID: walletID}
	}

	// Perform fraud checks
	checks := []struct {
		name   string
		weight float64
		check  func() (float64, []string)
	}{
		{"velocity", 25.0, func() (float64, []string) { return f.checkVelocity(walletID, pattern) }},
		{"amount_anomaly", 30.0, func() (float64, []string) { return f.checkAmountAnomaly(amount, pattern) }},
		{"time_pattern", 15.0, func() (float64, []string) { return f.checkTimePattern(pattern) }},
		{"geographic", 20.0, func() (float64, []string) { return f.checkGeographicAnomaly(location, pattern) }},
		{"device", 10.0, func() (float64, []string) { return f.checkDeviceAnomaly(deviceFingerprint, ipAddress, walletID) }},
	}

	totalWeight := 0.0
	for _, check := range checks {
		score, reasons := check.check()
		weightedScore := score * check.weight / 100.0
		result.RiskScore += weightedScore
		totalWeight += check.weight

		if score > 0 {
			result.Reasons = append(result.Reasons, reasons...)
			result.Metadata[check.name+"_score"] = score
		}
	}

	// Normalize confidence based on available data
	result.Confidence = f.calculateConfidence(pattern, deviceFingerprint, ipAddress)

	// Determine risk level and recommendation
	f.determineRiskLevelAndRecommendation(result)

	// Update user pattern
	f.updateUserPattern(walletID, amount, location, deviceFingerprint, time.Now())

	// Log fraud analysis
	f.logger.LogSecurity("fraud_analysis", fmt.Sprintf("%d", walletID), ipAddress,
		fmt.Sprintf("Risk: %s (%.2f), Recommendation: %s", result.RiskLevel, result.RiskScore, result.Recommendation))

	// Create fraud alert if necessary
	if result.RiskScore >= f.config.MediumRiskThreshold {
		if err := f.createFraudAlert(walletID, result, transactionData); err != nil {
			f.logger.LogError(err, map[string]interface{}{
				"action":    "create_fraud_alert",
				"wallet_id": walletID,
			})
		}
	}

	return result, nil
}

// checkVelocity checks transaction velocity
func (f *FraudDetectionService) checkVelocity(walletID uint, pattern *UserBehaviorPattern) (float64, []string) {
	// Check recent transaction count
	var recentCount int64
	oneMinuteAgo := time.Now().Add(-time.Minute)

	if err := f.db.Model(&models.WalletTransaction{}).
		Where("wallet_id = ? AND created_at > ?", walletID, oneMinuteAgo).
		Count(&recentCount).Error; err != nil {
		return 0.0, nil
	}

	if recentCount > int64(f.config.VelocityThreshold) {
		return 8.0, []string{fmt.Sprintf("High transaction velocity: %d transactions in last minute", recentCount)}
	}

	// Check against user's typical velocity
	if pattern.TransactionVelocity > 0 && float64(recentCount) > pattern.TransactionVelocity*2 {
		return 5.0, []string{"Transaction velocity above user's typical pattern"}
	}

	return 0.0, nil
}

// checkAmountAnomaly checks for unusual transaction amounts
func (f *FraudDetectionService) checkAmountAnomaly(amount float64, pattern *UserBehaviorPattern) (float64, []string) {
	if pattern.AverageAmount == 0 || pattern.StandardDeviation == 0 {
		// Not enough data for pattern analysis
		if amount > 10000 { // High amount threshold
			return 3.0, []string{"Large transaction amount with insufficient user history"}
		}
		return 0.0, nil
	}

	// Check if amount is significantly different from user's pattern
	deviations := math.Abs(amount-pattern.AverageAmount) / pattern.StandardDeviation

	if deviations > f.config.AmountAnomalyMultiplier {
		return 7.0, []string{fmt.Sprintf("Amount anomaly: %.2f standard deviations from average", deviations)}
	}

	// Check if amount exceeds user's historical maximum by significant margin
	if amount > pattern.MaxAmount*2 {
		return 5.0, []string{"Amount significantly exceeds user's historical maximum"}
	}

	return 0.0, nil
}

// checkTimePattern checks for unusual transaction timing
func (f *FraudDetectionService) checkTimePattern(pattern *UserBehaviorPattern) (float64, []string) {
	now := time.Now()
	currentHour := now.Hour()
	currentDay := int(now.Weekday())

	// Check if current hour is typical for user
	isTypicalHour := false
	for _, hour := range pattern.TypicalTransactionHours {
		if hour == currentHour {
			isTypicalHour = true
			break
		}
	}

	// Check if current day is typical for user
	isTypicalDay := false
	for _, day := range pattern.TypicalDays {
		if day == currentDay {
			isTypicalDay = true
			break
		}
	}

	reasons := []string{}
	score := 0.0

	if !isTypicalHour && len(pattern.TypicalTransactionHours) > 0 {
		score += 3.0
		reasons = append(reasons, "Transaction at unusual hour for user")
	}

	if !isTypicalDay && len(pattern.TypicalDays) > 0 {
		score += 2.0
		reasons = append(reasons, "Transaction on unusual day for user")
	}

	// Check for very late night transactions (2 AM - 6 AM)
	if currentHour >= 2 && currentHour <= 6 {
		score += 2.0
		reasons = append(reasons, "Late night transaction")
	}

	return score, reasons
}

// checkGeographicAnomaly checks for unusual transaction locations
func (f *FraudDetectionService) checkGeographicAnomaly(location string, pattern *UserBehaviorPattern) (float64, []string) {
	if location == "" {
		return 1.0, []string{"Unknown transaction location"}
	}

	// Check against user's typical locations
	isTypicalLocation := false
	for _, typicalLoc := range pattern.TypicalLocations {
		if strings.Contains(location, typicalLoc) || strings.Contains(typicalLoc, location) {
			isTypicalLocation = true
			break
		}
	}

	if !isTypicalLocation && len(pattern.TypicalLocations) > 0 {
		return 6.0, []string{"Transaction from unusual location for user"}
	}

	// Check against high-risk countries/regions
	highRiskRegions := []string{"Unknown", "VPN", "Tor", "Proxy"}
	for _, riskRegion := range highRiskRegions {
		if strings.Contains(location, riskRegion) {
			return 8.0, []string{fmt.Sprintf("Transaction from high-risk location: %s", riskRegion)}
		}
	}

	return 0.0, nil
}

// checkDeviceAnomaly checks for suspicious device patterns
func (f *FraudDetectionService) checkDeviceAnomaly(deviceFingerprint, ipAddress string, walletID uint) (float64, []string) {
	f.mu.RLock()
	defer f.mu.RUnlock()

	reasons := []string{}
	score := 0.0

	// Check device profile
	if deviceProfile, exists := f.deviceCache[deviceFingerprint]; exists {
		if deviceProfile.RiskScore > f.config.DeviceRiskThreshold {
			score += 7.0
			reasons = append(reasons, "High-risk device")
		}

		// Check if device is new to this user
		var deviceReg models.DeviceRegistration
		if err := f.db.Where("wallet_id = ? AND device_fingerprint = ?", walletID, deviceFingerprint).First(&deviceReg).Error; err != nil {
			score += 4.0
			reasons = append(reasons, "New device for user")
		}
	} else {
		score += 3.0
		reasons = append(reasons, "Unknown device")
	}

	// Check IP profile
	if ipProfile, exists := f.ipCache[ipAddress]; exists {
		if ipProfile.RiskScore > f.config.GeographicRiskThreshold {
			score += 5.0
			reasons = append(reasons, "High-risk IP address")
		}

		if ipProfile.IsVPN {
			score += 4.0
			reasons = append(reasons, "VPN detected")
		}

		if ipProfile.IsTor {
			score += 8.0
			reasons = append(reasons, "Tor network detected")
		}
	}

	return score, reasons
}

// calculateConfidence calculates confidence level based on available data
func (f *FraudDetectionService) calculateConfidence(pattern *UserBehaviorPattern, deviceFingerprint, ipAddress string) float64 {
	confidence := 0.0

	// Base confidence from user history
	if pattern.TransactionVelocity > 0 {
		confidence += 30.0
	}
	if len(pattern.TypicalLocations) > 0 {
		confidence += 25.0
	}
	if len(pattern.TypicalDevices) > 0 {
		confidence += 20.0
	}

	// Device and IP data availability
	f.mu.RLock()
	if _, exists := f.deviceCache[deviceFingerprint]; exists {
		confidence += 15.0
	}
	if _, exists := f.ipCache[ipAddress]; exists {
		confidence += 10.0
	}
	f.mu.RUnlock()

	return math.Min(confidence, 100.0)
}

// determineRiskLevelAndRecommendation sets risk level and recommendation
func (f *FraudDetectionService) determineRiskLevelAndRecommendation(result *FraudAnalysisResult) {
	if result.RiskScore >= f.config.CriticalRiskThreshold {
		result.RiskLevel = "CRITICAL"
		result.Recommendation = "BLOCK"
		result.RequireAction = true
	} else if result.RiskScore >= f.config.HighRiskThreshold {
		result.RiskLevel = "HIGH"
		result.Recommendation = "REVIEW"
		result.RequireAction = true
	} else if result.RiskScore >= f.config.MediumRiskThreshold {
		result.RiskLevel = "MEDIUM"
		result.Recommendation = "REQUIRE_2FA"
		result.RequireAction = true
	} else if result.RiskScore >= f.config.LowRiskThreshold {
		result.RiskLevel = "LOW"
		result.Recommendation = "MONITOR"
		result.RequireAction = false
	} else {
		result.RiskLevel = "MINIMAL"
		result.Recommendation = "ALLOW"
		result.RequireAction = false
	}
}

// getUserBehaviorPattern gets or creates user behavior pattern
func (f *FraudDetectionService) getUserBehaviorPattern(walletID uint) (*UserBehaviorPattern, error) {
	f.mu.RLock()
	if pattern, exists := f.userPatterns[walletID]; exists {
		f.mu.RUnlock()
		return pattern, nil
	}
	f.mu.RUnlock()

	// Load from database
	var profile models.RiskProfile
	if err := f.db.Where("wallet_id = ?", walletID).First(&profile).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			// Create new pattern
			pattern := &UserBehaviorPattern{
				WalletID:                walletID,
				TypicalTransactionHours: []int{},
				TypicalDays:             []int{},
				TypicalLocations:        []string{},
				TypicalDevices:          []string{},
			}

			f.mu.Lock()
			f.userPatterns[walletID] = pattern
			f.mu.Unlock()

			return pattern, nil
		}
		return nil, err
	}

	// Convert from database model
	pattern := &UserBehaviorPattern{
		WalletID:                profile.WalletID,
		AverageAmount:           profile.AverageTransactionAmount,
		MaxAmount:               profile.MaxTransactionAmount,
		LastTransactionTime:     profile.UpdatedAt, // Use UpdatedAt as last transaction time
		SuspiciousActivityCount: profile.SuspiciousActivityCount,
	}

	// Parse JSON fields
	if profile.TypicalTransactionHours != nil {
		json.Unmarshal(profile.TypicalTransactionHours, &pattern.TypicalTransactionHours)
	}
	if profile.TypicalLocations != nil {
		json.Unmarshal(profile.TypicalLocations, &pattern.TypicalLocations)
	}
	if profile.TypicalDevices != nil {
		json.Unmarshal(profile.TypicalDevices, &pattern.TypicalDevices)
	}

	f.mu.Lock()
	f.userPatterns[walletID] = pattern
	f.mu.Unlock()

	return pattern, nil
}

// updateUserPattern updates user behavior pattern with new transaction data
func (f *FraudDetectionService) updateUserPattern(walletID uint, amount float64, location, deviceFingerprint string, transactionTime time.Time) {
	f.mu.Lock()
	defer f.mu.Unlock()

	pattern, exists := f.userPatterns[walletID]
	if !exists {
		return
	}

	// Update transaction statistics
	pattern.LastTransactionTime = transactionTime

	// Update typical hours
	hour := transactionTime.Hour()
	if !f.containsInt(pattern.TypicalTransactionHours, hour) {
		pattern.TypicalTransactionHours = append(pattern.TypicalTransactionHours, hour)
	}

	// Update typical days
	day := int(transactionTime.Weekday())
	if !f.containsInt(pattern.TypicalDays, day) {
		pattern.TypicalDays = append(pattern.TypicalDays, day)
	}

	// Update typical locations
	if location != "" && !f.containsString(pattern.TypicalLocations, location) {
		pattern.TypicalLocations = append(pattern.TypicalLocations, location)
		// Keep only last 10 locations
		if len(pattern.TypicalLocations) > 10 {
			pattern.TypicalLocations = pattern.TypicalLocations[1:]
		}
	}

	// Update typical devices
	if deviceFingerprint != "" && !f.containsString(pattern.TypicalDevices, deviceFingerprint) {
		pattern.TypicalDevices = append(pattern.TypicalDevices, deviceFingerprint)
		// Keep only last 5 devices
		if len(pattern.TypicalDevices) > 5 {
			pattern.TypicalDevices = pattern.TypicalDevices[1:]
		}
	}

	// Update amount statistics
	if pattern.AverageAmount == 0 {
		pattern.AverageAmount = amount
		pattern.MaxAmount = amount
	} else {
		// Simple moving average (in production, use more sophisticated calculation)
		pattern.AverageAmount = (pattern.AverageAmount + amount) / 2
		if amount > pattern.MaxAmount {
			pattern.MaxAmount = amount
		}
	}

	// Save to database asynchronously
	go f.saveUserPattern(pattern)
}

// createFraudAlert creates a fraud alert in the database
func (f *FraudDetectionService) createFraudAlert(walletID uint, result *FraudAnalysisResult, transactionData map[string]interface{}) error {
	reasonsJSON, _ := json.Marshal(result.Reasons)

	alert := models.FraudAlert{
		WalletID:          walletID,
		RiskLevel:         result.RiskLevel,
		RiskScore:         result.RiskScore,
		Confidence:        result.Confidence,
		AlertType:         "transaction_analysis",
		Reasons:           datatypes.JSON(reasonsJSON),
		Recommendation:    result.Recommendation,
		DeviceFingerprint: f.extractString(transactionData, "device_fingerprint"),
		IPAddress:         f.extractString(transactionData, "ip_address"),
		Location:          f.extractString(transactionData, "location"),
		UserAgent:         f.extractString(transactionData, "user_agent"),
		Status:            "pending",
	}

	return f.db.Create(&alert).Error
}

// Helper methods
func (f *FraudDetectionService) extractString(data map[string]interface{}, key string) string {
	if value, exists := data[key]; exists {
		if str, ok := value.(string); ok {
			return str
		}
	}
	return ""
}

func (f *FraudDetectionService) containsInt(slice []int, item int) bool {
	for _, v := range slice {
		if v == item {
			return true
		}
	}
	return false
}

func (f *FraudDetectionService) containsString(slice []string, item string) bool {
	for _, v := range slice {
		if v == item {
			return true
		}
	}
	return false
}

func (f *FraudDetectionService) saveUserPattern(pattern *UserBehaviorPattern) {
	// Convert to database model and save
	hoursJSON, _ := json.Marshal(pattern.TypicalTransactionHours)
	locationsJSON, _ := json.Marshal(pattern.TypicalLocations)
	devicesJSON, _ := json.Marshal(pattern.TypicalDevices)

	// Use the existing RiskProfile model from security.go
	var profile models.RiskProfile
	if err := f.db.Where("wallet_id = ?", pattern.WalletID).First(&profile).Error; err != nil {
		// Create new profile
		profile = models.RiskProfile{
			WalletID:                 pattern.WalletID,
			AverageTransactionAmount: pattern.AverageAmount,
			MaxTransactionAmount:     pattern.MaxAmount,
			SuspiciousActivityCount:  pattern.SuspiciousActivityCount,
			TypicalTransactionHours:  datatypes.JSON(hoursJSON),
			TypicalLocations:         datatypes.JSON(locationsJSON),
			TypicalDevices:           datatypes.JSON(devicesJSON),
			LastRiskAssessment:       time.Now(),
		}
		f.db.Create(&profile)
	} else {
		// Update existing profile
		profile.AverageTransactionAmount = pattern.AverageAmount
		profile.MaxTransactionAmount = pattern.MaxAmount
		profile.SuspiciousActivityCount = pattern.SuspiciousActivityCount
		profile.TypicalTransactionHours = datatypes.JSON(hoursJSON)
		profile.TypicalLocations = datatypes.JSON(locationsJSON)
		profile.TypicalDevices = datatypes.JSON(devicesJSON)
		profile.LastRiskAssessment = time.Now()
		f.db.Save(&profile)
	}
}
