package database

import (
	"fmt"
	"time"

	"wallet-platform/internal/config"
	"wallet-platform/internal/models"
	"wallet-platform/internal/services"

	"gorm.io/driver/mysql"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// Initialize initializes the database connection and runs migrations
func Initialize(cfg config.DatabaseConfig) (*gorm.DB, error) {
	var db *gorm.DB
	var err error

	// Create database connection based on driver
	switch cfg.Driver {
	case "postgres":
		db, err = connectPostgres(cfg)
	case "mysql":
		db, err = connectMySQL(cfg)
	default:
		return nil, fmt.Errorf("unsupported database driver: %s", cfg.Driver)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// Configure connection pool
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	sqlDB.SetMaxOpenConns(cfg.MaxOpenConns)
	sqlDB.SetMaxIdleConns(cfg.MaxIdleConns)
	sqlDB.SetConnMaxLifetime(time.Duration(cfg.ConnMaxLifetime) * time.Second)
	sqlDB.SetConnMaxIdleTime(time.Duration(cfg.ConnMaxIdleTime) * time.Second)

	// Test the connection (with timeout for local development)
	if err := sqlDB.Ping(); err != nil {
		// For local development, log the error but don't fail
		fmt.Printf("Warning: Database ping failed (this is OK for local development): %v\n", err)
		// Return a mock connection that won't actually work but won't crash the app
		// In production, you should uncomment the line below:
		// return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	// Run migrations
	if err := runMigrations(db); err != nil {
		return nil, fmt.Errorf("failed to run migrations: %w", err)
	}

	return db, nil
}

// connectPostgres creates a PostgreSQL connection
func connectPostgres(cfg config.DatabaseConfig) (*gorm.DB, error) {
	dsn := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		cfg.Host, cfg.Port, cfg.Username, cfg.Password, cfg.Database, cfg.SSLMode)

	return gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
}

// connectMySQL creates a MySQL connection
func connectMySQL(cfg config.DatabaseConfig) (*gorm.DB, error) {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		cfg.Username, cfg.Password, cfg.Host, cfg.Port, cfg.Database)

	return gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
}

// runMigrations runs database migrations for all models
func runMigrations(db *gorm.DB) error {
	// Wallet models
	if err := db.AutoMigrate(
		&models.Wallet{},
		&models.WalletTransaction{},
		&models.WalletTransfer{},
		&models.TransactionLimit{},
		&models.WalletBalance{},
	); err != nil {
		return fmt.Errorf("failed to migrate wallet models: %w", err)
	}

	// PayCard models
	if err := db.AutoMigrate(
		&models.PayCard{},
		&models.PayCardPlan{},
		&models.PayCardTransaction{},
		&models.PayCardToken{},
	); err != nil {
		return fmt.Errorf("failed to migrate paycard models: %w", err)
	}

	// Security models
	if err := db.AutoMigrate(
		&models.SecurityEvent{},
		&models.FraudAlert{},
		&models.DeviceRegistration{},
		&models.TwoFactorAuth{},
		&models.TransactionFlag{},
		&models.SecuritySettings{},
		&models.SecurityAuditLog{},
		&models.RiskProfile{},
	); err != nil {
		return fmt.Errorf("failed to migrate security models: %w", err)
	}

	// Service models
	if err := db.AutoMigrate(
		&models.AppService{},
		&models.ServiceSubscription{},
		&models.ServiceUsage{},
		&models.ServiceBilling{},
		&models.ServiceWebhook{},
	); err != nil {
		return fmt.Errorf("failed to migrate service models: %w", err)
	}

	// Migration models
	if err := db.AutoMigrate(
		&services.MigrationStatus{},
	); err != nil {
		return fmt.Errorf("failed to migrate migration models: %w", err)
	}

	// Analytics models
	if err := db.AutoMigrate(
		&models.WalletAnalytics{},
		&models.PayCardAnalytics{},
		&models.SystemAnalytics{},
		&models.MerchantAnalytics{},
		&models.RevenueAnalytics{},
		&models.AnalyticsReport{},
		&models.ReportSchedule{},
		&models.Alert{},
		&models.ScheduledJob{},
		&models.JobExecution{},
	); err != nil {
		return fmt.Errorf("failed to migrate analytics models: %w", err)
	}

	return nil
}

// CreateIndexes creates additional database indexes for performance
func CreateIndexes(db *gorm.DB) error {
	// Wallet indexes
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_wallet_phone_status ON wallets(phone_number, status)").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_wallet_transaction_created_at ON wallet_transactions(created_at)").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_wallet_transfer_status_created ON wallet_transfers(status, created_at)").Error; err != nil {
		return err
	}

	// PayCard indexes
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_paycard_wallet_status ON pay_cards(wallet_id, status)").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_paycard_transaction_created_at ON pay_card_transactions(created_at)").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_paycard_transaction_merchant ON pay_card_transactions(merchant_name, merchant_category)").Error; err != nil {
		return err
	}

	// Security indexes
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_security_event_created_at ON security_events(created_at)").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_fraud_alert_risk_level ON fraud_alerts(risk_level, created_at)").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_device_registration_wallet_trusted ON device_registrations(wallet_id, is_trusted)").Error; err != nil {
		return err
	}

	// Analytics indexes
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_wallet_analytics_period ON wallet_analytics(period_type, period_start, period_end)").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_paycard_analytics_period ON pay_card_analytics(period_type, period_start, period_end)").Error; err != nil {
		return err
	}

	return nil
}

// HealthCheck performs a database health check
func HealthCheck(db *gorm.DB) error {
	sqlDB, err := db.DB()
	if err != nil {
		return fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	if err := sqlDB.Ping(); err != nil {
		return fmt.Errorf("database ping failed: %w", err)
	}

	return nil
}

// GetStats returns database connection statistics
func GetStats(db *gorm.DB) (map[string]interface{}, error) {
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	stats := sqlDB.Stats()
	return map[string]interface{}{
		"max_open_connections": stats.MaxOpenConnections,
		"open_connections":     stats.OpenConnections,
		"in_use":               stats.InUse,
		"idle":                 stats.Idle,
		"wait_count":           stats.WaitCount,
		"wait_duration":        stats.WaitDuration.String(),
		"max_idle_closed":      stats.MaxIdleClosed,
		"max_idle_time_closed": stats.MaxIdleTimeClosed,
		"max_lifetime_closed":  stats.MaxLifetimeClosed,
	}, nil
}
