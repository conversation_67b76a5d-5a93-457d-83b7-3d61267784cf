# Production-optimized Dockerfile
FROM golang:1.19-alpine AS builder

# Install build dependencies
RUN apk add --no-cache git ca-certificates tzdata

# Set working directory
WORKDIR /app

# Copy go mod files first for better caching
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download && go mod verify

# Copy source code
COPY . .

# Build the application with optimizations
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
    -a -installsuffix cgo \
    -ldflags='-w -s -extldflags "-static"' \
    -o wallet-platform cmd/server/main.go

# Final production stage
FROM scratch

# Copy timezone data
COPY --from=builder /usr/share/zoneinfo /usr/share/zoneinfo

# Copy CA certificates
COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/

# Copy binary
COPY --from=builder /app/wallet-platform /wallet-platform

# Copy configuration files
COPY --from=builder /app/configs /configs

# Expose port
EXPOSE 8086

# Health check (note: scratch doesn't have wget, so we rely on external health checks)
# HEALTHCHECK is handled by orchestration layer (Kubernetes, Docker Compose)

# Run the application
ENTRYPOINT ["/wallet-platform"]
