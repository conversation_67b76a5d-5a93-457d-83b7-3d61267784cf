package services

import (
	"crypto/rand"
	"crypto/sha256"
	"encoding/base32"
	"encoding/json"
	"fmt"
	"math"
	mathrand "math/rand"
	"time"

	"wallet-platform/internal/models"
	"wallet-platform/pkg/logger"
	"wallet-platform/pkg/utils"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// Enhanced2FAService handles advanced two-factor authentication
type Enhanced2FAService struct {
	db     *gorm.DB
	logger *logger.Logger

	// Configuration
	issuer      string
	codeLength  int
	codeExpiry  time.Duration
	maxAttempts int
	lockoutTime time.Duration

	// In-memory tracking for failed attempts (in production, use Redis)
	failedAttempts map[uint]int
	lockouts       map[uint]time.Time
}

// TwoFactorMethod represents different 2FA methods
type TwoFactorMethod string

const (
	MethodTOTP  TwoFactorMethod = "totp"
	MethodSMS   TwoFactorMethod = "sms"
	MethodEmail TwoFactorMethod = "email"
)

// TOTPSetupResult represents TOTP setup information
type TOTPSetupResult struct {
	Secret      string   `json:"secret"`
	QRCodeURL   string   `json:"qr_code_url"`
	BackupCodes []string `json:"backup_codes"`
}

// VerificationResult represents 2FA verification result
type VerificationResult struct {
	Success           bool       `json:"success"`
	Method            string     `json:"method"`
	RemainingAttempts int        `json:"remaining_attempts"`
	LockedUntil       *time.Time `json:"locked_until,omitempty"`
	Message           string     `json:"message"`
}

// BackupCode represents a backup code
type BackupCode struct {
	Code      string     `json:"code"`
	Used      bool       `json:"used"`
	UsedAt    *time.Time `json:"used_at,omitempty"`
	CreatedAt time.Time  `json:"created_at"`
}

// NewEnhanced2FAService creates a new enhanced 2FA service
func NewEnhanced2FAService(db *gorm.DB, log *logger.Logger, issuer string) *Enhanced2FAService {
	return &Enhanced2FAService{
		db:             db,
		logger:         log,
		issuer:         issuer,
		codeLength:     6,
		codeExpiry:     5 * time.Minute,
		maxAttempts:    3,
		lockoutTime:    15 * time.Minute,
		failedAttempts: make(map[uint]int),
		lockouts:       make(map[uint]time.Time),
	}
}

// SetupTOTP sets up TOTP for a wallet
func (t *Enhanced2FAService) SetupTOTP(walletID uint, accountName string) (*TOTPSetupResult, error) {
	// Generate secret key
	secret := t.generateTOTPSecret()

	// Generate backup codes
	backupCodes := t.generateBackupCodes(10)

	// Create QR code URL
	qrCodeURL := t.generateQRCodeURL(accountName, secret)

	// Save TOTP configuration
	backupCodesJSON, _ := t.encodeBackupCodes(backupCodes)

	twoFA := models.TwoFactorAuth{
		WalletID:    walletID,
		Method:      string(MethodTOTP),
		Secret:      secret,
		BackupCodes: datatypes.JSON(backupCodesJSON),
		IsEnabled:   false, // Will be enabled after verification
		CreatedAt:   time.Now(),
	}

	if err := t.db.Create(&twoFA).Error; err != nil {
		t.logger.LogError(err, map[string]interface{}{
			"action":    "setup_totp",
			"wallet_id": walletID,
		})
		return nil, fmt.Errorf("failed to setup TOTP: %w", err)
	}

	// Log TOTP setup
	t.logger.LogSecurity("totp_setup", fmt.Sprintf("%d", walletID), "",
		"TOTP authentication setup initiated")

	return &TOTPSetupResult{
		Secret:      secret,
		QRCodeURL:   qrCodeURL,
		BackupCodes: backupCodes,
	}, nil
}

// EnableTOTP enables TOTP after successful verification
func (t *Enhanced2FAService) EnableTOTP(walletID uint, code string) error {
	var twoFA models.TwoFactorAuth
	if err := t.db.Where("wallet_id = ? AND method = ?", walletID, string(MethodTOTP)).First(&twoFA).Error; err != nil {
		return fmt.Errorf("TOTP not found for wallet")
	}

	// Verify the code
	if !t.verifyTOTPCode(twoFA.Secret, code) {
		return fmt.Errorf("invalid TOTP code")
	}

	// Enable TOTP
	twoFA.IsEnabled = true
	if err := t.db.Save(&twoFA).Error; err != nil {
		return fmt.Errorf("failed to enable TOTP: %w", err)
	}

	// Log TOTP enablement
	t.logger.LogSecurity("totp_enabled", fmt.Sprintf("%d", walletID), "",
		"TOTP authentication enabled")

	return nil
}

// VerifyTOTP verifies a TOTP code
func (t *Enhanced2FAService) VerifyTOTP(walletID uint, code string) (*VerificationResult, error) {
	var twoFA models.TwoFactorAuth
	if err := t.db.Where("wallet_id = ? AND method = ? AND is_enabled = true", walletID, string(MethodTOTP)).First(&twoFA).Error; err != nil {
		return &VerificationResult{
			Success: false,
			Message: "TOTP not enabled for this wallet",
		}, nil
	}

	// Check for lockout
	if lockoutTime, exists := t.lockouts[walletID]; exists && time.Now().Before(lockoutTime) {
		return &VerificationResult{
			Success:     false,
			LockedUntil: &lockoutTime,
			Message:     "Account temporarily locked due to too many failed attempts",
		}, nil
	}

	// Try TOTP code first
	if t.verifyTOTPCode(twoFA.Secret, code) {
		// Reset failed attempts on success
		delete(t.failedAttempts, walletID)
		delete(t.lockouts, walletID)

		// Update last used
		now := time.Now()
		twoFA.LastUsed = &now
		twoFA.UsageCount++
		t.db.Save(&twoFA)

		t.logger.LogSecurity("totp_verified", fmt.Sprintf("%d", walletID), "",
			"TOTP code verified successfully")

		return &VerificationResult{
			Success: true,
			Method:  string(MethodTOTP),
			Message: "TOTP verification successful",
		}, nil
	}

	// Try backup codes
	if t.verifyBackupCode(walletID, code) {
		// Reset failed attempts on success
		delete(t.failedAttempts, walletID)
		delete(t.lockouts, walletID)

		// Update last used
		now2 := time.Now()
		twoFA.LastUsed = &now2
		twoFA.UsageCount++
		t.db.Save(&twoFA)

		t.logger.LogSecurity("backup_code_used", fmt.Sprintf("%d", walletID), "",
			"Backup code used for verification")

		return &VerificationResult{
			Success: true,
			Method:  "backup_code",
			Message: "Backup code verification successful",
		}, nil
	}

	// Increment failed attempts
	attempts := t.failedAttempts[walletID] + 1
	t.failedAttempts[walletID] = attempts
	remainingAttempts := t.maxAttempts - attempts

	// Lock account if max attempts reached
	var lockUntil *time.Time
	if attempts >= t.maxAttempts {
		lockTime := time.Now().Add(t.lockoutTime)
		t.lockouts[walletID] = lockTime
		lockUntil = &lockTime
		remainingAttempts = 0
	}

	t.logger.LogSecurity("totp_verification_failed", fmt.Sprintf("%d", walletID), "",
		fmt.Sprintf("TOTP verification failed. Attempts: %d", attempts))

	return &VerificationResult{
		Success:           false,
		Method:            string(MethodTOTP),
		RemainingAttempts: remainingAttempts,
		LockedUntil:       lockUntil,
		Message:           "Invalid TOTP code",
	}, nil
}

// DisableTOTP disables TOTP for a wallet
func (t *Enhanced2FAService) DisableTOTP(walletID uint, code string) error {
	// Verify current code before disabling
	result, err := t.VerifyTOTP(walletID, code)
	if err != nil {
		return err
	}

	if !result.Success {
		return fmt.Errorf("invalid TOTP code")
	}

	// Disable TOTP
	if err := t.db.Where("wallet_id = ? AND method = ?", walletID, string(MethodTOTP)).Delete(&models.TwoFactorAuth{}).Error; err != nil {
		return fmt.Errorf("failed to disable TOTP: %w", err)
	}

	// Log TOTP disabling
	t.logger.LogSecurity("totp_disabled", fmt.Sprintf("%d", walletID), "",
		"TOTP authentication disabled")

	return nil
}

// GenerateNewBackupCodes generates new backup codes
func (t *Enhanced2FAService) GenerateNewBackupCodes(walletID uint) ([]string, error) {
	var twoFA models.TwoFactorAuth
	if err := t.db.Where("wallet_id = ? AND method = ?", walletID, string(MethodTOTP)).First(&twoFA).Error; err != nil {
		return nil, fmt.Errorf("TOTP not found for wallet")
	}

	// Generate new backup codes
	backupCodes := t.generateBackupCodes(10)
	backupCodesJSON, _ := t.encodeBackupCodes(backupCodes)

	// Update backup codes
	twoFA.BackupCodes = datatypes.JSON(backupCodesJSON)
	if err := t.db.Save(&twoFA).Error; err != nil {
		return nil, fmt.Errorf("failed to update backup codes: %w", err)
	}

	// Log backup codes regeneration
	t.logger.LogSecurity("backup_codes_regenerated", fmt.Sprintf("%d", walletID), "",
		"New backup codes generated")

	return backupCodes, nil
}

// GetTOTPStatus gets TOTP status for a wallet
func (t *Enhanced2FAService) GetTOTPStatus(walletID uint) (bool, error) {
	var count int64
	if err := t.db.Model(&models.TwoFactorAuth{}).
		Where("wallet_id = ? AND method = ? AND is_enabled = true", walletID, string(MethodTOTP)).
		Count(&count).Error; err != nil {
		return false, fmt.Errorf("failed to check TOTP status: %w", err)
	}

	return count > 0, nil
}

// generateTOTPSecret generates a random secret for TOTP
func (t *Enhanced2FAService) generateTOTPSecret() string {
	bytes := make([]byte, 20)
	rand.Read(bytes)
	return base32.StdEncoding.EncodeToString(bytes)
}

// generateQRCodeURL generates a QR code URL for TOTP setup
func (t *Enhanced2FAService) generateQRCodeURL(accountName, secret string) string {
	return fmt.Sprintf("otpauth://totp/%s:%s?secret=%s&issuer=%s",
		t.issuer, accountName, secret, t.issuer)
}

// verifyTOTPCode verifies a TOTP code against a secret
func (t *Enhanced2FAService) verifyTOTPCode(secret, code string) bool {
	// Allow for time drift (check current, previous, and next time windows)
	now := time.Now().Unix() / 30

	for i := -1; i <= 1; i++ {
		timeWindow := now + int64(i)
		expectedCode := t.generateTOTPCode(secret, timeWindow)
		if expectedCode == code {
			return true
		}
	}

	return false
}

// generateTOTPCode generates a TOTP code for a given time window
func (t *Enhanced2FAService) generateTOTPCode(secret string, timeWindow int64) string {
	// Decode secret
	key, err := base32.StdEncoding.DecodeString(secret)
	if err != nil {
		return ""
	}

	// Convert time window to bytes
	timeBytes := make([]byte, 8)
	for i := 7; i >= 0; i-- {
		timeBytes[i] = byte(timeWindow & 0xff)
		timeWindow >>= 8
	}

	// HMAC-SHA256
	hash := sha256.New()
	hash.Write(key)
	hash.Write(timeBytes)
	hmac := hash.Sum(nil)

	// Dynamic truncation
	offset := hmac[len(hmac)-1] & 0x0f
	code := ((int(hmac[offset]) & 0x7f) << 24) |
		((int(hmac[offset+1]) & 0xff) << 16) |
		((int(hmac[offset+2]) & 0xff) << 8) |
		(int(hmac[offset+3]) & 0xff)

	// Generate 6-digit code
	code = code % int(math.Pow10(t.codeLength))
	return fmt.Sprintf("%0*d", t.codeLength, code)
}

// generateBackupCodes generates backup codes
func (t *Enhanced2FAService) generateBackupCodes(count int) []string {
	codes := make([]string, count)
	for i := 0; i < count; i++ {
		bytes := make([]byte, 4)
		rand.Read(bytes)
		codes[i] = fmt.Sprintf("%08x", bytes)
	}
	return codes
}

// encodeBackupCodes encodes backup codes for storage
func (t *Enhanced2FAService) encodeBackupCodes(codes []string) (string, error) {
	backupCodes := make([]BackupCode, len(codes))
	for i, code := range codes {
		backupCodes[i] = BackupCode{
			Code:      code,
			Used:      false,
			CreatedAt: time.Now(),
		}
	}

	encoded, err := json.Marshal(backupCodes)
	return string(encoded), err
}

// verifyBackupCode verifies and marks a backup code as used
func (t *Enhanced2FAService) verifyBackupCode(walletID uint, code string) bool {
	var twoFA models.TwoFactorAuth
	if err := t.db.Where("wallet_id = ? AND method = ?", walletID, string(MethodTOTP)).First(&twoFA).Error; err != nil {
		return false
	}

	// Parse backup codes
	var backupCodes []BackupCode
	if err := json.Unmarshal(twoFA.BackupCodes, &backupCodes); err != nil {
		return false
	}

	// Find and verify code
	for i, backupCode := range backupCodes {
		if backupCode.Code == code && !backupCode.Used {
			// Mark as used
			backupCodes[i].Used = true
			now := time.Now()
			backupCodes[i].UsedAt = &now

			// Update in database
			updatedJSON, _ := json.Marshal(backupCodes)
			twoFA.BackupCodes = datatypes.JSON(updatedJSON)
			t.db.Save(&twoFA)

			return true
		}
	}

	return false
}

// SendSMSCode sends a verification code via SMS using Centurion API
func (t *Enhanced2FAService) SendSMSCode(phoneNumber string, code string) error {
	message := fmt.Sprintf("Your wallet verification code is: %s. This code will expire in 5 minutes.", code)

	// Import utils package for SMS functionality
	err := utils.SendSMSViaAPI(phoneNumber, message)
	if err != nil {
		t.logger.LogError(err, map[string]interface{}{
			"action": "send_sms_code",
			"phone":  phoneNumber,
		})
		return fmt.Errorf("failed to send SMS: %w", err)
	}

	t.logger.LogSecurity("sms_code_sent", "", phoneNumber, "SMS verification code sent")
	return nil
}

// SendEmailCode sends a verification code via email using Centurion API
func (t *Enhanced2FAService) SendEmailCode(email string, code string) error {
	subject := "Wallet Platform - Verification Code"
	body := fmt.Sprintf(`
Dear User,

Your wallet verification code is: %s

This code will expire in 5 minutes. Please do not share this code with anyone.

If you did not request this code, please contact our support team immediately.

Best regards,
Wallet Platform Team
`, code)

	// Import utils package for Email functionality
	err := utils.SendEmailViaAPI(email, subject, body)
	if err != nil {
		t.logger.LogError(err, map[string]interface{}{
			"action": "send_email_code",
			"email":  email,
		})
		return fmt.Errorf("failed to send email: %w", err)
	}

	t.logger.LogSecurity("email_code_sent", "", email, "Email verification code sent")
	return nil
}

// GenerateVerificationCode generates a 6-digit verification code
func (t *Enhanced2FAService) GenerateVerificationCode() string {
	mathrand.Seed(time.Now().UnixNano())
	return fmt.Sprintf("%06d", mathrand.Intn(1000000))
}
