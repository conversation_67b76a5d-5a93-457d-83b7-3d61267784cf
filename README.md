# Wallet Platform

A production-ready standalone digital wallet and PayCard microservice platform. This platform provides comprehensive wallet management, digital/physical card services, advanced security features, real-time analytics, and complete administrative capabilities.

## 🎯 Production Status

⚠️ **REQUIRES ADDITIONAL WORK BEFORE PRODUCTION** - Core functionality implemented but critical issues remain

### ✅ Completed Features
- Complete API implementation with 100+ endpoints
- User authentication system with JWT tokens
- Basic security features and middleware
- Wallet and PayCard core functionality
- Internal APIs for service-to-service communication
- External webhook integrations
- Migration tools and data validation
- Environment-specific configuration system

### 🚨 Critical Issues (MUST FIX BEFORE PRODUCTION)
- **Admin Authentication**: Currently disabled due to security concerns - requires proper RBAC implementation
- **2FA Implementation**: Placeholder implementation - needs proper TOTP/SMS verification
- **Input Validation**: Comprehensive validation middleware created but not fully integrated
- **PIN Management**: Card PIN verification not implemented
- **Service Completions**: Several admin and security endpoints are incomplete

### 📊 Production Readiness Score: 45/100
**Estimated time to production readiness: 6-8 weeks**

See [Security Documentation](docs/SECURITY.md) and [Code Quality Issues](docs/CODE_QUALITY_ISSUES.md) for detailed information.

## 🚀 Features

### Digital Wallet Management
- **Phone-based Wallets** - Create wallets using phone numbers with unique account numbers
- **Balance Management** - Real-time balance tracking and updates
- **Fund Transfers** - Secure peer-to-peer transfers between wallets
- **Transaction History** - Comprehensive transaction logging and retrieval
- **Spending Limits** - Configurable daily and monthly spending controls

### PayCard Services
- **Digital Cards** - QR-code based virtual debit cards
- **Physical Cards** - Request and manage physical card delivery
- **PIN Management** - Secure PIN creation, updates, and validation
- **Spending Controls** - Merchant restrictions and category-based limits
- **Contactless Payments** - NFC and QR-code payment processing
- **Card Tokenization** - Secure token generation for enhanced security

### Advanced Security
- **Service-to-Service Authentication** - Internal API key system for microservice integration
- **Two-Factor Authentication** - TOTP, SMS, and email-based 2FA
- **Fraud Detection** - Real-time transaction monitoring and risk scoring
- **Device Management** - Device fingerprinting and trust scoring
- **IP Geolocation** - Location-based security and VPN detection
- **Session Tracking** - Comprehensive user session management
- **Security Alerts** - Automated threat detection and notifications

### Analytics & Monitoring
- **Real-time Metrics** - Live transaction and system performance data
- **Business Intelligence** - Revenue analytics and user behavior insights
- **System Health** - Comprehensive monitoring with alerting
- **Dashboard** - Real-time visualization of key metrics
- **Automated Reports** - Scheduled analytics and insights generation

### Migration & Integration
- **Data Migration** - Seamless migration from main payment engine
- **Webhook System** - Event-driven notifications with HMAC verification
- **REST API** - Comprehensive API for all platform features
- **Rate Limiting** - Configurable request throttling and protection

## 🏗️ Architecture

The platform follows a microservices architecture with clear separation of concerns:

```
┌─────────────────────────────────────────────────────────────────┐
│                        Client Applications                       │
├─────────────────────────────────────────────────────────────────┤
│                         Load Balancer                           │
├─────────────────────────────────────────────────────────────────┤
│                      Wallet Platform API                        │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐│
│  │   Wallet    │ │   PayCard   │ │  Analytics  │ │  Migration  ││
│  │ Controller  │ │ Controller  │ │ Controller  │ │ Controller  ││
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘│
├─────────────────────────────────────────────────────────────────┤
│                        Service Layer                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐│
│  │   Wallet    │ │   PayCard   │ │  Analytics  │ │  Migration  ││
│  │   Service   │ │   Service   │ │   Service   │ │   Service   ││
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘│
├─────────────────────────────────────────────────────────────────┤
│                         Data Layer                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐                │
│  │ PostgreSQL  │ │    Redis    │ │   File      │                │
│  │  Database   │ │    Cache    │ │  Storage    │                │
│  └─────────────┘ └─────────────┘ └─────────────┘                │
└─────────────────────────────────────────────────────────────────┘
```

## 🛠️ Technology Stack

- **Language**: Go 1.19+
- **Web Framework**: Gin
- **Database**: MySQL 8.0+
- **Cache**: Redis 6.0+
- **ORM**: GORM
- **Authentication**: JWT tokens, API keys
- **Communications**: Centurion SMS and Email APIs
- **Monitoring**: Prometheus metrics
- **Logging**: Structured JSON logging
- **Containerization**: Docker & Kubernetes ready

## 📋 Prerequisites

- Go 1.19 or higher
- MySQL 8.0+
- Redis 6.0+
- Git

## 🚀 Quick Start

### 1. Clone Repository
```bash
git clone <repository-url>
cd wallet-platform
```

### 2. Install Dependencies
```bash
go mod download
```

### 3. Setup Environment

**⚠️ IMPORTANT**: Choose the correct environment configuration:

```bash
# For Development
cp .env.development.example .env.development
# Edit .env.development with your development configuration

# For Production (SECURE VALUES REQUIRED)
cp .env.production.example .env.production
# Edit .env.production with SECURE production values

# Set environment
export APP_ENVIRONMENT=development  # or production
```

**🔒 Security Note**: Never use development credentials in production. Generate secure random values for all secrets in production.

### 4. Setup Database
```bash
# MySQL
mysql -u root -p -e "CREATE DATABASE wallet_platform;"
# Or using Docker
docker run --name wallet-mysql \
  -e MYSQL_DATABASE=wallet_platform \
  -e MYSQL_USER=wallet_user \
  -e MYSQL_PASSWORD=secure_password \
  -e MYSQL_ROOT_PASSWORD=root_password \
  -p 3306:3306 -d mysql:8.0
```

### 5. Setup Redis
```bash
# Using Docker
docker run --name wallet-redis -p 6379:6379 -d redis:6-alpine
```

### 6. Run Migrations
```bash
go run cmd/migrate/main.go
```

### 7. Start Server
```bash
go run cmd/server/main.go
```

The server will start on `http://localhost:8086`

## 🔧 Configuration

### Environment Variables

```env
# Server
PORT=8086
HOST=0.0.0.0
ENV=production

# Database
DB_HOST=localhost
DB_PORT=3306
DB_NAME=wallet_platform
DB_USER=wallet_user
DB_PASSWORD=secure_password

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Security
JWT_SECRET=your-super-secure-jwt-secret
API_KEY_SECRET=your-api-key-secret
ENCRYPTION_KEY=32-character-encryption-key
INTERNAL_API_KEY=your-internal-api-key-for-service-communication

# Features
RATE_LIMIT_ENABLED=true
METRICS_ENABLED=true
WEBHOOK_SECRET=webhook-verification-secret

# Communications
CENTURION_SMS_API_KEY=your-centurion-sms-api-key
CENTURION_EMAIL_API_KEY=your-centurion-email-api-key
```

### Configuration File

Create `config/config.yaml`:

```yaml
server:
  port: 8086
  host: "0.0.0.0"
  read_timeout: 30s
  write_timeout: 30s

database:
  host: "localhost"
  port: 3306
  name: "wallet_platform"
  user: "wallet_user"
  password: "secure_password"
  charset: "utf8mb4"
  parse_time: true

redis:
  host: "localhost"
  port: 6379
  password: ""
  db: 0

security:
  jwt_secret: "your-super-secure-jwt-secret"
  rate_limit:
    enabled: true
    requests_per_minute: 100

logging:
  level: "info"
  format: "json"
```

## 📚 API Documentation

### Base URL
```
Production: https://wallet-api.yourcompany.com
Development: http://localhost:8086
```

### Authentication
All endpoints require authentication:
```bash
curl -H "Authorization: Bearer <jwt_token>" \
     -H "X-API-Key: <api_key>" \
     https://wallet-api.yourcompany.com/api/v1/wallets
```

### Key Endpoints

#### Wallet Management
- `POST /api/v1/wallets` - Create wallet
- `GET /api/v1/wallets/{id}` - Get wallet details
- `GET /api/v1/wallets/{id}/balance` - Get wallet balance
- `POST /api/v1/wallets/{id}/topup` - Top up wallet
- `POST /api/v1/wallets/transfer` - Transfer funds

#### PayCard Management
- `POST /api/v1/cards` - Create PayCard
- `GET /api/v1/cards/{id}` - Get card details
- `POST /api/v1/cards/{id}/block` - Block card
- `PUT /api/v1/cards/{id}/pin` - Update PIN
- `POST /api/v1/cards/{id}/request-physical` - Request physical card

#### Analytics
- `GET /api/v1/analytics/metrics/realtime` - Real-time metrics
- `GET /api/v1/analytics/dashboard` - Dashboard data
- `GET /api/v1/analytics/health` - System health

#### Internal API (Service-to-Service)
- `POST /api/v1/internal/wallets` - Create wallet (internal)
- `GET /api/v1/internal/wallets/{id}` - Get wallet (internal)
- `GET /api/v1/internal/wallets/phone/{phone}` - Get wallet by phone (internal)
- `POST /api/v1/internal/cards` - Create card (internal)
- `GET /api/v1/internal/cards/wallet/{wallet_id}` - Get cards by wallet (internal)

For complete API documentation, see [API Documentation](docs/API_DOCUMENTATION.md)

For service-to-service authentication details, see [Service-to-Service Auth](docs/SERVICE_TO_SERVICE_AUTH.md)

## 🧪 Testing

### Run Tests
```bash
# All tests
go test ./...

# With coverage
go test -cover ./...

# Specific package
go test ./internal/services/
```

### Integration Tests
```bash
go test -tags=integration ./tests/integration/
```

## 🚀 Deployment

### Docker
```bash
# Build image
docker build -t wallet-platform .

# Run container
docker run -p 8086:8086 wallet-platform
```

### Docker Compose
```bash
docker-compose up -d
```

### Kubernetes
```bash
kubectl apply -f k8s/
```

For detailed deployment instructions, see [Deployment Guide](docs/DEPLOYMENT_GUIDE.md)

## 📖 Documentation

- [API Documentation](docs/API_DOCUMENTATION.md) - Complete API reference
- [Architecture Overview](docs/ARCHITECTURE_OVERVIEW.md) - System architecture and design
- [Deployment Guide](docs/DEPLOYMENT_GUIDE.md) - Production deployment instructions
- [Developer Guide](docs/DEVELOPER_GUIDE.md) - Development setup and guidelines
- [Migration Guide](docs/MIGRATION_GUIDE.md) - Data migration from main system

## 🔒 Security

### Current Security Status
⚠️ **CRITICAL SECURITY ISSUES IDENTIFIED** - See [Security Documentation](docs/SECURITY.md) for details

### Implemented Security Features
- **Authentication**: JWT tokens and API keys for user authentication
- **Environment Separation**: Secure configuration management for dev/staging/production
- **Input Validation**: Comprehensive validation middleware (requires integration)
- **Rate Limiting**: Configurable request throttling
- **Fraud Detection**: Basic real-time transaction monitoring
- **Audit Logging**: Security event logging framework

### 🚨 Security Issues Requiring Immediate Attention
- **Admin Authentication**: Currently disabled - requires proper RBAC implementation
- **2FA Implementation**: Placeholder only - needs proper TOTP/SMS verification
- **PIN Security**: Card PIN management not implemented
- **Input Validation**: Not fully integrated across all endpoints

### Security Recommendations
1. **DO NOT deploy to production** until admin authentication is properly implemented
2. **Generate secure random values** for all production secrets
3. **Implement proper 2FA** before enabling sensitive operations
4. **Complete input validation** integration across all endpoints
5. **Conduct security audit** before production deployment

For detailed security information, see [Security Documentation](docs/SECURITY.md)

## 📊 Monitoring

### Health Checks
```bash
curl http://localhost:8086/health
```

### Metrics
Prometheus metrics available at:
```
http://localhost:8086/metrics
```

### Logging
Structured JSON logs with configurable levels:
- Error tracking
- Performance monitoring
- Security events
- Business metrics

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

See [Developer Guide](docs/DEVELOPER_GUIDE.md) for detailed contribution guidelines.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [docs/](docs/)
- **Issues**: [GitHub Issues](https://github.com/yourcompany/wallet-platform/issues)
- **Email**: <EMAIL>
- **Status**: [status.wallet-platform.yourcompany.com](https://status.wallet-platform.yourcompany.com)

## 🗺️ Roadmap

### Upcoming Features
- [ ] Machine learning fraud detection
- [ ] Cryptocurrency support
- [ ] Open Banking integration
- [ ] Advanced analytics dashboard
- [ ] Mobile SDK
- [ ] GraphQL API
- [ ] Multi-currency support
- [ ] Biometric authentication

### Recent Updates
- [x] Standalone platform extraction
- [x] Advanced security features
- [x] Real-time analytics
- [x] Migration system
- [x] Comprehensive API
- [x] Docker containerization
- [x] Kubernetes support
#   w a l l e t - p l a t f o r m 
 
 