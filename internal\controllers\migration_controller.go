package controllers

import (
	"context"
	"net/http"
	"strconv"
	"time"

	"wallet-platform/internal/services"

	"github.com/gin-gonic/gin"
)

// MigrationController handles migration-related HTTP requests
type MigrationController struct {
	migrationService *services.MigrationService
}

// NewMigrationController creates a new migration controller
func NewMigrationController(container *services.Container) *MigrationController {
	return &MigrationController{
		migrationService: container.MigrationService,
	}
}

// Remove duplicate ErrorResponse - using the one from analytics_controller.go

// StartMigration starts the complete data migration process
func (mc *MigrationController) StartMigration(c *gin.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Minute)
	defer cancel()

	result, err := mc.migrationService.MigrateAllData(ctx)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Migration failed",
			Message: err.<PERSON>rror(),
			Code:    "MIGRATION_ERROR",
		})
		return
	}

	c.<PERSON>(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
		"message": "Migration completed successfully",
	})
}

// MigrateWallets migrates only wallet data
func (mc *MigrationController) MigrateWallets(c *gin.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Minute)
	defer cancel()

	result, err := mc.migrationService.MigrateDigitalWallets(ctx)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Wallet migration failed",
			Message: err.Error(),
			Code:    "WALLET_MIGRATION_ERROR",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
		"message": "Wallet migration completed successfully",
	})
}

// MigratePayCards migrates only paycard data
func (mc *MigrationController) MigratePayCards(c *gin.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Minute)
	defer cancel()

	result, err := mc.migrationService.MigratePayCards(ctx)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "PayCard migration failed",
			Message: err.Error(),
			Code:    "PAYCARD_MIGRATION_ERROR",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
		"message": "PayCard migration completed successfully",
	})
}

// MigrateTransactions migrates only transaction data
func (mc *MigrationController) MigrateTransactions(c *gin.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), 20*time.Minute)
	defer cancel()

	result, err := mc.migrationService.MigrateWalletTransactions(ctx)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Transaction migration failed",
			Message: err.Error(),
			Code:    "TRANSACTION_MIGRATION_ERROR",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
		"message": "Transaction migration completed successfully",
	})
}

// GetMigrationStatus returns the status of all migrations
func (mc *MigrationController) GetMigrationStatus(c *gin.Context) {
	statuses, err := mc.migrationService.GetMigrationStatus()
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to get migration status",
			Message: err.Error(),
			Code:    "STATUS_ERROR",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    statuses,
	})
}

// GetMigrationStatusByType returns the status of a specific migration type
func (mc *MigrationController) GetMigrationStatusByType(c *gin.Context) {
	migrationType := c.Param("type")
	if migrationType == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Migration type is required",
			Message: "Please specify a migration type",
			Code:    "INVALID_TYPE",
		})
		return
	}

	status, err := mc.migrationService.GetMigrationStatusByType(migrationType)
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{
			Error:   "Migration status not found",
			Message: err.Error(),
			Code:    "STATUS_NOT_FOUND",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    status,
	})
}

// ValidateMigration validates the migrated data integrity
func (mc *MigrationController) ValidateMigration(c *gin.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
	defer cancel()

	result, err := mc.migrationService.ValidateMigration(ctx)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Migration validation failed",
			Message: err.Error(),
			Code:    "VALIDATION_ERROR",
		})
		return
	}

	statusCode := http.StatusOK
	if result.Status == "failed" {
		statusCode = http.StatusBadRequest
	}

	c.JSON(statusCode, gin.H{
		"success": result.Status != "failed",
		"data":    result,
		"message": "Migration validation completed",
	})
}

// RollbackMigration rolls back the migration (for testing purposes)
func (mc *MigrationController) RollbackMigration(c *gin.Context) {
	// Add confirmation parameter check
	confirm := c.Query("confirm")
	if confirm != "true" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Confirmation required",
			Message: "Add ?confirm=true to confirm rollback",
			Code:    "CONFIRMATION_REQUIRED",
		})
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
	defer cancel()

	err := mc.migrationService.RollbackMigration(ctx)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Migration rollback failed",
			Message: err.Error(),
			Code:    "ROLLBACK_ERROR",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Migration rollback completed successfully",
	})
}

// GetMigrationProgress returns real-time migration progress
func (mc *MigrationController) GetMigrationProgress(c *gin.Context) {
	migrationID := c.Param("id")
	if migrationID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Migration ID is required",
			Message: "Please specify a migration ID",
			Code:    "INVALID_ID",
		})
		return
	}

	// Convert string ID to uint
	id, err := strconv.ParseUint(migrationID, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid migration ID",
			Message: "Migration ID must be a valid number",
			Code:    "INVALID_ID_FORMAT",
		})
		return
	}

	// Get migration status by ID (you'd need to implement this method)
	status, err := mc.migrationService.GetMigrationStatusByType("complete_migration")
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{
			Error:   "Migration not found",
			Message: err.Error(),
			Code:    "MIGRATION_NOT_FOUND",
		})
		return
	}

	// Calculate progress percentage
	progress := 0.0
	if status.RecordsTotal > 0 {
		progress = float64(status.RecordsProcessed) / float64(status.RecordsTotal) * 100
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"id":                uint(id),
			"status":            status.Status,
			"progress":          progress,
			"records_total":     status.RecordsTotal,
			"records_processed": status.RecordsProcessed,
			"records_failed":    status.RecordsFailed,
			"start_time":        status.StartTime,
			"end_time":          status.EndTime,
			"error_message":     status.ErrorMessage,
		},
	})
}

// GetMigrationSummary returns a summary of all migration operations
func (mc *MigrationController) GetMigrationSummary(c *gin.Context) {
	statuses, err := mc.migrationService.GetMigrationStatus()
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to get migration summary",
			Message: err.Error(),
			Code:    "SUMMARY_ERROR",
		})
		return
	}

	// Calculate summary statistics
	summary := gin.H{
		"total_migrations":     len(statuses),
		"completed_migrations": 0,
		"failed_migrations":    0,
		"running_migrations":   0,
		"total_records":        0,
		"total_processed":      0,
		"total_failed":         0,
		"migrations":           statuses,
	}

	for _, status := range statuses {
		switch status.Status {
		case "completed", "completed_with_errors":
			summary["completed_migrations"] = summary["completed_migrations"].(int) + 1
		case "failed":
			summary["failed_migrations"] = summary["failed_migrations"].(int) + 1
		case "running":
			summary["running_migrations"] = summary["running_migrations"].(int) + 1
		}
		summary["total_records"] = summary["total_records"].(int) + status.RecordsTotal
		summary["total_processed"] = summary["total_processed"].(int) + status.RecordsProcessed
		summary["total_failed"] = summary["total_failed"].(int) + status.RecordsFailed
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    summary,
	})
}
