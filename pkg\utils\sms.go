package utils

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
)

// SendSMSViaAPI sends an SMS using the Centurion SMS API
func SendSMSViaAPI(phone, message string) error {
	apiKey := os.Getenv("CENTURION_SMS_API_KEY")
	if apiKey == "" {
		return fmt.Errorf("CENTURION_SMS_API_KEY not set in environment")
	}

	url := "https://auth.centurionbd.com/api/v1/sms/send"
	payload := map[string]string{
		"phone_number": phone,
		"message":      message,
	}
	jsonData, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("failed to marshal SMS payload: %w", err)
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create SMS request: %w", err)
	}

	req.Header.Set("x-api-key", api<PERSON>ey)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send SMS request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return fmt.Errorf("SMS API returned status %d", resp.StatusCode)
	}

	return nil
}
