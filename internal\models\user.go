package models

import (
	"time"

	"gorm.io/datatypes"
)

// User represents a user account with authentication information
type User struct {
	ID           uint    `gorm:"primaryKey" json:"id"`
	WalletID     uint    `gorm:"uniqueIndex;not null" json:"wallet_id"`
	Wallet       Wallet  `gorm:"constraint:OnDelete:CASCADE" json:"wallet"`
	
	// Personal information
	FirstName    string  `gorm:"type:varchar(50);not null" json:"first_name"`
	LastName     string  `gorm:"type:varchar(50);not null" json:"last_name"`
	Email        string  `gorm:"type:varchar(100);uniqueIndex;not null" json:"email"`
	PhoneNumber  string  `gorm:"type:varchar(20);uniqueIndex;not null" json:"phone_number"`
	
	// Authentication
	PasswordHash string  `gorm:"type:varchar(255);not null" json:"-"` // Don't include in JSON
	IsVerified   bool    `gorm:"default:false" json:"is_verified"`
	
	// Verification codes
	VerificationCode     string     `gorm:"type:varchar(10)" json:"-"`
	VerificationExpiry   *time.Time `json:"-"`
	PasswordResetCode    string     `gorm:"type:varchar(10)" json:"-"`
	PasswordResetExpiry  *time.Time `json:"-"`
	
	// Account status
	Status       string  `gorm:"type:varchar(20);default:'active'" json:"status"` // active, suspended, deactivated
	LastLoginAt  *time.Time `json:"last_login_at"`
	LoginCount   int     `gorm:"default:0" json:"login_count"`
	
	// Profile information
	DateOfBirth  *time.Time `json:"date_of_birth"`
	Gender       string  `gorm:"type:varchar(10)" json:"gender"`
	Nationality  string  `gorm:"type:varchar(50)" json:"nationality"`
	Address      string  `gorm:"type:text" json:"address"`
	City         string  `gorm:"type:varchar(50)" json:"city"`
	Country      string  `gorm:"type:varchar(50)" json:"country"`
	PostalCode   string  `gorm:"type:varchar(20)" json:"postal_code"`
	
	// Preferences
	Language     string  `gorm:"type:varchar(10);default:'en'" json:"language"`
	Timezone     string  `gorm:"type:varchar(50);default:'UTC'" json:"timezone"`
	Currency     string  `gorm:"type:varchar(10);default:'USD'" json:"currency"`
	
	// Metadata
	Metadata     datatypes.JSON `json:"metadata"`
	
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// UserRequest represents a request to create or update user information
type UserRequest struct {
	FirstName   string `json:"first_name" binding:"required"`
	LastName    string `json:"last_name" binding:"required"`
	Email       string `json:"email" binding:"required,email"`
	PhoneNumber string `json:"phone_number" binding:"required"`
	Password    string `json:"password" binding:"required,min=8"`
	DateOfBirth string `json:"date_of_birth" binding:"omitempty"`
	Gender      string `json:"gender" binding:"omitempty,oneof=male female other"`
	Nationality string `json:"nationality" binding:"omitempty"`
	Address     string `json:"address" binding:"omitempty"`
	City        string `json:"city" binding:"omitempty"`
	Country     string `json:"country" binding:"omitempty"`
	PostalCode  string `json:"postal_code" binding:"omitempty"`
	Language    string `json:"language" binding:"omitempty"`
	Timezone    string `json:"timezone" binding:"omitempty"`
}

// UserResponse represents the response format for user operations
type UserResponse struct {
	ID          uint      `json:"id"`
	WalletID    uint      `json:"wallet_id"`
	FirstName   string    `json:"first_name"`
	LastName    string    `json:"last_name"`
	Email       string    `json:"email"`
	PhoneNumber string    `json:"phone_number"`
	IsVerified  bool      `json:"is_verified"`
	Status      string    `json:"status"`
	LastLoginAt *time.Time `json:"last_login_at"`
	LoginCount  int       `json:"login_count"`
	DateOfBirth *time.Time `json:"date_of_birth"`
	Gender      string    `json:"gender"`
	Nationality string    `json:"nationality"`
	Address     string    `json:"address"`
	City        string    `json:"city"`
	Country     string    `json:"country"`
	PostalCode  string    `json:"postal_code"`
	Language    string    `json:"language"`
	Timezone    string    `json:"timezone"`
	Currency    string    `json:"currency"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// UserProfileUpdateRequest represents a request to update user profile
type UserProfileUpdateRequest struct {
	FirstName   string `json:"first_name" binding:"omitempty"`
	LastName    string `json:"last_name" binding:"omitempty"`
	Email       string `json:"email" binding:"omitempty,email"`
	DateOfBirth string `json:"date_of_birth" binding:"omitempty"`
	Gender      string `json:"gender" binding:"omitempty,oneof=male female other"`
	Nationality string `json:"nationality" binding:"omitempty"`
	Address     string `json:"address" binding:"omitempty"`
	City        string `json:"city" binding:"omitempty"`
	Country     string `json:"country" binding:"omitempty"`
	PostalCode  string `json:"postal_code" binding:"omitempty"`
	Language    string `json:"language" binding:"omitempty"`
	Timezone    string `json:"timezone" binding:"omitempty"`
}

// PasswordChangeRequest represents a request to change password
type PasswordChangeRequest struct {
	CurrentPassword string `json:"current_password" binding:"required"`
	NewPassword     string `json:"new_password" binding:"required,min=8"`
	ConfirmPassword string `json:"confirm_password" binding:"required"`
}
