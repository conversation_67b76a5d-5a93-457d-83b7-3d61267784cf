package logger

import (
	"os"

	"github.com/sirupsen/logrus"
)

// <PERSON>gger wraps logrus.Logger with additional functionality
type Logger struct {
	*logrus.Logger
}

// NewLogger creates a new logger instance
func NewLogger(level, format string) *Logger {
	logger := logrus.New()

	// Set log level
	logLevel, err := logrus.ParseLevel(level)
	if err != nil {
		logLevel = logrus.InfoLevel
	}
	logger.SetLevel(logLevel)

	// Set log format
	switch format {
	case "json":
		logger.SetFormatter(&logrus.JSONFormatter{
			TimestampFormat: "2006-01-02T15:04:05.000Z07:00",
		})
	case "text":
		logger.SetFormatter(&logrus.TextFormatter{
			FullTimestamp:   true,
			TimestampFormat: "2006-01-02T15:04:05.000Z07:00",
		})
	default:
		logger.SetFormatter(&logrus.JSONFormatter{
			TimestampFormat: "2006-01-02T15:04:05.000Z07:00",
		})
	}

	// Set output
	logger.SetOutput(os.Stdout)

	return &Logger{Logger: logger}
}

// WithFields creates a new logger entry with fields
func (l *Logger) WithFields(fields map[string]interface{}) *logrus.Entry {
	return l.Logger.WithFields(fields)
}

// WithField creates a new logger entry with a single field
func (l *Logger) WithField(key string, value interface{}) *logrus.Entry {
	return l.Logger.WithField(key, value)
}

// WithError creates a new logger entry with an error field
func (l *Logger) WithError(err error) *logrus.Entry {
	return l.Logger.WithError(err)
}

// WithContext creates a new logger entry with context fields
func (l *Logger) WithContext(requestID, userID, action string) *logrus.Entry {
	return l.Logger.WithFields(logrus.Fields{
		"request_id": requestID,
		"user_id":    userID,
		"action":     action,
	})
}

// LogRequest logs HTTP request information
func (l *Logger) LogRequest(method, path, userAgent, ip string, statusCode int, duration int64) {
	l.WithFields(logrus.Fields{
		"method":      method,
		"path":        path,
		"user_agent":  userAgent,
		"ip":          ip,
		"status_code": statusCode,
		"duration_ms": duration,
		"type":        "http_request",
	}).Info("HTTP request processed")
}

// LogError logs error information with context
func (l *Logger) LogError(err error, context map[string]interface{}) {
	entry := l.WithError(err)
	if context != nil {
		entry = entry.WithFields(context)
	}
	entry.Error("Error occurred")
}

// LogSecurity logs security-related events
func (l *Logger) LogSecurity(event, userID, ip, details string) {
	l.WithFields(logrus.Fields{
		"event":   event,
		"user_id": userID,
		"ip":      ip,
		"details": details,
		"type":    "security",
	}).Warn("Security event")
}

// LogTransaction logs transaction-related events
func (l *Logger) LogTransaction(transactionID, walletID, action string, amount float64, status string) {
	l.WithFields(logrus.Fields{
		"transaction_id": transactionID,
		"wallet_id":      walletID,
		"action":         action,
		"amount":         amount,
		"status":         status,
		"type":           "transaction",
	}).Info("Transaction event")
}

// LogPayCard logs PayCard-related events
func (l *Logger) LogPayCard(cardID, walletID, action string, amount float64, merchant string) {
	l.WithFields(logrus.Fields{
		"card_id":   cardID,
		"wallet_id": walletID,
		"action":    action,
		"amount":    amount,
		"merchant":  merchant,
		"type":      "paycard",
	}).Info("PayCard event")
}

// LogFraud logs fraud detection events
func (l *Logger) LogFraud(alertID, walletID, riskLevel string, riskScore float64, reasons []string) {
	l.WithFields(logrus.Fields{
		"alert_id":   alertID,
		"wallet_id":  walletID,
		"risk_level": riskLevel,
		"risk_score": riskScore,
		"reasons":    reasons,
		"type":       "fraud",
	}).Warn("Fraud alert")
}

// LogAPI logs API-related events
func (l *Logger) LogAPI(endpoint, method, userID string, requestSize, responseSize int, duration int64) {
	l.WithFields(logrus.Fields{
		"endpoint":      endpoint,
		"method":        method,
		"user_id":       userID,
		"request_size":  requestSize,
		"response_size": responseSize,
		"duration_ms":   duration,
		"type":          "api",
	}).Info("API call")
}

// LogDatabase logs database-related events
func (l *Logger) LogDatabase(operation, table string, duration int64, rowsAffected int64) {
	l.WithFields(logrus.Fields{
		"operation":     operation,
		"table":         table,
		"duration_ms":   duration,
		"rows_affected": rowsAffected,
		"type":          "database",
	}).Debug("Database operation")
}

// LogCache logs cache-related events
func (l *Logger) LogCache(operation, key string, hit bool, duration int64) {
	l.WithFields(logrus.Fields{
		"operation":   operation,
		"key":         key,
		"hit":         hit,
		"duration_ms": duration,
		"type":        "cache",
	}).Debug("Cache operation")
}

// LogWebhook logs webhook-related events
func (l *Logger) LogWebhook(webhookID, event, url string, statusCode int, duration int64) {
	l.WithFields(logrus.Fields{
		"webhook_id":  webhookID,
		"event":       event,
		"url":         url,
		"status_code": statusCode,
		"duration_ms": duration,
		"type":        "webhook",
	}).Info("Webhook delivery")
}

// LogService logs service-related events
func (l *Logger) LogService(serviceID, action, userID string, success bool, details string) {
	l.WithFields(logrus.Fields{
		"service_id": serviceID,
		"action":     action,
		"user_id":    userID,
		"success":    success,
		"details":    details,
		"type":       "service",
	}).Info("Service event")
}

// LogAnalytics logs analytics-related events
func (l *Logger) LogAnalytics(reportType, period string, recordCount int, duration int64) {
	l.WithFields(logrus.Fields{
		"report_type":  reportType,
		"period":       period,
		"record_count": recordCount,
		"duration_ms":  duration,
		"type":         "analytics",
	}).Info("Analytics generation")
}

// LogStartup logs application startup events
func (l *Logger) LogStartup(component, version string, config map[string]interface{}) {
	l.WithFields(logrus.Fields{
		"component": component,
		"version":   version,
		"config":    config,
		"type":      "startup",
	}).Info("Component started")
}

// LogShutdown logs application shutdown events
func (l *Logger) LogShutdown(component string, duration int64) {
	l.WithFields(logrus.Fields{
		"component":   component,
		"duration_ms": duration,
		"type":        "shutdown",
	}).Info("Component shutdown")
}

// LogHealth logs health check events
func (l *Logger) LogHealth(component string, healthy bool, details string, duration int64) {
	level := logrus.InfoLevel
	if !healthy {
		level = logrus.ErrorLevel
	}

	l.WithFields(logrus.Fields{
		"component":   component,
		"healthy":     healthy,
		"details":     details,
		"duration_ms": duration,
		"type":        "health",
	}).Log(level, "Health check")
}

// LogMetrics logs metrics and performance data
func (l *Logger) LogMetrics(metrics map[string]interface{}) {
	l.WithFields(logrus.Fields{
		"metrics": metrics,
		"type":    "metrics",
	}).Info("Performance metrics")
}

// LogSystem logs system-related events
func (l *Logger) LogSystem(event, component, userID, details string) {
	l.WithFields(logrus.Fields{
		"event":     event,
		"component": component,
		"user_id":   userID,
		"details":   details,
		"type":      "system",
	}).Info("System event")
}
