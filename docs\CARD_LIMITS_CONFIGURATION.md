# Card Limits Configuration

## Overview

The wallet platform provides a comprehensive card limits configuration system that allows administrators to set different spending limits, transaction limits, and card creation restrictions for various card types and environments.

## Configuration Methods

### **1. Environment Variables**
Card limits can be configured using environment variables for maximum flexibility:

```bash
# Standard Card Limits
CARD_LIMITS_STANDARD_SPENDING_LIMIT=5000.00
CARD_LIMITS_STANDARD_DAILY_SPENDING_LIMIT=1000.00
CARD_LIMITS_STANDARD_MONTHLY_SPENDING_LIMIT=10000.00
CARD_LIMITS_STANDARD_MAX_CARDS_PER_WALLET=3

# Premium Card Limits
CARD_LIMITS_PREMIUM_SPENDING_LIMIT=15000.00
CARD_LIMITS_PREMIUM_DAILY_SPENDING_LIMIT=3000.00
CARD_LIMITS_PREMIUM_MONTHLY_SPENDING_LIMIT=30000.00
CARD_LIMITS_PREMIUM_MAX_CARDS_PER_WALLET=5

# Business Card Limits
CARD_LIMITS_BUSINESS_SPENDING_LIMIT=50000.00
CARD_LIMITS_BUSINESS_DAILY_SPENDING_LIMIT=10000.00
CARD_LIMITS_BUSINESS_MONTHLY_SPENDING_LIMIT=100000.00
CARD_LIMITS_BUSINESS_MAX_CARDS_PER_WALLET=10

# Card Creation Limits
CARD_CREATION_MAX_CARDS_PER_DAY=3
CARD_CREATION_COOLDOWN_HOURS=24

# Transaction Limits
CARD_TRANSACTION_MIN_AMOUNT=1.00
CARD_TRANSACTION_MAX_AMOUNT_STANDARD=5000.00
CARD_TRANSACTION_MAX_AMOUNT_PREMIUM=15000.00
CARD_TRANSACTION_MAX_AMOUNT_BUSINESS=50000.00

# ATM Withdrawal Limits
CARD_ATM_DAILY_LIMIT_STANDARD=2000.00
CARD_ATM_DAILY_LIMIT_PREMIUM=5000.00
CARD_ATM_DAILY_LIMIT_BUSINESS=10000.00
CARD_ATM_MONTHLY_LIMIT_STANDARD=20000.00
CARD_ATM_MONTHLY_LIMIT_PREMIUM=50000.00
CARD_ATM_MONTHLY_LIMIT_BUSINESS=100000.00
```

### **2. YAML Configuration Files**
Environment-specific configuration files in the `configs/` directory:

#### **Production Limits** (`configs/config.production.yaml`)
```yaml
card_limits:
  standard:
    spending_limit: 5000.00        # Per transaction limit
    daily_spending_limit: 1000.00  # Daily spending limit
    monthly_spending_limit: 10000.00  # Monthly spending limit
    max_cards_per_wallet: 3        # Maximum cards per wallet
  premium:
    spending_limit: 15000.00       # Per transaction limit
    daily_spending_limit: 3000.00  # Daily spending limit
    monthly_spending_limit: 30000.00  # Monthly spending limit
    max_cards_per_wallet: 5        # Maximum cards per wallet
  business:
    spending_limit: 50000.00       # Per transaction limit
    daily_spending_limit: 10000.00 # Daily spending limit
    monthly_spending_limit: 100000.00  # Monthly spending limit
    max_cards_per_wallet: 10       # Maximum cards per wallet
  creation:
    max_cards_per_day: 3           # Maximum cards created per day
    cooldown_hours: 24             # Hours to wait between card creation
  transaction:
    min_amount: 1.00               # Minimum transaction amount
    max_amount_standard: 5000.00   # Maximum transaction for standard cards
    max_amount_premium: 15000.00   # Maximum transaction for premium cards
    max_amount_business: 50000.00  # Maximum transaction for business cards
  atm:
    daily_limit_standard: 2000.00    # Daily ATM limit for standard cards
    daily_limit_premium: 5000.00     # Daily ATM limit for premium cards
    daily_limit_business: 10000.00   # Daily ATM limit for business cards
    monthly_limit_standard: 20000.00 # Monthly ATM limit for standard cards
    monthly_limit_premium: 50000.00  # Monthly ATM limit for premium cards
    monthly_limit_business: 100000.00 # Monthly ATM limit for business cards
```

#### **Development Limits** (`configs/config.development.yaml`)
```yaml
card_limits:
  standard:
    spending_limit: 10000.00       # Higher for testing
    daily_spending_limit: 5000.00  # Higher for testing
    monthly_spending_limit: 50000.00  # Higher for testing
    max_cards_per_wallet: 5        # More cards for testing
  premium:
    spending_limit: 25000.00       # Higher for testing
    daily_spending_limit: 10000.00 # Higher for testing
    monthly_spending_limit: 100000.00  # Higher for testing
    max_cards_per_wallet: 8        # More cards for testing
  business:
    spending_limit: 100000.00      # Higher for testing
    daily_spending_limit: 50000.00 # Higher for testing
    monthly_spending_limit: 500000.00  # Higher for testing
    max_cards_per_wallet: 15       # More cards for testing
  creation:
    max_cards_per_day: 10          # More cards per day for testing
    cooldown_hours: 1              # Shorter cooldown for testing
  transaction:
    min_amount: 0.10               # Lower minimum for testing
    max_amount_standard: 10000.00  # Higher for testing
    max_amount_premium: 25000.00   # Higher for testing
    max_amount_business: 100000.00 # Higher for testing
  atm:
    daily_limit_standard: 10000.00   # Higher for testing
    daily_limit_premium: 25000.00    # Higher for testing
    daily_limit_business: 50000.00   # Higher for testing
    monthly_limit_standard: 100000.00 # Higher for testing
    monthly_limit_premium: 250000.00  # Higher for testing
    monthly_limit_business: 500000.00 # Higher for testing
```

## Card Limit Types

### **1. Spending Limits**
Controls how much can be spent per transaction, day, and month:

#### **Per Transaction Limits**
- **Standard Cards**: 5,000 SZL per transaction
- **Premium Cards**: 15,000 SZL per transaction
- **Business Cards**: 50,000 SZL per transaction

#### **Daily Spending Limits**
- **Standard Cards**: 1,000 SZL per day
- **Premium Cards**: 3,000 SZL per day
- **Business Cards**: 10,000 SZL per day

#### **Monthly Spending Limits**
- **Standard Cards**: 10,000 SZL per month
- **Premium Cards**: 30,000 SZL per month
- **Business Cards**: 100,000 SZL per month

### **2. Card Creation Limits**
Controls how many cards can be created and when:

#### **Cards Per Wallet**
- **Standard Cards**: Maximum 3 per wallet
- **Premium Cards**: Maximum 5 per wallet
- **Business Cards**: Maximum 10 per wallet

#### **Creation Rate Limits**
- **Maximum Cards Per Day**: 3 cards per wallet per day
- **Cooldown Period**: 24 hours between card creation attempts

### **3. Transaction Limits**
Controls transaction amount boundaries:

#### **Minimum Transaction Amount**
- **All Card Types**: 1.00 SZL minimum

#### **Maximum Transaction Amounts**
- **Standard Cards**: 5,000 SZL maximum
- **Premium Cards**: 15,000 SZL maximum
- **Business Cards**: 50,000 SZL maximum

### **4. ATM Withdrawal Limits**
Controls ATM cash withdrawal limits:

#### **Daily ATM Limits**
- **Standard Cards**: 2,000 SZL per day
- **Premium Cards**: 5,000 SZL per day
- **Business Cards**: 10,000 SZL per day

#### **Monthly ATM Limits**
- **Standard Cards**: 20,000 SZL per month
- **Premium Cards**: 50,000 SZL per month
- **Business Cards**: 100,000 SZL per month

## Implementation Details

### **Configuration Structure**
```go
type CardLimitsConfig struct {
    Standard struct {
        SpendingLimit        float64 `mapstructure:"spending_limit"`
        DailySpendingLimit   float64 `mapstructure:"daily_spending_limit"`
        MonthlySpendingLimit float64 `mapstructure:"monthly_spending_limit"`
        MaxCardsPerWallet    int     `mapstructure:"max_cards_per_wallet"`
    } `mapstructure:"standard"`
    Premium struct {
        SpendingLimit        float64 `mapstructure:"spending_limit"`
        DailySpendingLimit   float64 `mapstructure:"daily_spending_limit"`
        MonthlySpendingLimit float64 `mapstructure:"monthly_spending_limit"`
        MaxCardsPerWallet    int     `mapstructure:"max_cards_per_wallet"`
    } `mapstructure:"premium"`
    Business struct {
        SpendingLimit        float64 `mapstructure:"spending_limit"`
        DailySpendingLimit   float64 `mapstructure:"daily_spending_limit"`
        MonthlySpendingLimit float64 `mapstructure:"monthly_spending_limit"`
        MaxCardsPerWallet    int     `mapstructure:"max_cards_per_wallet"`
    } `mapstructure:"business"`
    Creation struct {
        MaxCardsPerDay int `mapstructure:"max_cards_per_day"`
        CooldownHours  int `mapstructure:"cooldown_hours"`
    } `mapstructure:"creation"`
    Transaction struct {
        MinAmount         float64 `mapstructure:"min_amount"`
        MaxAmountStandard float64 `mapstructure:"max_amount_standard"`
        MaxAmountPremium  float64 `mapstructure:"max_amount_premium"`
        MaxAmountBusiness float64 `mapstructure:"max_amount_business"`
    } `mapstructure:"transaction"`
    ATM struct {
        DailyLimitStandard   float64 `mapstructure:"daily_limit_standard"`
        DailyLimitPremium    float64 `mapstructure:"daily_limit_premium"`
        DailyLimitBusiness   float64 `mapstructure:"daily_limit_business"`
        MonthlyLimitStandard float64 `mapstructure:"monthly_limit_standard"`
        MonthlyLimitPremium  float64 `mapstructure:"monthly_limit_premium"`
        MonthlyLimitBusiness float64 `mapstructure:"monthly_limit_business"`
    } `mapstructure:"atm"`
}
```

### **Limit Enforcement Examples**

#### **Card Creation Validation**
```go
func (s *PayCardService) validateCardCreation(walletID uint, cardType string) error {
    // Check maximum cards per wallet
    maxCards := s.getMaxCardsForType(cardType)
    if cardCount >= int64(maxCards) {
        return fmt.Errorf("maximum number of %s cards (%d) reached", cardType, maxCards)
    }

    // Check rapid creation limits
    cooldownHours := s.config.CardLimits.Creation.CooldownHours
    maxCardsPerDay := s.config.CardLimits.Creation.MaxCardsPerDay
    
    if recentCards >= int64(maxCardsPerDay) {
        return fmt.Errorf("too many cards created recently. Please wait %d hours", cooldownHours)
    }
}
```

#### **Spending Limit Validation**
```go
func (s *PayCardService) getCardLimitsByType(cardType string, wallet *models.Wallet) CardLimits {
    switch cardType {
    case "standard":
        return CardLimits{
            SpendingLimit:        s.config.CardLimits.Standard.SpendingLimit,
            DailySpendingLimit:   s.config.CardLimits.Standard.DailySpendingLimit,
            MonthlySpendingLimit: s.config.CardLimits.Standard.MonthlySpendingLimit,
        }
    // ... other card types
    }
}
```

## Environment-Specific Settings

### **Production Environment**
- **Conservative Limits**: Lower limits for security and compliance
- **Strict Creation Rules**: Limited card creation to prevent abuse
- **Regulatory Compliance**: Limits aligned with financial regulations
- **Risk Management**: Balanced limits for fraud prevention

### **Development Environment**
- **Higher Limits**: Increased limits for easier testing
- **Relaxed Creation Rules**: More cards and shorter cooldowns
- **Testing Convenience**: Lower minimums and higher maximums
- **Rapid Iteration**: Faster card creation for development workflows

### **Staging Environment**
- **Production-like Limits**: Similar to production for realistic testing
- **Validation Testing**: Comprehensive limit validation testing
- **Performance Testing**: Load testing with realistic limits
- **Integration Testing**: End-to-end testing with production-like constraints

## Security Considerations

### **Fraud Prevention**
1. **Velocity Checks**: Rapid card creation detection
2. **Amount Limits**: Transaction amount boundaries
3. **Time-based Restrictions**: Cooldown periods between operations
4. **Pattern Detection**: Unusual spending pattern identification

### **Compliance Requirements**
1. **Regulatory Limits**: Alignment with financial regulations
2. **AML Compliance**: Anti-money laundering transaction limits
3. **KYC Requirements**: Know Your Customer verification thresholds
4. **Audit Trail**: Complete logging of limit enforcement

### **Risk Management**
1. **Graduated Limits**: Different limits for different card types
2. **Dynamic Adjustment**: Ability to adjust limits based on risk assessment
3. **Emergency Controls**: Ability to quickly reduce limits during incidents
4. **Monitoring Alerts**: Real-time alerts for limit violations

## Best Practices

### **Limit Configuration**
1. **Business Alignment**: Limits should align with business requirements
2. **Risk Assessment**: Regular review and adjustment based on risk analysis
3. **User Experience**: Balance security with user convenience
4. **Regulatory Compliance**: Ensure limits meet regulatory requirements

### **Environment Management**
1. **Consistent Structure**: Same configuration structure across environments
2. **Appropriate Scaling**: Different limits for different environments
3. **Testing Validation**: Comprehensive testing of limit enforcement
4. **Documentation**: Clear documentation of limit rationale

### **Monitoring & Alerting**
1. **Limit Violations**: Real-time monitoring of limit breaches
2. **Usage Patterns**: Analysis of limit utilization patterns
3. **Performance Impact**: Monitor performance impact of limit checks
4. **Business Metrics**: Track business impact of limit changes

## Troubleshooting

### **Common Issues**

#### **Card Creation Failures**
- Check maximum cards per wallet limits
- Verify cooldown period hasn't expired
- Validate card type configuration
- Review recent card creation history

#### **Transaction Rejections**
- Verify transaction amount against limits
- Check daily/monthly spending totals
- Validate card type limits
- Review ATM withdrawal limits

#### **Configuration Loading Issues**
- Check environment variable names and values
- Verify YAML file syntax and structure
- Ensure configuration files are in correct directory
- Review application logs for configuration errors

### **Debugging Commands**

#### **Check Current Limits**
```bash
# View current card limits configuration
curl -H "X-Admin-Key: your_admin_key" \
     http://localhost:8086/api/v1/admin/config/card-limits
```

#### **Validate Limit Configuration**
```bash
# Test limit validation with specific values
go run scripts/test-limits.go --card-type=standard --amount=1000
```

#### **Monitor Limit Usage**
```bash
# Check limit utilization for specific wallet
curl -H "X-Admin-Key: your_admin_key" \
     http://localhost:8086/api/v1/admin/wallets/{wallet_id}/limits
```

## Future Enhancements

### **Planned Features**
1. **Dynamic Limits**: Real-time limit adjustments based on risk scoring
2. **User-Specific Limits**: Custom limits for individual users
3. **Time-based Limits**: Different limits for different times of day/week
4. **Geographic Limits**: Location-based transaction limits
5. **Merchant Category Limits**: Limits based on merchant categories

### **API Enhancements**
1. **Limit Management API**: RESTful API for limit configuration
2. **Limit Preview API**: Preview limits before applying changes
3. **Limit History API**: Historical limit configuration tracking
4. **Limit Analytics API**: Detailed limit utilization analytics

## Support

For card limits configuration support:
1. Check this documentation first
2. Review application logs for limit enforcement errors
3. Test limit changes in development environment
4. Contact system administrators for production limit changes
5. Review business requirements for limit adjustments
