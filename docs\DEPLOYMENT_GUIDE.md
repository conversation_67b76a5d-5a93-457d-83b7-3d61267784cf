# Wallet Platform Deployment Guide

## Overview

This guide covers the deployment of the standalone Wallet Platform, including infrastructure setup, configuration, and monitoring.

## Prerequisites

### System Requirements
- **Operating System**: Linux (Ubuntu 20.04+ recommended) or Windows Server 2019+
- **Memory**: Minimum 4GB RAM, Recommended 8GB+
- **CPU**: Minimum 2 cores, Recommended 4+ cores
- **Storage**: Minimum 50GB SSD
- **Network**: Stable internet connection with HTTPS support

### Dependencies
- **Go**: Version 1.19 or higher
- **Database**: MySQL 8.0+
- **Redis**: Version 6.0+ (for caching and rate limiting)
- **SSL Certificate**: For HTTPS endpoints

## Installation

### 1. Clone Repository
```bash
git clone <repository-url>
cd wallet-platform
```

### 2. Build Application
```bash
# Install dependencies
go mod download

# Build the application
go build -o bin/wallet-platform cmd/server/main.go

# Make executable (Linux/Mac)
chmod +x bin/wallet-platform
```

### 3. Database Setup

#### MySQL Setup
```sql
-- Create database
CREATE DATABASE wallet_platform;

-- Create user
CREATE USER 'wallet_user'@'localhost' IDENTIFIED BY 'secure_password';

-- Grant permissions
GRANT ALL PRIVILEGES ON wallet_platform.* TO 'wallet_user'@'localhost';
FLUSH PRIVILEGES;
```

### 4. Redis Setup
```bash
# Install Redis (Ubuntu)
sudo apt update
sudo apt install redis-server

# Start Redis
sudo systemctl start redis-server
sudo systemctl enable redis-server

# Verify installation
redis-cli ping
```

## Configuration

### 1. Environment Configuration

Create a `.env` file in the project root:

```env
# Server Configuration
PORT=8086
HOST=0.0.0.0
ENV=production

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=wallet_platform
DB_USER=wallet_user
DB_PASSWORD=secure_password
DB_SSL_MODE=require

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Security Configuration
JWT_SECRET=your-super-secure-jwt-secret-key
API_KEY_SECRET=your-api-key-secret
ENCRYPTION_KEY=32-character-encryption-key

# External Services
WEBHOOK_SECRET=webhook-verification-secret
RATE_LIMIT_ENABLED=true

# Communications
CENTURION_SMS_API_KEY=your-centurion-sms-api-key
CENTURION_EMAIL_API_KEY=your-centurion-email-api-key

# Logging
LOG_LEVEL=info
LOG_FORMAT=json

# Monitoring
METRICS_ENABLED=true
HEALTH_CHECK_ENABLED=true
```

### 2. Configuration File

Create `config/config.yaml`:

```yaml
server:
  port: 8086
  host: "0.0.0.0"
  read_timeout: 30s
  write_timeout: 30s
  idle_timeout: 120s

database:
  host: "localhost"
  port: 3306
  name: "wallet_platform"
  user: "wallet_user"
  password: "secure_password"
  charset: "utf8mb4"
  parse_time: true
  ssl_mode: "require"
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: 300s

redis:
  host: "localhost"
  port: 6379
  password: ""
  db: 0
  pool_size: 10

security:
  jwt_secret: "your-super-secure-jwt-secret-key"
  api_key_secret: "your-api-key-secret"
  encryption_key: "32-character-encryption-key"
  rate_limit:
    enabled: true
    requests_per_minute: 100

logging:
  level: "info"
  format: "json"
  file: "/var/log/wallet-platform/app.log"

monitoring:
  metrics_enabled: true
  health_check_enabled: true
  prometheus_enabled: true
```

## Deployment Options

### 1. Direct Deployment

```bash
# Create service user
sudo useradd -r -s /bin/false wallet-platform

# Create directories
sudo mkdir -p /opt/wallet-platform
sudo mkdir -p /var/log/wallet-platform
sudo mkdir -p /etc/wallet-platform

# Copy files
sudo cp bin/wallet-platform /opt/wallet-platform/
sudo cp config/config.yaml /etc/wallet-platform/
sudo chown -R wallet-platform:wallet-platform /opt/wallet-platform
sudo chown -R wallet-platform:wallet-platform /var/log/wallet-platform

# Create systemd service
sudo tee /etc/systemd/system/wallet-platform.service > /dev/null <<EOF
[Unit]
Description=Wallet Platform Service
After=network.target postgresql.service redis.service

[Service]
Type=simple
User=wallet-platform
Group=wallet-platform
WorkingDirectory=/opt/wallet-platform
ExecStart=/opt/wallet-platform/wallet-platform
Restart=always
RestartSec=5
Environment=CONFIG_PATH=/etc/wallet-platform/config.yaml

[Install]
WantedBy=multi-user.target
EOF

# Enable and start service
sudo systemctl daemon-reload
sudo systemctl enable wallet-platform
sudo systemctl start wallet-platform
```

### 2. Docker Deployment

#### Dockerfile
```dockerfile
FROM golang:1.19-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN go build -o bin/wallet-platform cmd/server/main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/

COPY --from=builder /app/bin/wallet-platform .
COPY --from=builder /app/config ./config

EXPOSE 8086
CMD ["./wallet-platform"]
```

#### Docker Compose
```yaml
version: '3.8'

services:
  wallet-platform:
    build: .
    ports:
      - "8086:8086"
    environment:
      - DB_HOST=postgres
      - REDIS_HOST=redis
    depends_on:
      - postgres
      - redis
    volumes:
      - ./config:/app/config
      - ./logs:/var/log/wallet-platform

  postgres:
    image: postgres:13
    environment:
      POSTGRES_DB: wallet_platform
      POSTGRES_USER: wallet_user
      POSTGRES_PASSWORD: secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
```

### 3. Kubernetes Deployment

#### Deployment YAML
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: wallet-platform
spec:
  replicas: 3
  selector:
    matchLabels:
      app: wallet-platform
  template:
    metadata:
      labels:
        app: wallet-platform
    spec:
      containers:
      - name: wallet-platform
        image: wallet-platform:latest
        ports:
        - containerPort: 8086
        env:
        - name: DB_HOST
          value: "postgres-service"
        - name: REDIS_HOST
          value: "redis-service"
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
---
apiVersion: v1
kind: Service
metadata:
  name: wallet-platform-service
spec:
  selector:
    app: wallet-platform
  ports:
  - port: 80
    targetPort: 8086
  type: LoadBalancer
```

## SSL/TLS Configuration

### 1. Nginx Reverse Proxy

```nginx
server {
    listen 80;
    server_name wallet-api.yourcompany.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name wallet-api.yourcompany.com;

    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;

    location / {
        proxy_pass http://localhost:8086;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## Monitoring and Logging

### 1. Health Checks

The platform provides health check endpoints:

```bash
# Basic health check
curl http://localhost:8086/health

# Detailed health check
curl http://localhost:8086/health/detailed
```

### 2. Metrics

Prometheus metrics are available at:
```
http://localhost:8086/metrics
```

### 3. Log Management

Configure log rotation:

```bash
# Create logrotate configuration
sudo tee /etc/logrotate.d/wallet-platform > /dev/null <<EOF
/var/log/wallet-platform/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 wallet-platform wallet-platform
    postrotate
        systemctl reload wallet-platform
    endscript
}
EOF
```

## Security Considerations

### 1. Firewall Configuration
```bash
# Allow only necessary ports
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw enable
```

### 2. Database Security
- Use strong passwords
- Enable SSL connections
- Restrict database access to application servers only
- Regular security updates

### 3. Application Security
- Keep dependencies updated
- Use environment variables for secrets
- Enable rate limiting
- Implement proper logging and monitoring

## Backup and Recovery

### 1. Database Backup
```bash
# PostgreSQL backup
pg_dump -h localhost -U wallet_user wallet_platform > backup_$(date +%Y%m%d_%H%M%S).sql

# MySQL backup
mysqldump -u wallet_user -p wallet_platform > backup_$(date +%Y%m%d_%H%M%S).sql
```

### 2. Automated Backup Script
```bash
#!/bin/bash
BACKUP_DIR="/backups/wallet-platform"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p $BACKUP_DIR

# Database backup
pg_dump -h localhost -U wallet_user wallet_platform > $BACKUP_DIR/db_backup_$DATE.sql

# Compress backup
gzip $BACKUP_DIR/db_backup_$DATE.sql

# Remove backups older than 30 days
find $BACKUP_DIR -name "*.gz" -mtime +30 -delete
```

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check database credentials
   - Verify database server is running
   - Check network connectivity

2. **Redis Connection Failed**
   - Verify Redis server is running
   - Check Redis configuration
   - Verify network connectivity

3. **High Memory Usage**
   - Check for memory leaks
   - Adjust connection pool sizes
   - Monitor garbage collection

4. **Performance Issues**
   - Check database query performance
   - Monitor Redis cache hit rates
   - Review application logs

### Log Analysis
```bash
# View application logs
sudo journalctl -u wallet-platform -f

# Check error logs
grep "ERROR" /var/log/wallet-platform/app.log

# Monitor performance
tail -f /var/log/wallet-platform/app.log | grep "response_time"
```
