package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"os"
	"time"

	"wallet-platform/internal/config"
	"wallet-platform/internal/database"
	"wallet-platform/internal/services"
	"wallet-platform/pkg/logger"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

func main() {
	// Command line flags
	var (
		sourceDB  = flag.String("source", "", "Source database connection string")
		targetDB  = flag.String("target", "", "Target database connection string")
		operation = flag.String("operation", "migrate", "Operation: migrate, validate, rollback, status")
		dryRun    = flag.Bool("dry-run", false, "Perform a dry run without making changes")
		verbose   = flag.Bool("verbose", false, "Enable verbose logging")
	)
	flag.Parse()

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Initialize logger
	logLevel := "info"
	if *verbose {
		logLevel = "debug"
	}
	logger := logger.NewLogger(logLevel, "json")

	// Connect to source database (main payment engine)
	var sourceDatabase *gorm.DB
	if *sourceDB != "" {
		sourceDatabase, err = gorm.Open(mysql.Open(*sourceDB), &gorm.Config{})
		if err != nil {
			log.Fatalf("Failed to connect to source database: %v", err)
		}
	} else {
		// Use default source connection from config
		sourceDSN := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
			cfg.Database.Username, cfg.Database.Password, cfg.Database.Host, cfg.Database.Port, cfg.Database.Database)
		sourceDatabase, err = gorm.Open(mysql.Open(sourceDSN), &gorm.Config{})
		if err != nil {
			log.Fatalf("Failed to connect to source database: %v", err)
		}
	}

	// Connect to target database (standalone wallet platform)
	var targetDatabase *gorm.DB
	if *targetDB != "" {
		targetDatabase, err = gorm.Open(mysql.Open(*targetDB), &gorm.Config{})
		if err != nil {
			log.Fatalf("Failed to connect to target database: %v", err)
		}
	} else {
		// Initialize target database using the platform's database setup
		targetDatabase, err = database.Initialize(cfg.Database)
		if err != nil {
			log.Fatalf("Failed to initialize target database: %v", err)
		}
	}

	// Create migration service
	migrationService := services.NewMigrationService(sourceDatabase, targetDatabase, logger)

	// Execute operation
	ctx := context.Background()
	switch *operation {
	case "migrate":
		if *dryRun {
			fmt.Println("DRY RUN: Would perform complete migration")
			printMigrationPlan()
		} else {
			fmt.Println("Starting complete data migration...")
			result, err := migrationService.MigrateAllData(ctx)
			if err != nil {
				log.Fatalf("Migration failed: %v", err)
			}
			printMigrationResult(result)
		}

	case "migrate-wallets":
		if *dryRun {
			fmt.Println("DRY RUN: Would migrate wallet data")
		} else {
			fmt.Println("Starting wallet migration...")
			result, err := migrationService.MigrateDigitalWallets(ctx)
			if err != nil {
				log.Fatalf("Wallet migration failed: %v", err)
			}
			printMigrationResult(result)
		}

	case "migrate-cards":
		if *dryRun {
			fmt.Println("DRY RUN: Would migrate paycard data")
		} else {
			fmt.Println("Starting paycard migration...")
			result, err := migrationService.MigratePayCards(ctx)
			if err != nil {
				log.Fatalf("PayCard migration failed: %v", err)
			}
			printMigrationResult(result)
		}

	case "migrate-transactions":
		if *dryRun {
			fmt.Println("DRY RUN: Would migrate transaction data")
		} else {
			fmt.Println("Starting transaction migration...")
			result, err := migrationService.MigrateWalletTransactions(ctx)
			if err != nil {
				log.Fatalf("Transaction migration failed: %v", err)
			}
			printMigrationResult(result)
		}

	case "validate":
		fmt.Println("Validating migration...")
		result, err := migrationService.ValidateMigration(ctx)
		if err != nil {
			log.Fatalf("Validation failed: %v", err)
		}
		printValidationResult(result)

	case "rollback":
		if *dryRun {
			fmt.Println("DRY RUN: Would rollback migration")
		} else {
			fmt.Print("Are you sure you want to rollback the migration? This will delete all migrated data. (y/N): ")
			var confirm string
			fmt.Scanln(&confirm)
			if confirm != "y" && confirm != "Y" {
				fmt.Println("Rollback cancelled")
				return
			}
			fmt.Println("Rolling back migration...")
			err := migrationService.RollbackMigration(ctx)
			if err != nil {
				log.Fatalf("Rollback failed: %v", err)
			}
			fmt.Println("Rollback completed successfully")
		}

	case "status":
		fmt.Println("Getting migration status...")
		statuses, err := migrationService.GetMigrationStatus()
		if err != nil {
			log.Fatalf("Failed to get status: %v", err)
		}
		printMigrationStatus(statuses)

	default:
		fmt.Printf("Unknown operation: %s\n", *operation)
		fmt.Println("Available operations: migrate, migrate-wallets, migrate-cards, migrate-transactions, validate, rollback, status")
		os.Exit(1)
	}
}

func printMigrationPlan() {
	fmt.Println("\nMigration Plan:")
	fmt.Println("==============")
	fmt.Println("1. Migrate Digital Wallets")
	fmt.Println("   - Extract wallet data from digital_wallets table")
	fmt.Println("   - Transform to new wallet schema")
	fmt.Println("   - Insert into standalone platform")
	fmt.Println()
	fmt.Println("2. Migrate PayCards")
	fmt.Println("   - Extract paycard data from pay_cards table")
	fmt.Println("   - Transform to new paycard schema")
	fmt.Println("   - Link to migrated wallets")
	fmt.Println()
	fmt.Println("3. Migrate Wallet Transactions")
	fmt.Println("   - Extract transaction data from digital_wallet_transactions table")
	fmt.Println("   - Transform to new transaction schema")
	fmt.Println("   - Link to migrated wallets")
	fmt.Println()
	fmt.Println("4. Migrate Security Data")
	fmt.Println("   - Extract security events and fraud alerts")
	fmt.Println("   - Transform to new security schema")
	fmt.Println()
	fmt.Println("5. Validate Data Integrity")
	fmt.Println("   - Verify record counts match")
	fmt.Println("   - Validate balance totals")
	fmt.Println("   - Check referential integrity")
}

func printMigrationResult(result *services.MigrationResult) {
	fmt.Printf("\nMigration Result: %s\n", result.Type)
	fmt.Println("===================")
	fmt.Printf("Status: %s\n", result.Status)
	fmt.Printf("Duration: %v\n", result.Duration)
	fmt.Printf("Records Total: %d\n", result.RecordsTotal)
	fmt.Printf("Records Processed: %d\n", result.RecordsProcessed)
	fmt.Printf("Records Failed: %d\n", result.RecordsFailed)

	if len(result.Errors) > 0 {
		fmt.Printf("\nErrors (%d):\n", len(result.Errors))
		for i, err := range result.Errors {
			if i < 10 { // Show only first 10 errors
				fmt.Printf("  %d. %s\n", i+1, err)
			}
		}
		if len(result.Errors) > 10 {
			fmt.Printf("  ... and %d more errors\n", len(result.Errors)-10)
		}
	}

	if result.Summary != nil && len(result.Summary) > 0 {
		fmt.Println("\nSummary:")
		for key, value := range result.Summary {
			fmt.Printf("  %s: %v\n", key, value)
		}
	}
}

func printValidationResult(result *services.MigrationResult) {
	fmt.Println("\nValidation Result:")
	fmt.Println("==================")
	fmt.Printf("Status: %s\n", result.Status)

	if result.Summary != nil {
		fmt.Println("\nData Comparison:")
		for key, value := range result.Summary {
			fmt.Printf("  %s: %v\n", key, value)
		}
	}

	if len(result.Errors) > 0 {
		fmt.Printf("\nValidation Errors (%d):\n", len(result.Errors))
		for i, err := range result.Errors {
			fmt.Printf("  %d. %s\n", i+1, err)
		}
	} else {
		fmt.Println("\n✓ All validation checks passed!")
	}
}

func printMigrationStatus(statuses []services.MigrationStatus) {
	fmt.Println("\nMigration Status:")
	fmt.Println("=================")

	if len(statuses) == 0 {
		fmt.Println("No migrations found")
		return
	}

	for _, status := range statuses {
		fmt.Printf("\nMigration: %s\n", status.MigrationType)
		fmt.Printf("  Status: %s\n", status.Status)
		fmt.Printf("  Start Time: %s\n", status.StartTime.Format(time.RFC3339))
		if status.EndTime != nil {
			fmt.Printf("  End Time: %s\n", status.EndTime.Format(time.RFC3339))
			duration := status.EndTime.Sub(status.StartTime)
			fmt.Printf("  Duration: %v\n", duration)
		}
		fmt.Printf("  Records Total: %d\n", status.RecordsTotal)
		fmt.Printf("  Records Processed: %d\n", status.RecordsProcessed)
		fmt.Printf("  Records Failed: %d\n", status.RecordsFailed)

		if status.ErrorMessage != "" {
			fmt.Printf("  Error: %s\n", status.ErrorMessage)
		}
	}
}
