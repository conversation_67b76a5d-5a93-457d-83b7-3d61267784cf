package controllers

import (
	"net/http"
	"strconv"

	"wallet-platform/internal/services"

	"github.com/gin-gonic/gin"
)

// ServiceController handles service-related requests
type ServiceController struct {
	container *services.Container
}

// NewServiceController creates a new service controller
func NewServiceController(container *services.Container) *ServiceController {
	return &ServiceController{
		container: container,
	}
}

// ServiceSubscriptionRequest represents service subscription request payload
type ServiceSubscriptionRequest struct {
	ServiceID    uint   `json:"service_id" binding:"required"`
	BillingCycle string `json:"billing_cycle" binding:"required,oneof=monthly quarterly yearly"`
}

// ServiceUsageRequest represents service usage recording request
type ServiceUsageRequest struct {
	UsageType string `json:"usage_type" binding:"required"`
	Quantity  int    `json:"quantity" binding:"required,min=1"`
}

// SubscriptionUpdateRequest represents subscription update request
type SubscriptionUpdateRequest struct {
	AutoRenew           *bool  `json:"auto_renew"`
	RenewalReminders    *bool  `json:"renewal_reminders"`
	UsageLimit          *int   `json:"usage_limit"`
	NotificationEnabled *bool  `json:"notification_enabled"`
	BillingCycle        string `json:"billing_cycle,omitempty"`
}

// GetAvailableServices retrieves all available services
func (sc *ServiceController) GetAvailableServices(c *gin.Context) {
	services, err := sc.container.ServiceManager.GetAvailableServices()
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "FETCH_FAILED",
			Message: err.Error(),
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    services,
	})
}

// SubscribeToService subscribes a user to a service
func (sc *ServiceController) SubscribeToService(c *gin.Context) {
	var req ServiceSubscriptionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	// Get wallet ID from context
	walletID, exists := c.Get("wallet_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "Authentication required",
			Code:    "401",
		})
		return
	}

	// Subscribe to service
	subscription, err := sc.container.ServiceManager.SubscribeToService(
		walletID.(uint),
		req.ServiceID,
		req.BillingCycle,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "SUBSCRIPTION_FAILED",
			Message: err.Error(),
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "Successfully subscribed to service",
		"data":    subscription,
	})
}

// GetMySubscriptions retrieves user's service subscriptions
func (sc *ServiceController) GetMySubscriptions(c *gin.Context) {
	// Get wallet ID from context
	walletID, exists := c.Get("wallet_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "Authentication required",
			Code:    "401",
		})
		return
	}

	// Get subscriptions
	subscriptions, err := sc.container.ServiceManager.GetSubscriptions(walletID.(uint))
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "FETCH_FAILED",
			Message: err.Error(),
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    subscriptions,
	})
}

// UpdateSubscription updates a service subscription
func (sc *ServiceController) UpdateSubscription(c *gin.Context) {
	// Get subscription ID from URL
	subscriptionIDStr := c.Param("id")
	subscriptionID, err := strconv.ParseUint(subscriptionIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_ID",
			Message: "Invalid subscription ID",
			Code:    "400",
		})
		return
	}

	var req SubscriptionUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	// Convert request to map for service
	updates := make(map[string]interface{})
	if req.AutoRenew != nil {
		updates["auto_renew"] = *req.AutoRenew
	}
	if req.RenewalReminders != nil {
		updates["renewal_reminders"] = *req.RenewalReminders
	}
	if req.UsageLimit != nil {
		updates["usage_limit"] = *req.UsageLimit
	}
	if req.NotificationEnabled != nil {
		updates["notification_enabled"] = *req.NotificationEnabled
	}
	if req.BillingCycle != "" {
		updates["billing_cycle"] = req.BillingCycle
	}

	// Update subscription
	subscription, err := sc.container.ServiceManager.UpdateSubscription(uint(subscriptionID), updates)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "UPDATE_FAILED",
			Message: err.Error(),
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Subscription updated successfully",
		"data":    subscription,
	})
}

// CancelSubscription cancels a service subscription
func (sc *ServiceController) CancelSubscription(c *gin.Context) {
	// Get subscription ID from URL
	subscriptionIDStr := c.Param("id")
	subscriptionID, err := strconv.ParseUint(subscriptionIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_ID",
			Message: "Invalid subscription ID",
			Code:    "400",
		})
		return
	}

	// Get cancellation reason from request body (optional)
	var requestBody struct {
		Reason string `json:"reason"`
	}
	c.ShouldBindJSON(&requestBody)

	reason := requestBody.Reason
	if reason == "" {
		reason = "User requested cancellation"
	}

	// Cancel subscription
	err = sc.container.ServiceManager.CancelSubscription(uint(subscriptionID), reason)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "CANCELLATION_FAILED",
			Message: err.Error(),
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Subscription cancelled successfully",
	})
}

// GetServiceUsage retrieves usage data for a subscription
func (sc *ServiceController) GetServiceUsage(c *gin.Context) {
	// Get subscription ID from URL
	subscriptionIDStr := c.Param("id")
	subscriptionID, err := strconv.ParseUint(subscriptionIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_ID",
			Message: "Invalid subscription ID",
			Code:    "400",
		})
		return
	}

	// Get query parameters for filtering
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")
	usageType := c.Query("usage_type")

	// Get service usage from ServiceManager
	usageRecords, err := sc.container.ServiceManager.GetServiceUsage(uint(subscriptionID), startDate, endDate, usageType)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "INTERNAL_ERROR",
			Message: "Failed to retrieve service usage: " + err.Error(),
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Service usage retrieved successfully",
		"data":    usageRecords,
	})
}

// RecordServiceUsage records usage for a service subscription
func (sc *ServiceController) RecordServiceUsage(c *gin.Context) {
	// Get subscription ID from URL
	subscriptionIDStr := c.Param("id")
	subscriptionID, err := strconv.ParseUint(subscriptionIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_ID",
			Message: "Invalid subscription ID",
			Code:    "400",
		})
		return
	}

	var req ServiceUsageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	// Record service usage
	err = sc.container.ServiceManager.RecordServiceUsage(
		uint(subscriptionID),
		req.UsageType,
		req.Quantity,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "USAGE_RECORDING_FAILED",
			Message: err.Error(),
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "Service usage recorded successfully",
	})
}
