package middleware

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"

	"wallet-platform/internal/config"
	"wallet-platform/pkg/logger"
	"wallet-platform/pkg/redis"

	"github.com/gin-gonic/gin"
)

// Logger middleware for request logging
func Logger(log *logger.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		raw := c.Request.URL.RawQuery

		// Process request
		c.Next()

		// Calculate latency
		latency := time.Since(start)
		clientIP := c.ClientIP()
		method := c.Request.Method
		statusCode := c.Writer.Status()
		userAgent := c.Request.UserAgent()

		if raw != "" {
			path = path + "?" + raw
		}

		// Check if logger is nil before using it
		if log != nil {
			log.LogRequest(method, path, userAgent, clientIP, statusCode, latency.Milliseconds())
		} else {
			// Fallback logging to stdout
			fmt.Printf("REQUEST: %s %s - %d (%dms) from %s\n",
				method, path, statusCode, latency.Milliseconds(), clientIP)
		}
	}
}

// Recovery middleware for panic recovery
func Recovery(log *logger.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				// Safely convert panic to error
				var errorMsg string
				var actualError error

				switch e := err.(type) {
				case error:
					actualError = e
					errorMsg = e.Error()
				case string:
					errorMsg = e
					actualError = fmt.Errorf(e)
				default:
					errorMsg = fmt.Sprintf("%v", e)
					actualError = fmt.Errorf("%v", e)
				}

				// Enhanced logging with more context
				if log != nil {
					log.LogError(actualError, map[string]interface{}{
						"path":       c.Request.URL.Path,
						"method":     c.Request.Method,
						"ip":         c.ClientIP(),
						"user_agent": c.Request.UserAgent(),
						"panic_type": fmt.Sprintf("%T", err),
						"error_msg":  errorMsg,
					})
				}

				// Also log to stdout for immediate visibility
				fmt.Printf("PANIC RECOVERED: %s %s - %s (Type: %T)\n",
					c.Request.Method, c.Request.URL.Path, errorMsg, err)

				c.JSON(http.StatusInternalServerError, gin.H{
					"error": errorMsg,
					"code":  "INTERNAL_ERROR",
				})
				c.Abort()
			}
		}()
		c.Next()
	}
}

// CORS middleware for cross-origin requests
func CORS(corsConfig config.CORSConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		origin := c.Request.Header.Get("Origin")

		// Check if origin is allowed
		allowed := false
		for _, allowedOrigin := range corsConfig.AllowOrigins {
			if allowedOrigin == "*" || allowedOrigin == origin {
				allowed = true
				break
			}
		}

		if allowed {
			c.Header("Access-Control-Allow-Origin", origin)
		}

		c.Header("Access-Control-Allow-Methods", strings.Join(corsConfig.AllowMethods, ", "))
		c.Header("Access-Control-Allow-Headers", strings.Join(corsConfig.AllowHeaders, ", "))
		c.Header("Access-Control-Expose-Headers", strings.Join(corsConfig.ExposeHeaders, ", "))
		c.Header("Access-Control-Max-Age", strconv.Itoa(corsConfig.MaxAge))

		if corsConfig.AllowCredentials {
			c.Header("Access-Control-Allow-Credentials", "true")
		}

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// Security middleware for security headers
func Security() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("X-Content-Type-Options", "nosniff")
		c.Header("X-Frame-Options", "DENY")
		c.Header("X-XSS-Protection", "1; mode=block")
		c.Header("Referrer-Policy", "strict-origin-when-cross-origin")
		c.Header("Content-Security-Policy", "default-src 'self'")
		c.Next()
	}
}

// RateLimit middleware for rate limiting
func RateLimit(redisClient *redis.Client, rateLimitConfig config.RateLimitConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		if !rateLimitConfig.Enabled {
			c.Next()
			return
		}

		// Get client identifier (IP address)
		clientIP := c.ClientIP()
		endpoint := c.Request.URL.Path

		// Get rate limit for this endpoint
		limit := rateLimitConfig.DefaultLimit
		window := rateLimitConfig.DefaultWindow

		if endpointLimit, exists := rateLimitConfig.EndpointLimits[endpoint]; exists {
			limit = endpointLimit.Limit
			window = endpointLimit.Window
		}

		// Create Redis key
		key := "rate_limit:" + clientIP + ":" + endpoint

		// Check if Redis client is available
		if redisClient == nil {
			// If Redis client is nil, allow the request
			c.Next()
			return
		}

		// Check current count
		count, err := redisClient.IncrementWithExpiration(c.Request.Context(), key, time.Duration(window)*time.Second)
		if err != nil {
			// If Redis is down, allow the request
			c.Next()
			return
		}

		// Check if limit exceeded
		if count > int64(limit) {
			c.JSON(http.StatusTooManyRequests, gin.H{
				"error":       "Rate limit exceeded",
				"code":        "RATE_LIMIT_EXCEEDED",
				"retry_after": window,
			})
			c.Abort()
			return
		}

		// Add rate limit headers
		c.Header("X-RateLimit-Limit", strconv.Itoa(limit))
		c.Header("X-RateLimit-Remaining", strconv.FormatInt(int64(limit)-count, 10))
		c.Header("X-RateLimit-Reset", strconv.FormatInt(time.Now().Add(time.Duration(window)*time.Second).Unix(), 10))

		c.Next()
	}
}

// Auth middleware for JWT authentication
func Auth() gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Authorization header required",
				"code":  "MISSING_AUTH_HEADER",
			})
			c.Abort()
			return
		}

		// Check Bearer token format
		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Invalid authorization header format",
				"code":  "INVALID_AUTH_FORMAT",
			})
			c.Abort()
			return
		}

		token := tokenParts[1]

		// TODO: Implement JWT token validation
		// For now, we'll just check if token is not empty
		if token == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Invalid token",
				"code":  "INVALID_TOKEN",
			})
			c.Abort()
			return
		}

		// Set user context (placeholder)
		c.Set("user_id", "placeholder_user_id")
		c.Set("wallet_id", "placeholder_wallet_id")

		c.Next()
	}
}

// AdminAuth middleware for admin authentication
func AdminAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Check for admin API key first (for service-to-service admin operations)
		adminKey := c.GetHeader("X-Admin-Key")
		if adminKey != "" {
			expectedAdminKey := os.Getenv("ADMIN_API_KEY")
			if expectedAdminKey == "" {
				c.JSON(http.StatusInternalServerError, gin.H{
					"error": "Admin API key not configured",
					"code":  "ADMIN_KEY_NOT_CONFIGURED",
				})
				c.Abort()
				return
			}

			if adminKey != expectedAdminKey {
				c.JSON(http.StatusUnauthorized, gin.H{
					"error": "Invalid admin API key",
					"code":  "INVALID_ADMIN_KEY",
				})
				c.Abort()
				return
			}

			// Set admin context
			c.Set("auth_type", "admin")
			c.Set("is_admin", true)
			c.Set("admin_key_auth", true)
			c.Next()
			return
		}

		// Check for regular user authentication with admin role
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Authentication required - provide either Authorization header or X-Admin-Key",
				"code":  "MISSING_AUTHENTICATION",
			})
			c.Abort()
			return
		}

		// Check Bearer token format
		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Invalid authorization header format",
				"code":  "INVALID_AUTH_FORMAT",
			})
			c.Abort()
			return
		}

		token := tokenParts[1]
		if token == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Invalid token",
				"code":  "INVALID_TOKEN",
			})
			c.Abort()
			return
		}

		// For production: implement JWT validation and admin role check
		// For now, allow any valid token with admin role header
		adminRole := c.GetHeader("X-Admin-Role")
		if adminRole != "admin" && adminRole != "super_admin" {
			c.JSON(http.StatusForbidden, gin.H{
				"error": "Admin access required",
				"code":  "INSUFFICIENT_PERMISSIONS",
			})
			c.Abort()
			return
		}

		// Set admin context
		c.Set("auth_type", "admin")
		c.Set("is_admin", true)
		c.Set("admin_role", adminRole)
		c.Set("user_id", "admin_user") // Placeholder
		c.Next()
	}
}

// RequestID middleware for request tracking
func RequestID() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID := c.GetHeader("X-Request-ID")
		if requestID == "" {
			requestID = generateRequestID()
		}

		c.Set("request_id", requestID)
		c.Header("X-Request-ID", requestID)
		c.Next()
	}
}

// ContentType middleware for content type validation
func ContentType(allowedTypes ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		if c.Request.Method == "POST" || c.Request.Method == "PUT" || c.Request.Method == "PATCH" {
			contentType := c.GetHeader("Content-Type")

			allowed := false
			for _, allowedType := range allowedTypes {
				if strings.Contains(contentType, allowedType) {
					allowed = true
					break
				}
			}

			if !allowed {
				c.JSON(http.StatusUnsupportedMediaType, gin.H{
					"error":         "Unsupported content type",
					"code":          "UNSUPPORTED_CONTENT_TYPE",
					"allowed_types": allowedTypes,
				})
				c.Abort()
				return
			}
		}

		c.Next()
	}
}

// Timeout middleware for request timeout
func Timeout(timeout time.Duration) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Create a context with timeout
		ctx, cancel := context.WithTimeout(c.Request.Context(), timeout)
		defer cancel()

		// Replace the request context
		c.Request = c.Request.WithContext(ctx)

		// Channel to signal completion
		done := make(chan struct{})

		go func() {
			defer close(done)
			c.Next()
		}()

		select {
		case <-done:
			// Request completed normally
		case <-ctx.Done():
			// Request timed out
			c.JSON(http.StatusRequestTimeout, gin.H{
				"error": "Request timeout",
				"code":  "REQUEST_TIMEOUT",
			})
			c.Abort()
		}
	}
}

// generateRequestID generates a unique request ID
func generateRequestID() string {
	// Simple implementation - in production, use UUID or similar
	return strconv.FormatInt(time.Now().UnixNano(), 36)
}

// InternalAuth middleware for service-to-service authentication
// This allows other services to access the wallet platform using an internal API key
func InternalAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		fmt.Printf("MIDDLEWARE DEBUG: InternalAuth called for %s %s\n", c.Request.Method, c.Request.URL.Path)

		// Check for internal API key first
		internalKey := c.GetHeader("X-Internal-Key")
		fmt.Printf("MIDDLEWARE DEBUG: X-Internal-Key header: %s\n", internalKey)
		if internalKey != "" {
			// Validate internal API key
			expectedKey := os.Getenv("INTERNAL_API_KEY")
			if expectedKey == "" {
				c.JSON(http.StatusInternalServerError, gin.H{
					"error": "Internal API key not configured",
					"code":  "INTERNAL_KEY_NOT_CONFIGURED",
				})
				c.Abort()
				return
			}

			if internalKey != expectedKey {
				c.JSON(http.StatusUnauthorized, gin.H{
					"error": "Invalid internal API key",
					"code":  "INVALID_INTERNAL_KEY",
				})
				c.Abort()
				return
			}

			// Set internal service context
			c.Set("auth_type", "internal")
			c.Set("service_name", c.GetHeader("X-Service-Name"))
			c.Set("is_internal", true)

			// For internal requests, we might need user context passed from the calling service
			if userID := c.GetHeader("X-User-ID"); userID != "" {
				c.Set("user_id", userID)
			}
			if walletID := c.GetHeader("X-Wallet-ID"); walletID != "" {
				c.Set("wallet_id", walletID)
			}

			c.Next()
			return
		}

		// If no internal key, fall back to regular authentication
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Authorization required - provide either Authorization header or X-Internal-Key",
				"code":  "MISSING_AUTHENTICATION",
			})
			c.Abort()
			return
		}

		// Check Bearer token format
		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Invalid authorization header format",
				"code":  "INVALID_AUTH_FORMAT",
			})
			c.Abort()
			return
		}

		token := tokenParts[1]

		// TODO: Implement JWT token validation
		// For now, we'll just check if token is not empty
		if token == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Invalid token",
				"code":  "INVALID_TOKEN",
			})
			c.Abort()
			return
		}

		// Set regular user context
		c.Set("auth_type", "user")
		c.Set("user_id", "placeholder_user_id")
		c.Set("wallet_id", "placeholder_wallet_id")
		c.Set("is_internal", false)

		c.Next()
	}
}

// OptionalInternalAuth middleware for endpoints that can work with or without authentication
// This is useful for public endpoints that provide additional features when authenticated
func OptionalInternalAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Check for internal API key first
		internalKey := c.GetHeader("X-Internal-Key")
		if internalKey != "" {
			expectedKey := os.Getenv("INTERNAL_API_KEY")
			if expectedKey != "" && internalKey == expectedKey {
				// Set internal service context
				c.Set("auth_type", "internal")
				c.Set("service_name", c.GetHeader("X-Service-Name"))
				c.Set("is_internal", true)
				c.Set("authenticated", true)

				if userID := c.GetHeader("X-User-ID"); userID != "" {
					c.Set("user_id", userID)
				}
				if walletID := c.GetHeader("X-Wallet-ID"); walletID != "" {
					c.Set("wallet_id", walletID)
				}

				c.Next()
				return
			}
		}

		// Check for regular authentication
		authHeader := c.GetHeader("Authorization")
		if authHeader != "" {
			tokenParts := strings.Split(authHeader, " ")
			if len(tokenParts) == 2 && tokenParts[0] == "Bearer" && tokenParts[1] != "" {
				// Set regular user context
				c.Set("auth_type", "user")
				c.Set("user_id", "placeholder_user_id")
				c.Set("wallet_id", "placeholder_wallet_id")
				c.Set("is_internal", false)
				c.Set("authenticated", true)

				c.Next()
				return
			}
		}

		// No authentication provided - continue as unauthenticated
		c.Set("authenticated", false)
		c.Set("is_internal", false)
		c.Next()
	}
}

// SecurityHeaders adds security headers to all responses
func SecurityHeaders() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Prevent XSS attacks
		c.Header("X-XSS-Protection", "1; mode=block")

		// Prevent MIME type sniffing
		c.Header("X-Content-Type-Options", "nosniff")

		// Prevent clickjacking
		c.Header("X-Frame-Options", "DENY")

		// Enforce HTTPS
		c.Header("Strict-Transport-Security", "max-age=31536000; includeSubDomains")

		// Content Security Policy
		c.Header("Content-Security-Policy", "default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self'; connect-src 'self'; frame-ancestors 'none'")

		// Referrer Policy
		c.Header("Referrer-Policy", "strict-origin-when-cross-origin")

		// Permissions Policy
		c.Header("Permissions-Policy", "geolocation=(), microphone=(), camera=()")

		// Remove server information
		c.Header("Server", "")

		c.Next()
	}
}
