# Staging Environment Configuration
# This file contains staging-specific settings

app:
  name: "Wallet Platform (Staging)"
  version: "1.0.0"
  environment: "staging"
  debug: false

server:
  port: 8086
  host: "0.0.0.0"
  environment: "staging"
  read_timeout: 30
  write_timeout: 30
  idle_timeout: 120
  cors:
    allow_origins:
      - "https://staging-frontend.your-domain.com"
      - "https://staging-admin.your-domain.com"
    allow_methods:
      - "GET"
      - "POST"
      - "PUT"
      - "DELETE"
      - "OPTIONS"
    allow_headers:
      - "Content-Type"
      - "Authorization"
      - "X-Request-ID"
      - "X-2FA-Code"
    allow_credentials: true
    max_age: 86400

database:
  driver: "mysql"
  host: ""      # Set via DATABASE_HOST env var
  port: 3306
  username: ""  # Set via DATABASE_USERNAME env var
  password: ""  # Set via DATABASE_PASSWORD env var
  database: "wallet_platform_staging"
  ssl_mode: "true"  # Use SSL in staging
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: 300
  conn_max_idle_time: 60

redis:
  enabled: true  # Enable for staging testing
  host: ""       # Set via REDIS_HOST env var
  port: 6379
  password: ""   # Set via REDIS_PASSWORD env var
  database: 1    # Different database for staging
  pool_size: 10
  min_idle_conns: 2
  max_retries: 3

jwt:
  secret_key: ""  # Set via JWT_SECRET_KEY env var
  expiration_time: 3600  # 1 hour
  refresh_time: 86400    # 24 hours
  issuer: "wallet-platform-staging"
  audience: "wallet-platform-users"

log:
  level: "debug"  # More verbose for staging debugging
  format: "json"  # Structured logging
  output: "stdout"

rate_limit:
  enabled: true
  default_limit: 200  # More lenient than production
  default_window: 60
  endpoint_limits:
    "/api/v1/auth/login":
      limit: 10
      window: 300
    "/api/v1/wallets/transfer":
      limit: 20
      window: 60
    "/api/v1/cards/*/transactions":
      limit: 50
      window: 60

security:
  encryption_key: ""  # Set via SECURITY_ENCRYPTION_KEY env var
  hash_salt: ""       # Set via SECURITY_HASH_SALT env var
  max_login_attempts: 8  # Slightly more lenient for testing
  lockout_duration: 600  # 10 minutes
  session_timeout: 3600  # 1 hour
  require_https: true    # Use HTTPS in staging
  csrf_protection: true
  content_type_no_sniff: true

internal_api:
  enabled: true
  key: ""  # Set via INTERNAL_API_KEY env var
  allowed_services:
    - "payment-engine"
    - "user-service"
    - "notification-service"
    - "admin-panel"
    - "test-service"  # Additional for staging testing
  rate_limit:
    enabled: true
    limit: 2000  # Higher limit for staging testing
    window: 60

external:
  payment_engine:
    base_url: ""  # Set via EXTERNAL_PAYMENT_ENGINE_BASE_URL env var (staging URL)
    api_key: ""   # Set via EXTERNAL_PAYMENT_ENGINE_API_KEY env var
    timeout: 30
    retry_count: 3
  
  sms:
    provider: "centurion"
    api_key: ""   # Set via CENTURION_SMS_API_KEY env var (staging key)
    api_url: "https://auth.centurionbd.com/api/v1/sms/send"

  email:
    provider: "centurion"
    api_key: ""   # Set via CENTURION_EMAIL_API_KEY env var (staging key)
    api_url: "https://auth.centurionbd.com/api/v1/email/send"
  
  webhook:
    secret: ""    # Set via EXTERNAL_WEBHOOK_SECRET env var
    timeout: 30
    max_retries: 3
