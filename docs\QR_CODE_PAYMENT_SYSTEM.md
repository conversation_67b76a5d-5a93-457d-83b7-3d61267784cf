# QR Code Payment System Documentation

## Overview

The wallet platform uses QR code-based cards instead of traditional physical cards. This system provides secure, convenient payments through QR code scanning with comprehensive fraud protection and security features.

## QR Code Data Structure

### QRCodePaymentData Format
```json
{
  "card_id": 123,
  "card_number": "****************",
  "wallet_id": 456,
  "card_type": "standard",
  "holder_name": "<PERSON> Do<PERSON>",
  "expires_at": "2025-12-31T23:59:59Z",
  "timestamp": 1704067200,
  "nonce": "abc123def456",
  "signature": "hmac_sha256_signature",
  "version": "1.0",
  "platform": "wallet-platform"
}
```

### Security Features

1. **HMAC-SHA256 Signature**: Each QR code includes a cryptographic signature for data integrity
2. **Nonce Generation**: Unique random nonce for each QR code to prevent replay attacks
3. **Timestamp Validation**: QR codes expire after 24 hours for security
4. **Version Control**: Format versioning for backward compatibility
5. **Base64 Encoding**: Efficient encoding for QR code generation

## API Endpoints

### 1. Generate QR Code
**Endpoint**: `GET /api/v1/cards/:id/qr-code`
**Authentication**: Required (Bearer token)

**Response**:
```json
{
  "success": true,
  "message": "QR code generated successfully",
  "data": {
    "qr_code_data": "base64_encoded_qr_data",
    "card_id": 123,
    "generated_at": "2024-01-01T12:00:00Z"
  }
}
```

### 2. Validate QR Code
**Endpoint**: `POST /api/v1/cards/qr-code/validate`
**Authentication**: Not required (for merchant validation)

**Request**:
```json
{
  "qr_code_data": "base64_encoded_qr_data"
}
```

**Response**:
```json
{
  "success": true,
  "message": "QR code validated successfully",
  "data": {
    "valid": true,
    "card_id": 123,
    "wallet_id": 456,
    "card_type": "standard",
    "holder_name": "John Doe",
    "expires_at": "2025-12-31T23:59:59Z"
  }
}
```

### 3. Process QR Code Payment
**Endpoint**: `POST /api/v1/cards/qr-code/payment`
**Authentication**: Not required (merchant endpoint)

**Request**:
```json
{
  "qr_code_data": "base64_encoded_qr_data",
  "amount": 100.50,
  "currency": "SZL",
  "transaction_type": "purchase",
  "pin": "1234",
  "merchant_info": {
    "name": "Coffee Shop",
    "category": "food_beverage",
    "location": "Mbabane",
    "id": "MERCHANT123"
  },
  "device_info": {
    "type": "pos_terminal",
    "id": "POS001"
  },
  "location_info": {
    "latitude": -26.3054,
    "longitude": 31.1367
  }
}
```

**Response**:
```json
{
  "success": true,
  "message": "QR code payment processed successfully",
  "data": {
    "transaction_id": "TXN123456",
    "amount": 100.50,
    "currency": "SZL",
    "status": "completed",
    "processed_at": "2024-01-01T12:00:00Z",
    "reference": "REF123456"
  }
}
```

## Client-Side QR Code Generation

### Recommended Format
- **QR Code Type**: Data Matrix or QR Code 2D barcode
- **Error Correction**: Level M (15% error correction)
- **Encoding**: UTF-8
- **Size**: Minimum 200x200 pixels for reliable scanning

### Mobile App Integration

#### 1. Generate QR Code
```javascript
// Example JavaScript for QR code generation
async function generateQRCode(cardId) {
  const response = await fetch(`/api/v1/cards/${cardId}/qr-code`, {
    headers: {
      'Authorization': `Bearer ${userToken}`
    }
  });
  
  const data = await response.json();
  return data.data.qr_code_data;
}
```

#### 2. Display QR Code
```javascript
// Using a QR code library (e.g., qrcode.js)
import QRCode from 'qrcode';

async function displayQRCode(qrData) {
  const canvas = document.getElementById('qr-canvas');
  await QRCode.toCanvas(canvas, qrData, {
    width: 300,
    margin: 2,
    color: {
      dark: '#000000',
      light: '#FFFFFF'
    }
  });
}
```

#### 3. QR Code Scanner Integration
```javascript
// Example scanner integration
async function scanQRCode(scannedData) {
  // Validate QR code first
  const validation = await fetch('/api/v1/cards/qr-code/validate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      qr_code_data: scannedData
    })
  });
  
  if (validation.data.valid) {
    // Proceed with payment
    return processPayment(scannedData);
  }
}
```

## Security Considerations

### 1. QR Code Expiration
- QR codes expire after 24 hours
- Generate new QR codes for each payment session
- Implement automatic refresh in mobile apps

### 2. PIN Protection
- PIN verification is optional but recommended for high-value transactions
- Failed PIN attempts trigger card lockout (3 attempts = 30-minute lockout)
- Use secure PIN entry methods in mobile apps

### 3. Fraud Detection
- Real-time fraud analysis for all QR code payments
- Velocity checks and amount anomaly detection
- Geographic and merchant category restrictions
- Automatic transaction blocking for suspicious activity

### 4. Data Protection
- QR code data is signed with HMAC-SHA256
- Sensitive card data is not exposed in QR codes
- Use HTTPS for all API communications

## Environment Configuration

### Required Environment Variables
```bash
# QR Code signing key (generate secure random key for production)
QR_CODE_SIGNING_KEY=your_secure_qr_code_signing_key_here
```

### Production Security
- Generate a strong, random signing key (minimum 32 characters)
- Store signing key securely (environment variables, not in code)
- Rotate signing keys periodically
- Monitor QR code usage patterns for anomalies

## Error Handling

### Common Error Responses

#### Invalid QR Code
```json
{
  "success": false,
  "error": "INVALID_QR_CODE",
  "message": "QR code validation failed: invalid signature",
  "code": "400"
}
```

#### Expired QR Code
```json
{
  "success": false,
  "error": "QR_VALIDATION_FAILED",
  "message": "QR code validation failed: QR code has expired",
  "code": "400"
}
```

#### Card Blocked
```json
{
  "success": false,
  "error": "CARD_UNAVAILABLE",
  "message": "card is not active",
  "code": "400"
}
```

#### Spending Limit Exceeded
```json
{
  "success": false,
  "error": "SPENDING_LIMIT_EXCEEDED",
  "message": "spending limit exceeded",
  "code": "400"
}
```

#### Fraud Detection
```json
{
  "success": false,
  "error": "FRAUD_DETECTED",
  "message": "Transaction blocked due to fraud detection",
  "code": "400"
}
```

## Best Practices

### For Mobile App Developers
1. **QR Code Refresh**: Automatically refresh QR codes every 30 minutes
2. **Offline Handling**: Cache QR codes for offline scenarios with expiration checks
3. **Security**: Implement secure storage for QR code data
4. **User Experience**: Provide clear feedback for QR code generation and scanning
5. **Error Handling**: Implement comprehensive error handling for all scenarios

### For Merchant Integration
1. **Validation**: Always validate QR codes before processing payments
2. **Timeout**: Implement reasonable timeouts for payment processing
3. **Retry Logic**: Implement retry mechanisms for network failures
4. **Logging**: Log all QR code transactions for audit purposes
5. **Security**: Use HTTPS and validate all input data

## Troubleshooting

### QR Code Generation Issues
- Check card status (must be active)
- Verify user authentication
- Ensure card belongs to authenticated user

### QR Code Validation Issues
- Check QR code format and encoding
- Verify signature with correct signing key
- Check timestamp for expiration

### Payment Processing Issues
- Validate all required fields
- Check spending limits and card restrictions
- Review fraud detection logs
- Verify PIN if required

## Integration Examples

See the `/examples` directory for complete integration examples including:
- Mobile app QR code generation
- POS terminal QR code scanning
- Web-based payment processing
- Webhook handling for payment confirmations
