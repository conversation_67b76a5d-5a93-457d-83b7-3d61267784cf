# Development Environment Configuration
# Copy this file to .env.development and update the values

# Application
APP_NAME=Wallet Platform (Dev)
APP_VERSION=1.0.0
APP_ENVIRONMENT=development
APP_DEBUG=true

# Server
SERVER_PORT=8086
SERVER_HOST=0.0.0.0
SERVER_ENVIRONMENT=development
SERVER_READ_TIMEOUT=30
SERVER_WRITE_TIMEOUT=30
SERVER_IDLE_TIMEOUT=120

# CORS Configuration (Development - More permissive)
SERVER_CORS_ALLOW_ORIGINS=http://localhost:3000,http://localhost:3001,http://127.0.0.1:3000
SERVER_CORS_ALLOW_METHODS=GET,POST,PUT,DELETE,OPTIONS
SERVER_CORS_ALLOW_HEADERS=*
SERVER_CORS_ALLOW_CREDENTIALS=true
SERVER_CORS_MAX_AGE=86400

# Database (Development)
DATABASE_DRIVER=mysql
DATABASE_HOST=localhost
DATABASE_PORT=3306
DATABASE_USERNAME=wallet_dev_user
DATABASE_PASSWORD=dev_password_123
DATABASE_DATABASE=wallet_platform_dev
DATABASE_SSL_MODE=false
DATABASE_MAX_OPEN_CONNS=10
DATABASE_MAX_IDLE_CONNS=2
DATABASE_CONN_MAX_LIFETIME=300
DATABASE_CONN_MAX_IDLE_TIME=60

# Redis (Optional in development)
REDIS_ENABLED=false
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DATABASE=0
REDIS_POOL_SIZE=5
REDIS_MIN_IDLE_CONNS=1
REDIS_MAX_RETRIES=3

# JWT (Development - Use secure values even in dev)
JWT_SECRET_KEY=ZGV2X2p3dF9zZWNyZXRfa2V5X2NoYW5nZV9pbl9wcm9kdWN0aW9u
JWT_EXPIRATION_TIME=7200
JWT_REFRESH_TIME=86400
JWT_ISSUER=wallet-platform-dev
JWT_AUDIENCE=wallet-platform-users

# Logging (Development)
LOG_LEVEL=debug
LOG_FORMAT=text
LOG_OUTPUT=stdout

# Rate Limiting (Disabled for development)
RATE_LIMIT_ENABLED=false
RATE_LIMIT_DEFAULT_LIMIT=1000
RATE_LIMIT_DEFAULT_WINDOW=60

# Security (Development - More lenient)
SECURITY_ENCRYPTION_KEY=dev_encryption_key_32_characters
SECURITY_HASH_SALT=dev_hash_salt_16
SECURITY_MAX_LOGIN_ATTEMPTS=10
SECURITY_LOCKOUT_DURATION=300
SECURITY_SESSION_TIMEOUT=7200
SECURITY_REQUIRE_HTTPS=false
SECURITY_CSRF_PROTECTION=false
SECURITY_CONTENT_TYPE_NO_SNIFF=true

# Internal API (Development)
INTERNAL_API_KEY=ZGV2X2ludGVybmFsX2FwaV9rZXlfY2hhbmdlX2luX3Byb2R1Y3Rpb24=

# External Services (Development/Testing)
EXTERNAL_PAYMENT_ENGINE_BASE_URL=http://localhost:8000
EXTERNAL_PAYMENT_ENGINE_API_KEY=dev_payment_engine_key
EXTERNAL_PAYMENT_ENGINE_TIMEOUT=30
EXTERNAL_PAYMENT_ENGINE_RETRY_COUNT=3

EXTERNAL_SMS_PROVIDER=centurion
CENTURION_SMS_API_KEY=dev_sms_api_key

EXTERNAL_EMAIL_PROVIDER=centurion
CENTURION_EMAIL_API_KEY=dev_email_api_key

EXTERNAL_WEBHOOK_SECRET=dev_webhook_secret_key
EXTERNAL_WEBHOOK_TIMEOUT=30
EXTERNAL_WEBHOOK_MAX_RETRIES=3
