# Environment Configuration Example
# Copy this file to .env and update the values

# Application
APP_NAME=Wallet Platform
APP_VERSION=1.0.0
APP_ENVIRONMENT=development
APP_DEBUG=true

# Server
SERVER_PORT=8086
SERVER_HOST=0.0.0.0
SERVER_ENVIRONMENT=development
SERVER_READ_TIMEOUT=30
SERVER_WRITE_TIMEOUT=30
SERVER_IDLE_TIMEOUT=120

# CORS Configuration
SERVER_CORS_ALLOW_ORIGINS=http://localhost,https://lockedpay.centurionbd.com,https://merchant.centurionbd.com,https://cashier.centurionbd.com
SERVER_CORS_ALLOW_METHODS=GET,POST,PUT,DELETE,OPTIONS
SERVER_CORS_ALLOW_HEADERS=*
SERVER_CORS_ALLOW_CREDENTIALS=true
SERVER_CORS_MAX_AGE=86400

# Database
DATABASE_DRIVER=mysql
DATABASE_HOST=localhost
DATABASE_PORT=3306
DATABASE_USERNAME=appuser
DATABASE_PASSWORD=8htf677
DATABASE_DATABASE=wallet_platform
DATABASE_SSL_MODE=true
DATABASE_MAX_OPEN_CONNS=25
DATABASE_MAX_IDLE_CONNS=5
DATABASE_CONN_MAX_LIFETIME=300
DATABASE_CONN_MAX_IDLE_TIME=60

# Redis (Optional - for rate limiting)
REDIS_ENABLED=false
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DATABASE=0
REDIS_POOL_SIZE=10
REDIS_MIN_IDLE_CONNS=2
REDIS_MAX_RETRIES=3

# JWT - GENERATE SECURE RANDOM VALUES FOR PRODUCTION
JWT_SECRET_KEY=cKuf6G9d4dXPOJxIYlIM2Y+ZoBG6YpS+p7xw8LUcA4Q=
JWT_EXPIRATION_TIME=3600
JWT_REFRESH_TIME=86400
JWT_ISSUER=wallet-platform
JWT_AUDIENCE=wallet-platform-users

# Logging
LOG_LEVEL=info
LOG_FORMAT=json
LOG_OUTPUT=stdout

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_DEFAULT_LIMIT=100
RATE_LIMIT_DEFAULT_WINDOW=60

# Security - GENERATE SECURE RANDOM VALUES FOR PRODUCTION
SECURITY_ENCRYPTION_KEY=yuC9tWv3HnBpQgF6mL0eXa92KbVrYs5D
SECURITY_HASH_SALT=a7CzT!3v@9Wk^Lp#2mBnZxY$uQeRsJd1
SECURITY_MAX_LOGIN_ATTEMPTS=5
SECURITY_LOCKOUT_DURATION=900
SECURITY_SESSION_TIMEOUT=3600
SECURITY_REQUIRE_HTTPS=true
SECURITY_CSRF_PROTECTION=true
SECURITY_CONTENT_TYPE_NO_SNIFF=true

# Internal API (Service-to-Service Authentication) - GENERATE SECURE RANDOM KEY
INTERNAL_API_KEY=i7jN1yM5+W9dZlRE2pRZw0AkE8tKq7XsdI0jRcnwdxI=

# Admin API (Administrative Operations) - GENERATE SECURE RANDOM KEY
ADMIN_API_KEY=3nTq8cH4eX7JvY2r9WpZsmfVoqL1bGx0dKwNCE6+X0M=

# QR Code Configuration - GENERATE SECURE RANDOM KEY FOR PRODUCTION
QR_CODE_SIGNING_KEY=FkzR8vNpXybHsDq5tJ1w4GmULo9eZcYB7WSVxQaCkTI=

# Payment Engine API (for direct wallet operations) - SET PRODUCTION VALUES
PAYMENT_API_URL=https://payment.centurionbd.com/api/v1/pay
PAYMENT_API_KEY=3464214dd1b7f3512091d86c4ee0f50df0f3c13e01baf5b6821a9f19153ae342

# Platform Fees Configuration - SET PRODUCTION VALUES
CARD_CREATION_FEE_STANDARD=25.00
CARD_CREATION_FEE_PREMIUM=50.00
CARD_CREATION_FEE_BUSINESS=100.00
WITHDRAWAL_FEE_PERCENTAGE=1.0
TRANSACTION_FEE_PERCENTAGE=0.5
MONTHLY_MAINTENANCE_FEE=10.00
CARD_REPLACEMENT_FEE=15.00
INTERNATIONAL_TRANSACTION_FEE_PERCENTAGE=2.5
ATM_WITHDRAWAL_FEE=5.00

# Card Limits Configuration - SET PRODUCTION VALUES
# Standard Card Limits
CARD_LIMITS_STANDARD_SPENDING_LIMIT=5000.00
CARD_LIMITS_STANDARD_DAILY_SPENDING_LIMIT=1000.00
CARD_LIMITS_STANDARD_MONTHLY_SPENDING_LIMIT=10000.00
CARD_LIMITS_STANDARD_MAX_CARDS_PER_WALLET=3

# Premium Card Limits
CARD_LIMITS_PREMIUM_SPENDING_LIMIT=15000.00
CARD_LIMITS_PREMIUM_DAILY_SPENDING_LIMIT=3000.00
CARD_LIMITS_PREMIUM_MONTHLY_SPENDING_LIMIT=30000.00
CARD_LIMITS_PREMIUM_MAX_CARDS_PER_WALLET=5

# Business Card Limits
CARD_LIMITS_BUSINESS_SPENDING_LIMIT=50000.00
CARD_LIMITS_BUSINESS_DAILY_SPENDING_LIMIT=10000.00
CARD_LIMITS_BUSINESS_MONTHLY_SPENDING_LIMIT=100000.00
CARD_LIMITS_BUSINESS_MAX_CARDS_PER_WALLET=10

# Card Creation Limits
CARD_CREATION_MAX_CARDS_PER_DAY=3
CARD_CREATION_COOLDOWN_HOURS=24

# Transaction Limits
CARD_TRANSACTION_MIN_AMOUNT=1.00
CARD_TRANSACTION_MAX_AMOUNT_STANDARD=5000.00
CARD_TRANSACTION_MAX_AMOUNT_PREMIUM=15000.00
CARD_TRANSACTION_MAX_AMOUNT_BUSINESS=50000.00

# ATM Withdrawal Limits
CARD_ATM_DAILY_LIMIT_STANDARD=2000.00
CARD_ATM_DAILY_LIMIT_PREMIUM=5000.00
CARD_ATM_DAILY_LIMIT_BUSINESS=10000.00
CARD_ATM_MONTHLY_LIMIT_STANDARD=20000.00
CARD_ATM_MONTHLY_LIMIT_PREMIUM=50000.00
CARD_ATM_MONTHLY_LIMIT_BUSINESS=100000.00
EXTERNAL_PAYMENT_ENGINE_RETRY_COUNT=3

EXTERNAL_SMS_PROVIDER=centurion
CENTURION_SMS_API_KEY=93e0c0c8fba4180a3da6919cdcb24406094c96ae40cca05def305daaee444c1a

EXTERNAL_EMAIL_PROVIDER=centurion
CENTURION_EMAIL_API_KEY=93e0c0c8fba4180a3da6919cdcb24406094c96ae40cca05def305daaee444c1a

EXTERNAL_WEBHOOK_SECRET=Jv7XqZr3Ls9wNfK1bTGvP2xHaUmRcQeY8oD4Vj0s5kE=
EXTERNAL_WEBHOOK_TIMEOUT=30
EXTERNAL_WEBHOOK_MAX_RETRIES=3
