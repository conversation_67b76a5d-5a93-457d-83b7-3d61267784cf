package services

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"wallet-platform/internal/models"
	"wallet-platform/pkg/logger"
	"wallet-platform/pkg/redis"

	"gorm.io/gorm"
)

// DashboardService provides real-time dashboard data and analytics
type DashboardService struct {
	db                *gorm.DB
	redis             *redis.Client
	logger            *logger.Logger
	monitoringService *MonitoringService
	analyticsService  *AnalyticsService
}

// NewDashboardService creates a new dashboard service
func NewDashboardService(db *gorm.DB, redisClient *redis.Client, log *logger.Logger,
	monitoringService *MonitoringService, analyticsService *AnalyticsService) *DashboardService {
	return &DashboardService{
		db:                db,
		redis:             redisClient,
		logger:            log,
		monitoringService: monitoringService,
		analyticsService:  analyticsService,
	}
}

// DashboardData represents comprehensive dashboard information
type DashboardData struct {
	Timestamp       time.Time          `json:"timestamp"`
	Overview        *DashboardOverview `json:"overview"`
	RealtimeMetrics *RealtimeMetrics   `json:"realtime_metrics"`
	Charts          []DashboardChart   `json:"charts"`
	Alerts          []DashboardAlert   `json:"alerts"`
	SystemStatus    *SystemStatus      `json:"system_status"`
	QuickStats      *QuickStats        `json:"quick_stats"`
	RecentActivity  []ActivityItem     `json:"recent_activity"`
}

// DashboardOverview represents key overview metrics
type DashboardOverview struct {
	TotalWallets      int     `json:"total_wallets"`
	ActiveWallets     int     `json:"active_wallets"`
	TotalCards        int     `json:"total_cards"`
	ActiveCards       int     `json:"active_cards"`
	TotalBalance      float64 `json:"total_balance"`
	TransactionsToday int     `json:"transactions_today"`
	VolumeToday       float64 `json:"volume_today"`
	RevenueToday      float64 `json:"revenue_today"`
	WalletGrowth      float64 `json:"wallet_growth"`      // percentage
	TransactionGrowth float64 `json:"transaction_growth"` // percentage
	VolumeGrowth      float64 `json:"volume_growth"`      // percentage
}

// RealtimeMetrics represents live system metrics
type RealtimeMetrics struct {
	ActiveUsers        int     `json:"active_users"`
	TransactionsPerMin float64 `json:"transactions_per_minute"`
	VolumePerMin       float64 `json:"volume_per_minute"`
	SuccessRate        float64 `json:"success_rate"`
	ResponseTime       float64 `json:"response_time"`
	ErrorRate          float64 `json:"error_rate"`
	FraudRate          float64 `json:"fraud_rate"`
	SystemLoad         float64 `json:"system_load"`
}

// DashboardChart represents chart data for dashboard
type DashboardChart struct {
	ID          string                   `json:"id"`
	Type        string                   `json:"type"` // line, bar, pie, area
	Title       string                   `json:"title"`
	Description string                   `json:"description"`
	Data        []map[string]interface{} `json:"data"`
	Config      map[string]interface{}   `json:"config"`
	UpdatedAt   time.Time                `json:"updated_at"`
}

// DashboardAlert represents dashboard alert
type DashboardAlert struct {
	ID        uint      `json:"id"`
	Type      string    `json:"type"`
	Level     string    `json:"level"`
	Title     string    `json:"title"`
	Message   string    `json:"message"`
	Component string    `json:"component"`
	CreatedAt time.Time `json:"created_at"`
	IsNew     bool      `json:"is_new"`
}

// SystemStatus represents overall system health
type SystemStatus struct {
	OverallStatus  string    `json:"overall_status"` // healthy, warning, critical
	DatabaseStatus string    `json:"database_status"`
	RedisStatus    string    `json:"redis_status"`
	APIStatus      string    `json:"api_status"`
	Uptime         string    `json:"uptime"`
	LastCheck      time.Time `json:"last_check"`
}

// QuickStats represents quick statistics
type QuickStats struct {
	NewWalletsToday    int     `json:"new_wallets_today"`
	NewCardsToday      int     `json:"new_cards_today"`
	FailedTransactions int     `json:"failed_transactions"`
	FraudAlertsToday   int     `json:"fraud_alerts_today"`
	AverageTransaction float64 `json:"average_transaction"`
	LargestTransaction float64 `json:"largest_transaction"`
	PeakHour           int     `json:"peak_hour"`
	TopMerchant        string  `json:"top_merchant"`
}

// ActivityItem represents recent activity
type ActivityItem struct {
	ID          uint      `json:"id"`
	Type        string    `json:"type"` // transaction, wallet_created, card_created, alert
	Description string    `json:"description"`
	Amount      float64   `json:"amount,omitempty"`
	Status      string    `json:"status"`
	CreatedAt   time.Time `json:"created_at"`
	UserID      uint      `json:"user_id,omitempty"`
}

// GetDashboardData returns comprehensive dashboard data
func (d *DashboardService) GetDashboardData() (*DashboardData, error) {
	// Check cache first
	cached, err := d.redis.Get(context.Background(), "dashboard_data").Result()
	if err == nil {
		var dashboardData DashboardData
		if json.Unmarshal([]byte(cached), &dashboardData) == nil {
			// Return cached data if less than 30 seconds old
			if time.Since(dashboardData.Timestamp) < 30*time.Second {
				return &dashboardData, nil
			}
		}
	}

	// Generate fresh dashboard data
	dashboardData := &DashboardData{
		Timestamp: time.Now(),
		Charts:    []DashboardChart{},
	}

	// Get overview metrics
	dashboardData.Overview = d.getDashboardOverview()

	// Get realtime metrics
	dashboardData.RealtimeMetrics = d.getRealtimeMetrics()

	// Get system status
	dashboardData.SystemStatus = d.getSystemStatus()

	// Get quick stats
	dashboardData.QuickStats = d.getQuickStats()

	// Get recent alerts
	dashboardData.Alerts = d.getRecentAlerts()

	// Get recent activity
	dashboardData.RecentActivity = d.getRecentActivity()

	// Generate charts
	dashboardData.Charts = d.generateDashboardCharts()

	// Cache the dashboard data
	dashboardJSON, _ := json.Marshal(dashboardData)
	d.redis.Set(context.Background(), "dashboard_data", string(dashboardJSON), 30*time.Second)

	d.logger.LogAnalytics("dashboard_data", "realtime", 0, 0)

	return dashboardData, nil
}

// getDashboardOverview generates overview metrics
func (d *DashboardService) getDashboardOverview() *DashboardOverview {
	overview := &DashboardOverview{}

	// Get current metrics
	var totalWallets, activeWallets, totalCards, activeCards int64
	var totalBalance float64

	d.db.Model(&models.Wallet{}).Count(&totalWallets)
	d.db.Model(&models.Wallet{}).Where("status = 'active'").Count(&activeWallets)
	d.db.Model(&models.PayCard{}).Count(&totalCards)
	d.db.Model(&models.PayCard{}).Where("status = 'active'").Count(&activeCards)
	d.db.Model(&models.Wallet{}).Select("COALESCE(SUM(balance), 0)").Scan(&totalBalance)

	overview.TotalWallets = int(totalWallets)
	overview.ActiveWallets = int(activeWallets)
	overview.TotalCards = int(totalCards)
	overview.ActiveCards = int(activeCards)
	overview.TotalBalance = totalBalance

	// Get today's metrics
	today := time.Now().Format("2006-01-02")
	var transactionsToday int64
	var volumeToday float64

	d.db.Model(&models.WalletTransaction{}).Where("DATE(created_at) = ?", today).Count(&transactionsToday)
	d.db.Model(&models.WalletTransaction{}).Where("DATE(created_at) = ?", today).
		Select("COALESCE(SUM(amount), 0)").Scan(&volumeToday)

	overview.TransactionsToday = int(transactionsToday)
	overview.VolumeToday = volumeToday
	overview.RevenueToday = volumeToday * 0.01 // Assuming 1% fee

	// Calculate growth rates (compared to yesterday)
	yesterday := time.Now().AddDate(0, 0, -1).Format("2006-01-02")
	var transactionsYesterday int64
	var volumeYesterday float64

	d.db.Model(&models.WalletTransaction{}).Where("DATE(created_at) = ?", yesterday).Count(&transactionsYesterday)
	d.db.Model(&models.WalletTransaction{}).Where("DATE(created_at) = ?", yesterday).
		Select("COALESCE(SUM(amount), 0)").Scan(&volumeYesterday)

	if transactionsYesterday > 0 {
		overview.TransactionGrowth = (float64(transactionsToday) - float64(transactionsYesterday)) / float64(transactionsYesterday) * 100
	}
	if volumeYesterday > 0 {
		overview.VolumeGrowth = (volumeToday - volumeYesterday) / volumeYesterday * 100
	}

	return overview
}

// getRealtimeMetrics generates realtime metrics
func (d *DashboardService) getRealtimeMetrics() *RealtimeMetrics {
	metrics := &RealtimeMetrics{}

	// Get active users (last 5 minutes)
	fiveMinutesAgo := time.Now().Add(-5 * time.Minute)
	var activeUsers int64
	d.db.Model(&models.WalletTransaction{}).
		Where("created_at >= ?", fiveMinutesAgo).
		Distinct("wallet_id").Count(&activeUsers)
	metrics.ActiveUsers = int(activeUsers)

	// Get transactions per minute (last hour)
	oneHourAgo := time.Now().Add(-1 * time.Hour)
	var transactionsLastHour int64
	var volumeLastHour float64

	d.db.Model(&models.WalletTransaction{}).Where("created_at >= ?", oneHourAgo).Count(&transactionsLastHour)
	d.db.Model(&models.WalletTransaction{}).Where("created_at >= ?", oneHourAgo).
		Select("COALESCE(SUM(amount), 0)").Scan(&volumeLastHour)

	metrics.TransactionsPerMin = float64(transactionsLastHour) / 60.0
	metrics.VolumePerMin = volumeLastHour / 60.0

	// Calculate success rate
	var successfulTxns int64
	d.db.Model(&models.WalletTransaction{}).
		Where("created_at >= ? AND status = 'completed'", oneHourAgo).Count(&successfulTxns)

	if transactionsLastHour > 0 {
		metrics.SuccessRate = float64(successfulTxns) / float64(transactionsLastHour) * 100
		metrics.ErrorRate = 100 - metrics.SuccessRate
	}

	// Get fraud rate
	var fraudAlerts int64
	d.db.Model(&models.FraudAlert{}).Where("created_at >= ?", oneHourAgo).Count(&fraudAlerts)
	if transactionsLastHour > 0 {
		metrics.FraudRate = float64(fraudAlerts) / float64(transactionsLastHour) * 100
	}

	// Simulated metrics (would come from actual monitoring)
	metrics.ResponseTime = 120.0 // milliseconds
	metrics.SystemLoad = 65.0    // percentage

	return metrics
}

// getSystemStatus generates system status
func (d *DashboardService) getSystemStatus() *SystemStatus {
	status := &SystemStatus{
		LastCheck: time.Now(),
	}

	// Check database
	if err := d.db.Exec("SELECT 1").Error; err != nil {
		status.DatabaseStatus = "unhealthy"
	} else {
		status.DatabaseStatus = "healthy"
	}

	// Check Redis
	if err := d.redis.Ping(context.Background()).Err(); err != nil {
		status.RedisStatus = "unhealthy"
	} else {
		status.RedisStatus = "healthy"
	}

	// API status (simulated)
	status.APIStatus = "healthy"

	// Overall status
	if status.DatabaseStatus == "healthy" && status.RedisStatus == "healthy" && status.APIStatus == "healthy" {
		status.OverallStatus = "healthy"
	} else if status.DatabaseStatus == "unhealthy" || status.RedisStatus == "unhealthy" {
		status.OverallStatus = "critical"
	} else {
		status.OverallStatus = "warning"
	}

	// Uptime (simulated)
	status.Uptime = "99.9%"

	return status
}

// getQuickStats generates quick statistics
func (d *DashboardService) getQuickStats() *QuickStats {
	stats := &QuickStats{}
	today := time.Now().Format("2006-01-02")

	// Get new wallets and cards today
	var newWallets, newCards int64
	d.db.Model(&models.Wallet{}).Where("DATE(created_at) = ?", today).Count(&newWallets)
	d.db.Model(&models.PayCard{}).Where("DATE(created_at) = ?", today).Count(&newCards)

	stats.NewWalletsToday = int(newWallets)
	stats.NewCardsToday = int(newCards)

	// Get failed transactions today
	var failedTxns int64
	d.db.Model(&models.WalletTransaction{}).
		Where("DATE(created_at) = ? AND status = 'failed'", today).Count(&failedTxns)
	stats.FailedTransactions = int(failedTxns)

	// Get fraud alerts today
	var fraudAlerts int64
	d.db.Model(&models.FraudAlert{}).Where("DATE(created_at) = ?", today).Count(&fraudAlerts)
	stats.FraudAlertsToday = int(fraudAlerts)

	// Calculate average transaction amount today
	var avgAmount, maxAmount float64
	d.db.Model(&models.WalletTransaction{}).Where("DATE(created_at) = ?", today).
		Select("COALESCE(AVG(amount), 0)").Scan(&avgAmount)
	d.db.Model(&models.WalletTransaction{}).Where("DATE(created_at) = ?", today).
		Select("COALESCE(MAX(amount), 0)").Scan(&maxAmount)

	stats.AverageTransaction = avgAmount
	stats.LargestTransaction = maxAmount

	// Find peak hour (simulated)
	stats.PeakHour = 14 // 2 PM

	// Top merchant (simulated)
	stats.TopMerchant = "Online Store"

	return stats
}

// getRecentAlerts gets recent system alerts
func (d *DashboardService) getRecentAlerts() []DashboardAlert {
	var alerts []models.Alert
	d.db.Where("created_at >= ?", time.Now().Add(-24*time.Hour)).
		Order("created_at DESC").
		Limit(10).
		Find(&alerts)

	dashboardAlerts := make([]DashboardAlert, len(alerts))
	for i, alert := range alerts {
		dashboardAlerts[i] = DashboardAlert{
			ID:        alert.ID,
			Type:      alert.Type,
			Level:     alert.Level,
			Title:     alert.Title,
			Message:   alert.Message,
			Component: alert.Component,
			CreatedAt: alert.CreatedAt,
			IsNew:     time.Since(alert.CreatedAt) < 1*time.Hour,
		}
	}

	return dashboardAlerts
}

// getRecentActivity gets recent system activity
func (d *DashboardService) getRecentActivity() []ActivityItem {
	var activities []ActivityItem

	// Get recent transactions
	var transactions []models.WalletTransaction
	d.db.Where("created_at >= ?", time.Now().Add(-2*time.Hour)).
		Order("created_at DESC").
		Limit(20).
		Find(&transactions)

	for _, tx := range transactions {
		activities = append(activities, ActivityItem{
			ID:          tx.ID,
			Type:        "transaction",
			Description: fmt.Sprintf("%s transaction", tx.Type),
			Amount:      tx.Amount,
			Status:      "completed", // WalletTransaction doesn't have Status field, assume completed
			CreatedAt:   tx.CreatedAt,
			UserID:      tx.WalletID,
		})
	}

	return activities
}

// generateDashboardCharts generates chart data for dashboard
func (d *DashboardService) generateDashboardCharts() []DashboardChart {
	charts := []DashboardChart{}

	// Transaction volume chart (last 24 hours)
	volumeChart := d.generateVolumeChart()
	charts = append(charts, volumeChart)

	// Transaction success rate chart
	successChart := d.generateSuccessRateChart()
	charts = append(charts, successChart)

	// User activity chart
	activityChart := d.generateActivityChart()
	charts = append(charts, activityChart)

	return charts
}

// generateVolumeChart creates transaction volume chart
func (d *DashboardService) generateVolumeChart() DashboardChart {
	data := []map[string]interface{}{}

	// Get hourly data for last 24 hours
	for i := 23; i >= 0; i-- {
		hour := time.Now().Add(-time.Duration(i) * time.Hour)

		var volume float64
		var count int64
		d.db.Model(&models.WalletTransaction{}).
			Where("created_at >= ? AND created_at < ?", hour, hour.Add(1*time.Hour)).
			Select("COALESCE(SUM(amount), 0)").Scan(&volume)
		d.db.Model(&models.WalletTransaction{}).
			Where("created_at >= ? AND created_at < ?", hour, hour.Add(1*time.Hour)).
			Count(&count)

		data = append(data, map[string]interface{}{
			"time":         hour.Format("15:04"),
			"volume":       volume,
			"transactions": count,
		})
	}

	return DashboardChart{
		ID:          "volume_chart",
		Type:        "line",
		Title:       "Transaction Volume (24h)",
		Description: "Hourly transaction volume and count",
		Data:        data,
		Config: map[string]interface{}{
			"xAxis": "time",
			"yAxis": []string{"volume", "transactions"},
		},
		UpdatedAt: time.Now(),
	}
}

// generateSuccessRateChart creates success rate chart
func (d *DashboardService) generateSuccessRateChart() DashboardChart {
	data := []map[string]interface{}{}

	// Get hourly success rate for last 12 hours
	for i := 11; i >= 0; i-- {
		hour := time.Now().Add(-time.Duration(i) * time.Hour)

		var total, successful int64
		d.db.Model(&models.WalletTransaction{}).
			Where("created_at >= ? AND created_at < ?", hour, hour.Add(1*time.Hour)).
			Count(&total)
		d.db.Model(&models.WalletTransaction{}).
			Where("created_at >= ? AND created_at < ? AND status = 'completed'", hour, hour.Add(1*time.Hour)).
			Count(&successful)

		successRate := 100.0
		if total > 0 {
			successRate = float64(successful) / float64(total) * 100
		}

		data = append(data, map[string]interface{}{
			"time":         hour.Format("15:04"),
			"success_rate": successRate,
			"total":        total,
		})
	}

	return DashboardChart{
		ID:          "success_rate_chart",
		Type:        "area",
		Title:       "Success Rate (12h)",
		Description: "Transaction success rate over time",
		Data:        data,
		Config: map[string]interface{}{
			"xAxis": "time",
			"yAxis": "success_rate",
		},
		UpdatedAt: time.Now(),
	}
}

// generateActivityChart creates user activity chart
func (d *DashboardService) generateActivityChart() DashboardChart {
	data := []map[string]interface{}{}

	// Get daily activity for last 7 days
	for i := 6; i >= 0; i-- {
		day := time.Now().AddDate(0, 0, -i)
		dayStr := day.Format("2006-01-02")

		var activeUsers int64
		d.db.Model(&models.WalletTransaction{}).
			Where("DATE(created_at) = ?", dayStr).
			Distinct("wallet_id").Count(&activeUsers)

		data = append(data, map[string]interface{}{
			"date":         day.Format("Jan 02"),
			"active_users": activeUsers,
		})
	}

	return DashboardChart{
		ID:          "activity_chart",
		Type:        "bar",
		Title:       "Daily Active Users (7d)",
		Description: "Daily active user count",
		Data:        data,
		Config: map[string]interface{}{
			"xAxis": "date",
			"yAxis": "active_users",
		},
		UpdatedAt: time.Now(),
	}
}
