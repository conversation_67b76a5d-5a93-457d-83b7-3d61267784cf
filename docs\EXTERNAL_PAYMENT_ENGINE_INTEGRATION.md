# External Payment Engine Integration

## Overview

The wallet platform has been successfully integrated with an external payment engine to handle actual fund movement for topup and withdrawal operations. This ensures that the platform operates as an Over-The-Top (OTT) service that relies on external payment providers for real money transactions.

## Integration Architecture

### Payment Direction Mapping
- **"debit"** direction = Collecting funds from external wallet to our platform (topups)
- **"credit"** direction = Sending funds from our platform to external wallet (withdrawals)

### Transaction Flow
1. **Database Transaction Started** - All operations are wrapped in database transactions
2. **External Payment Processed** - Payment engine is called first
3. **Wallet Balance Updated** - Only after successful external payment
4. **Transaction Records Created** - Audit trail maintained
5. **Database Transaction Committed** - All changes persisted atomically

## Implementation Details

### Core Payment Function
```go
func (s *WalletService) makePayment(provider, direction, phone string, amount float64) error
```

**Features:**
- HTTP client with 30-second timeout
- Comprehensive error handling and logging
- Payment request validation
- Response status verification
- Detailed audit logging for all operations

### Topup Operations
**Method:** `TopupWallet(walletID uint, amount float64, paymentMethod, reference string)`

**Process:**
1. Validate wallet status and permissions
2. Extract phone number (remove country code if present)
3. Call external payment engine with "debit" direction
4. Update wallet balance only after successful external payment
5. Create transaction records with external payment reference
6. Commit all changes atomically

**Error Handling:**
- External payment failure → Database rollback, no wallet changes
- Wallet update failure → Critical logging for manual reconciliation
- Transaction record failure → Critical logging for manual reconciliation

### Withdrawal Operations
**Method:** `WithdrawFunds(walletID uint, amount float64, destination, reference string)`

**Process:**
1. Validate wallet status, balance, and withdrawal limits
2. Calculate withdrawal fees (1% configurable)
3. Extract phone number for external payment
4. Call external payment engine with "credit" direction
5. Deduct total amount (withdrawal + fees) from wallet
6. Create separate transaction records for withdrawal and fees
7. Commit all changes atomically

**Features:**
- Withdrawal limit validation (daily/monthly)
- Fee calculation and separate transaction recording
- Comprehensive audit logging
- Critical error logging for reconciliation

### Phone-Based Operations
**Method:** `TopupWalletByPhone(phoneNumber string, amount float64, paymentMethod, reference string)`

- Automatically resolves phone number to wallet ID
- Uses the same external payment integration as ID-based operations
- Maintains consistency across all topup methods

## Configuration

### Environment Variables
```bash
# Payment Engine API Configuration
PAYMENT_API_URL=https://your-payment-api.com/api/payments
PAYMENT_API_KEY=your_payment_api_key_here
```

### Payment Request Format
```json
{
  "provider_code": "provider_name",
  "reference": "uuid-generated-reference",
  "amount": 100.00,
  "direction": "debit|credit",
  "meta": {
    "msisdn": "268xxxxxxxx"
  }
}
```

### Expected Response Format
```json
{
  "message": "Payment processed successfully",
  "transaction": {
    "Status": "success",
    "TransactionId": "ext_txn_12345",
    "Amount": 100.00,
    "Currency": "SZL"
  }
}
```

## Error Handling & Reconciliation

### Critical Error Scenarios
1. **External payment succeeds but wallet update fails**
2. **External payment succeeds but transaction record fails**
3. **External payment succeeds but database commit fails**

### Reconciliation Process
- All critical errors are logged with `"critical": true` flag
- Detailed context provided for manual reconciliation
- External payment references preserved for tracking
- Audit logs maintain complete transaction history

### Logging Strategy
- **Success:** Transaction completion with external reference
- **Failure:** Detailed error context with operation state
- **Critical:** Scenarios requiring manual intervention

## Security Features

### Phone Number Handling
- Automatic country code detection and removal
- Support for both "+268" and "268" prefixes
- Consistent formatting for external API calls

### Transaction Integrity
- Database transactions ensure atomicity
- External payment processed before wallet changes
- Rollback mechanisms for all failure scenarios
- Comprehensive audit trail maintenance

### Validation
- Wallet status verification
- Balance and limit checks
- Payment method validation
- Reference uniqueness enforcement

## Integration Points

### Updated Methods
1. `TopupWallet()` - Now uses external payment engine
2. `TopupWalletByPhone()` - Inherits external payment integration
3. `WithdrawFunds()` - New method with external payment integration
4. `Withdraw()` controller - Now functional with real implementation

### Service Interface
- Added `WithdrawFunds()` to `WalletServiceInterface`
- Maintains backward compatibility
- Consistent error handling patterns

## Testing Recommendations

### Unit Tests
- Mock external payment engine responses
- Test failure scenarios and rollback behavior
- Validate transaction record creation
- Test phone number formatting

### Integration Tests
- End-to-end payment flow testing
- Error handling verification
- Reconciliation scenario testing
- Performance under load

### Production Monitoring
- External payment success/failure rates
- Response time monitoring
- Critical error alerting
- Reconciliation queue monitoring

## Deployment Checklist

- [ ] Configure `PAYMENT_API_URL` and `PAYMENT_API_KEY`
- [ ] Test external payment engine connectivity
- [ ] Verify phone number formatting for target country
- [ ] Set up monitoring for critical errors
- [ ] Implement reconciliation procedures
- [ ] Configure alerting for failed payments
- [ ] Test rollback scenarios
- [ ] Validate audit logging functionality

## Next Steps

1. **Webhook Integration** - Connect existing webhook handlers to process async payment confirmations
2. **Reconciliation Service** - Implement automated reconciliation for critical errors
3. **Fee Configuration** - Make withdrawal fees configurable per wallet type
4. **Provider Management** - Support multiple payment providers
5. **Retry Logic** - Implement retry mechanisms for transient failures
