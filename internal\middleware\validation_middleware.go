package middleware

import (
	"fmt"
	"net/http"
	"regexp"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

// ValidationMiddleware provides input validation for API requests
type ValidationMiddleware struct {
	phoneRegex *regexp.Regexp
	emailRegex *regexp.Regexp
}

// NewValidationMiddleware creates a new validation middleware
func NewValidationMiddleware() *ValidationMiddleware {
	return &ValidationMiddleware{
		phoneRegex: regexp.MustCompile(`^\+?[1-9]\d{1,14}$`), // E.164 format
		emailRegex: regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`),
	}
}

// ValidatePhoneNumber validates phone number format
func (v *ValidationMiddleware) ValidatePhoneNumber() gin.HandlerFunc {
	return func(c *gin.Context) {
		var body map[string]interface{}
		if err := c.ShouldBindJSON(&body); err != nil {
			c.J<PERSON>(http.StatusBadRequest, gin.H{
				"error":   "Invalid JSON format",
				"message": "Request body must be valid JSON",
				"code":    "INVALID_JSON",
			})
			c.Abort()
			return
		}

		if phone, exists := body["phone_number"]; exists {
			phoneStr, ok := phone.(string)
			if !ok || !v.phoneRegex.MatchString(phoneStr) {
				c.JSON(http.StatusBadRequest, gin.H{
					"error":   "Invalid phone number",
					"message": "Phone number must be in valid international format",
					"code":    "INVALID_PHONE",
				})
				c.Abort()
				return
			}
		}

		// Re-bind the body for the next handler
		c.Set("validated_body", body)
		c.Next()
	}
}

// ValidateEmail validates email format
func (v *ValidationMiddleware) ValidateEmail() gin.HandlerFunc {
	return func(c *gin.Context) {
		var body map[string]interface{}
		if err := c.ShouldBindJSON(&body); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "Invalid JSON format",
				"message": "Request body must be valid JSON",
				"code":    "INVALID_JSON",
			})
			c.Abort()
			return
		}

		if email, exists := body["email"]; exists {
			emailStr, ok := email.(string)
			if !ok || !v.emailRegex.MatchString(emailStr) {
				c.JSON(http.StatusBadRequest, gin.H{
					"error":   "Invalid email format",
					"message": "Email must be in valid format",
					"code":    "INVALID_EMAIL",
				})
				c.Abort()
				return
			}
		}

		c.Set("validated_body", body)
		c.Next()
	}
}

// ValidateAmount validates monetary amounts
func (v *ValidationMiddleware) ValidateAmount() gin.HandlerFunc {
	return func(c *gin.Context) {
		var body map[string]interface{}
		if err := c.ShouldBindJSON(&body); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "Invalid JSON format",
				"message": "Request body must be valid JSON",
				"code":    "INVALID_JSON",
			})
			c.Abort()
			return
		}

		if amount, exists := body["amount"]; exists {
			var amountFloat float64
			var err error

			switch v := amount.(type) {
			case float64:
				amountFloat = v
			case string:
				amountFloat, err = strconv.ParseFloat(v, 64)
				if err != nil {
					c.JSON(http.StatusBadRequest, gin.H{
						"error":   "Invalid amount format",
						"message": "Amount must be a valid number",
						"code":    "INVALID_AMOUNT_FORMAT",
					})
					c.Abort()
					return
				}
			default:
				c.JSON(http.StatusBadRequest, gin.H{
					"error":   "Invalid amount type",
					"message": "Amount must be a number",
					"code":    "INVALID_AMOUNT_TYPE",
				})
				c.Abort()
				return
			}

			// Validate amount constraints
			if amountFloat <= 0 {
				c.JSON(http.StatusBadRequest, gin.H{
					"error":   "Invalid amount",
					"message": "Amount must be greater than zero",
					"code":    "AMOUNT_TOO_LOW",
				})
				c.Abort()
				return
			}

			if amountFloat > 1000000 { // 1 million limit
				c.JSON(http.StatusBadRequest, gin.H{
					"error":   "Amount too large",
					"message": "Amount exceeds maximum allowed limit",
					"code":    "AMOUNT_TOO_HIGH",
				})
				c.Abort()
				return
			}

			// Update body with validated amount
			body["amount"] = amountFloat
		}

		c.Set("validated_body", body)
		c.Next()
	}
}

// ValidateStringLength validates string field lengths
func (v *ValidationMiddleware) ValidateStringLength(field string, minLen, maxLen int) gin.HandlerFunc {
	return func(c *gin.Context) {
		var body map[string]interface{}
		if err := c.ShouldBindJSON(&body); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "Invalid JSON format",
				"message": "Request body must be valid JSON",
				"code":    "INVALID_JSON",
			})
			c.Abort()
			return
		}

		if value, exists := body[field]; exists {
			valueStr, ok := value.(string)
			if !ok {
				c.JSON(http.StatusBadRequest, gin.H{
					"error":   fmt.Sprintf("Invalid %s type", field),
					"message": fmt.Sprintf("%s must be a string", field),
					"code":    "INVALID_TYPE",
				})
				c.Abort()
				return
			}

			valueStr = strings.TrimSpace(valueStr)
			if len(valueStr) < minLen {
				c.JSON(http.StatusBadRequest, gin.H{
					"error":   fmt.Sprintf("%s too short", field),
					"message": fmt.Sprintf("%s must be at least %d characters", field, minLen),
					"code":    "STRING_TOO_SHORT",
				})
				c.Abort()
				return
			}

			if len(valueStr) > maxLen {
				c.JSON(http.StatusBadRequest, gin.H{
					"error":   fmt.Sprintf("%s too long", field),
					"message": fmt.Sprintf("%s must be at most %d characters", field, maxLen),
					"code":    "STRING_TOO_LONG",
				})
				c.Abort()
				return
			}

			// Update body with trimmed value
			body[field] = valueStr
		}

		c.Set("validated_body", body)
		c.Next()
	}
}

// ValidateNumericID validates numeric ID parameters
func (v *ValidationMiddleware) ValidateNumericID(paramName string) gin.HandlerFunc {
	return func(c *gin.Context) {
		idStr := c.Param(paramName)
		if idStr == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "Missing ID parameter",
				"message": fmt.Sprintf("%s parameter is required", paramName),
				"code":    "MISSING_ID",
			})
			c.Abort()
			return
		}

		id, err := strconv.ParseUint(idStr, 10, 32)
		if err != nil || id == 0 {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "Invalid ID format",
				"message": fmt.Sprintf("%s must be a positive integer", paramName),
				"code":    "INVALID_ID_FORMAT",
			})
			c.Abort()
			return
		}

		// Store validated ID in context
		c.Set(fmt.Sprintf("validated_%s", paramName), uint(id))
		c.Next()
	}
}

// SanitizeInput sanitizes string inputs to prevent injection attacks
func (v *ValidationMiddleware) SanitizeInput() gin.HandlerFunc {
	return func(c *gin.Context) {
		fmt.Printf("MIDDLEWARE DEBUG: SanitizeInput called for %s %s\n", c.Request.Method, c.Request.URL.Path)

		var body map[string]interface{}
		if err := c.ShouldBindJSON(&body); err != nil {
			fmt.Printf("MIDDLEWARE ERROR: SanitizeInput JSON binding failed: %v\n", err)
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "Invalid JSON format",
				"message": "Request body must be valid JSON",
				"code":    "INVALID_JSON",
			})
			c.Abort()
			return
		}
		fmt.Printf("MIDDLEWARE DEBUG: SanitizeInput JSON binding successful\n")

		// Sanitize string fields recursively
		sanitizedBody := v.sanitizeValue(body)

		// Store both original and sanitized body for controller use
		c.Set("original_body", body)
		c.Set("sanitized_body", sanitizedBody)
		c.Next()
	}
}

// sanitizeValue recursively sanitizes values in maps, slices, and strings
func (v *ValidationMiddleware) sanitizeValue(value interface{}) interface{} {
	switch val := value.(type) {
	case string:
		return v.sanitizeString(val)
	case map[string]interface{}:
		sanitized := make(map[string]interface{})
		for k, mapVal := range val {
			sanitized[k] = v.sanitizeValue(mapVal)
		}
		return sanitized
	case []interface{}:
		sanitized := make([]interface{}, len(val))
		for i, sliceVal := range val {
			sanitized[i] = v.sanitizeValue(sliceVal)
		}
		return sanitized
	default:
		return value
	}
}

// sanitizeString performs comprehensive string sanitization
func (v *ValidationMiddleware) sanitizeString(str string) string {
	// Trim whitespace
	sanitized := strings.TrimSpace(str)

	// Check for suspicious patterns and block them
	if v.containsSuspiciousPatterns(sanitized) {
		return "" // Return empty string for suspicious input
	}

	// HTML encode dangerous characters for XSS prevention
	sanitized = strings.ReplaceAll(sanitized, "<", "&lt;")
	sanitized = strings.ReplaceAll(sanitized, ">", "&gt;")
	sanitized = strings.ReplaceAll(sanitized, "\"", "&quot;")
	sanitized = strings.ReplaceAll(sanitized, "'", "&#x27;")
	sanitized = strings.ReplaceAll(sanitized, "&", "&amp;")

	// Remove null bytes and control characters
	sanitized = strings.ReplaceAll(sanitized, "\x00", "")
	for i := 1; i <= 31; i++ {
		if i != 9 && i != 10 && i != 13 { // Keep tab, newline, carriage return
			sanitized = strings.ReplaceAll(sanitized, string(rune(i)), "")
		}
	}

	return sanitized
}

// containsSuspiciousPatterns checks for SQL injection and other attack patterns
func (v *ValidationMiddleware) containsSuspiciousPatterns(str string) bool {
	lowerStr := strings.ToLower(str)

	// SQL injection patterns
	sqlPatterns := []string{
		"union select", "drop table", "delete from", "insert into", "update set",
		"exec(", "execute(", "sp_", "xp_", "cmdshell", "openrowset",
		"' or '1'='1", "' or 1=1", "\" or \"1\"=\"1", "\" or 1=1",
		"'; drop", "\"; drop", "/*", "*/", "--", "#",
		"char(", "ascii(", "substring(", "waitfor delay",
		"benchmark(", "sleep(", "pg_sleep(", "dbms_pipe.receive_message",
	}

	// NoSQL injection patterns
	noSQLPatterns := []string{
		"$where", "$ne", "$in", "$nin", "$gt", "$lt", "$gte", "$lte",
		"$regex", "$exists", "$type", "$mod", "$all", "$size",
		"javascript:", "function(", "this.", "db.",
	}

	// XSS patterns
	xssPatterns := []string{
		"<script", "</script", "javascript:", "vbscript:", "onload=", "onerror=",
		"onclick=", "onmouseover=", "onfocus=", "onblur=", "onchange=",
		"eval(", "expression(", "url(", "import(", "document.cookie",
		"window.location", "document.write", "innerHTML", "outerHTML",
	}

	// LDAP injection patterns
	ldapPatterns := []string{
		"*)(uid=*", "*)(cn=*", "*)(&", "*))%00", "admin*", "*)(userpassword=*",
		"*)(objectclass=*", "*)(mail=*", "*)(sn=*", "*)(givenname=*",
	}

	// Command injection patterns
	cmdPatterns := []string{
		"&&", "||", ";", "|", "`", "$(", "${", "cat /", "ls -", "ps -",
		"netstat", "ifconfig", "ping ", "nslookup", "dig ", "wget ",
		"curl ", "nc ", "telnet ", "/bin/", "/usr/bin/", "/etc/passwd",
		"/etc/shadow", "cmd.exe", "powershell", "bash", "/bin/sh",
	}

	// Path traversal patterns
	pathPatterns := []string{
		"../", "..\\", "....//", "....\\\\", "%2e%2e%2f", "%2e%2e%5c",
		"%252e%252e%252f", "%c0%ae%c0%ae%c0%af", "..%c0%af", "..%c1%9c",
	}

	allPatterns := append(sqlPatterns, noSQLPatterns...)
	allPatterns = append(allPatterns, xssPatterns...)
	allPatterns = append(allPatterns, ldapPatterns...)
	allPatterns = append(allPatterns, cmdPatterns...)
	allPatterns = append(allPatterns, pathPatterns...)

	for _, pattern := range allPatterns {
		if strings.Contains(lowerStr, pattern) {
			return true
		}
	}

	return false
}

// ValidateRequestSize limits request body size to prevent DoS attacks
func (v *ValidationMiddleware) ValidateRequestSize(maxSizeBytes int64) gin.HandlerFunc {
	return func(c *gin.Context) {
		if c.Request.ContentLength > maxSizeBytes {
			c.JSON(http.StatusRequestEntityTooLarge, gin.H{
				"error":   "Request too large",
				"message": fmt.Sprintf("Request body exceeds maximum size of %d bytes", maxSizeBytes),
				"code":    "REQUEST_TOO_LARGE",
			})
			c.Abort()
			return
		}

		// Set a reader limit to prevent memory exhaustion
		c.Request.Body = http.MaxBytesReader(c.Writer, c.Request.Body, maxSizeBytes)
		c.Next()
	}
}

// ValidateContentType ensures only allowed content types are accepted
func (v *ValidationMiddleware) ValidateContentType(allowedTypes ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		fmt.Printf("MIDDLEWARE DEBUG: ValidateContentType called for %s %s\n", c.Request.Method, c.Request.URL.Path)

		if c.Request.Method == "POST" || c.Request.Method == "PUT" || c.Request.Method == "PATCH" {
			contentType := c.GetHeader("Content-Type")
			fmt.Printf("MIDDLEWARE DEBUG: Content-Type header: %s\n", contentType)

			if contentType == "" {
				c.JSON(http.StatusBadRequest, gin.H{
					"error":   "Missing content type",
					"message": "Content-Type header is required",
					"code":    "MISSING_CONTENT_TYPE",
				})
				c.Abort()
				return
			}

			allowed := false
			for _, allowedType := range allowedTypes {
				if strings.Contains(contentType, allowedType) {
					allowed = true
					break
				}
			}

			if !allowed {
				c.JSON(http.StatusUnsupportedMediaType, gin.H{
					"error":         "Unsupported content type",
					"message":       "Content type not allowed",
					"code":          "UNSUPPORTED_CONTENT_TYPE",
					"allowed_types": allowedTypes,
				})
				c.Abort()
				return
			}
		}

		c.Next()
	}
}

// ValidateHeaders validates required security headers
func (v *ValidationMiddleware) ValidateHeaders() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Check for required security headers
		userAgent := c.GetHeader("User-Agent")
		if userAgent == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "Missing User-Agent",
				"message": "User-Agent header is required",
				"code":    "MISSING_USER_AGENT",
			})
			c.Abort()
			return
		}

		// Block suspicious user agents
		suspiciousAgents := []string{
			"sqlmap", "nikto", "nmap", "masscan", "zap", "burp", "w3af",
			"havij", "pangolin", "jsql", "sqlninja", "bsqlbf",
		}

		lowerUA := strings.ToLower(userAgent)
		for _, suspicious := range suspiciousAgents {
			if strings.Contains(lowerUA, suspicious) {
				c.JSON(http.StatusForbidden, gin.H{
					"error":   "Blocked user agent",
					"message": "Your request has been blocked",
					"code":    "BLOCKED_USER_AGENT",
				})
				c.Abort()
				return
			}
		}

		c.Next()
	}
}

// ValidateRequired validates that required fields are present
func (v *ValidationMiddleware) ValidateRequired(fields ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		var body map[string]interface{}
		if err := c.ShouldBindJSON(&body); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "Invalid JSON format",
				"message": "Request body must be valid JSON",
				"code":    "INVALID_JSON",
			})
			c.Abort()
			return
		}

		for _, field := range fields {
			if value, exists := body[field]; !exists || value == nil || value == "" {
				c.JSON(http.StatusBadRequest, gin.H{
					"error":   "Missing required field",
					"message": fmt.Sprintf("Field '%s' is required", field),
					"code":    "MISSING_REQUIRED_FIELD",
				})
				c.Abort()
				return
			}
		}

		c.Set("validated_body", body)
		c.Next()
	}
}
