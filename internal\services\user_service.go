package services

import (
	"fmt"
	"time"

	"wallet-platform/internal/models"
	"wallet-platform/pkg/logger"

	"gorm.io/gorm"
)

// UserService handles user-related operations
type UserService struct {
	db     *gorm.DB
	logger *logger.Logger
}

// NewUserService creates a new user service
func NewUserService(db *gorm.DB, log *logger.Logger) *UserService {
	return &UserService{
		db:     db,
		logger: log,
	}
}

// C<PERSON><PERSON><PERSON> creates a new user with associated wallet
func (s *UserService) CreateUser(phoneNumber, walletType, firstName, lastName, email, passwordHash string) (*models.User, error) {
	// Start transaction
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Create wallet first
	wallet := models.Wallet{
		PhoneNumber:   phoneNumber,
		AccountNumber: s.generateAccountNumber(phoneNumber),
		WalletType:    walletType,
		Balance:       0.0,
		Currency:      "USD",
		Status:        "active",
		IsVerified:    false,
	}

	if err := tx.Create(&wallet).Error; err != nil {
		tx.Rollback()
		s.logger.LogError(err, map[string]interface{}{
			"action":       "create_wallet_for_user",
			"phone_number": phoneNumber,
		})
		return nil, fmt.Errorf("failed to create wallet: %w", err)
	}

	// Create user
	user := models.User{
		WalletID:     wallet.ID,
		FirstName:    firstName,
		LastName:     lastName,
		Email:        email,
		PhoneNumber:  phoneNumber,
		PasswordHash: passwordHash,
		IsVerified:   false,
		Status:       "active",
		LoginCount:   0,
		Language:     "en",
		Timezone:     "UTC",
		Currency:     "USD",
	}

	if err := tx.Create(&user).Error; err != nil {
		tx.Rollback()
		s.logger.LogError(err, map[string]interface{}{
			"action":       "create_user",
			"phone_number": phoneNumber,
			"email":        email,
		})
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		s.logger.LogError(err, map[string]interface{}{
			"action": "commit_user_creation",
		})
		return nil, fmt.Errorf("failed to commit user creation: %w", err)
	}

	s.logger.WithFields(map[string]interface{}{
		"user_id":        user.ID,
		"wallet_id":      wallet.ID,
		"phone_number":   phoneNumber,
		"account_number": wallet.AccountNumber,
	}).Info("User created successfully")

	return &user, nil
}

// GetUserByPhoneNumber retrieves a user by phone number
func (s *UserService) GetUserByPhoneNumber(phoneNumber string) (*models.User, error) {
	var user models.User
	if err := s.db.Preload("Wallet").Where("phone_number = ?", phoneNumber).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("user not found for phone number: %s", phoneNumber)
		}
		s.logger.LogError(err, map[string]interface{}{
			"action":       "get_user_by_phone",
			"phone_number": phoneNumber,
		})
		return nil, fmt.Errorf("failed to retrieve user: %w", err)
	}

	return &user, nil
}

// GetUserByEmail retrieves a user by email
func (s *UserService) GetUserByEmail(email string) (*models.User, error) {
	var user models.User
	if err := s.db.Preload("Wallet").Where("email = ?", email).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("user not found for email: %s", email)
		}
		s.logger.LogError(err, map[string]interface{}{
			"action": "get_user_by_email",
			"email":  email,
		})
		return nil, fmt.Errorf("failed to retrieve user: %w", err)
	}

	return &user, nil
}

// GetUserByWalletID retrieves a user by wallet ID
func (s *UserService) GetUserByWalletID(walletID uint) (*models.User, error) {
	var user models.User
	if err := s.db.Preload("Wallet").Where("wallet_id = ?", walletID).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("user not found for wallet ID: %d", walletID)
		}
		s.logger.LogError(err, map[string]interface{}{
			"action":    "get_user_by_wallet_id",
			"wallet_id": walletID,
		})
		return nil, fmt.Errorf("failed to retrieve user: %w", err)
	}

	return &user, nil
}

// SetVerificationCode sets verification code for a user
func (s *UserService) SetVerificationCode(userID uint, code string) error {
	expiryTime := time.Now().Add(15 * time.Minute) // 15 minutes expiry

	updates := map[string]interface{}{
		"verification_code":   code,
		"verification_expiry": expiryTime,
	}

	if err := s.db.Model(&models.User{}).Where("id = ?", userID).Updates(updates).Error; err != nil {
		s.logger.LogError(err, map[string]interface{}{
			"action":  "set_verification_code",
			"user_id": userID,
		})
		return fmt.Errorf("failed to set verification code: %w", err)
	}

	return nil
}

// VerifyCode verifies the verification code for a user
func (s *UserService) VerifyCode(userID uint, code string) bool {
	var user models.User
	if err := s.db.Where("id = ? AND verification_code = ?", userID, code).First(&user).Error; err != nil {
		return false
	}

	// Check if code is expired
	if user.VerificationExpiry != nil && time.Now().After(*user.VerificationExpiry) {
		return false
	}

	return true
}

// MarkAsVerified marks a user as verified
func (s *UserService) MarkAsVerified(userID uint) error {
	updates := map[string]interface{}{
		"is_verified":         true,
		"verification_code":   "",
		"verification_expiry": nil,
	}

	if err := s.db.Model(&models.User{}).Where("id = ?", userID).Updates(updates).Error; err != nil {
		s.logger.LogError(err, map[string]interface{}{
			"action":  "mark_as_verified",
			"user_id": userID,
		})
		return fmt.Errorf("failed to mark user as verified: %w", err)
	}

	return nil
}

// SetPasswordResetCode sets password reset code for a user
func (s *UserService) SetPasswordResetCode(userID uint, code string) error {
	expiryTime := time.Now().Add(30 * time.Minute) // 30 minutes expiry

	updates := map[string]interface{}{
		"password_reset_code":   code,
		"password_reset_expiry": expiryTime,
	}

	if err := s.db.Model(&models.User{}).Where("id = ?", userID).Updates(updates).Error; err != nil {
		s.logger.LogError(err, map[string]interface{}{
			"action":  "set_password_reset_code",
			"user_id": userID,
		})
		return fmt.Errorf("failed to set password reset code: %w", err)
	}

	return nil
}

// VerifyPasswordResetCode verifies the password reset code for a user
func (s *UserService) VerifyPasswordResetCode(userID uint, code string) bool {
	var user models.User
	if err := s.db.Where("id = ? AND password_reset_code = ?", userID, code).First(&user).Error; err != nil {
		return false
	}

	// Check if code is expired
	if user.PasswordResetExpiry != nil && time.Now().After(*user.PasswordResetExpiry) {
		return false
	}

	return true
}

// UpdatePassword updates the password for a user
func (s *UserService) UpdatePassword(userID uint, passwordHash string) error {
	updates := map[string]interface{}{
		"password_hash":         passwordHash,
		"password_reset_code":   "",
		"password_reset_expiry": nil,
	}

	if err := s.db.Model(&models.User{}).Where("id = ?", userID).Updates(updates).Error; err != nil {
		s.logger.LogError(err, map[string]interface{}{
			"action":  "update_password",
			"user_id": userID,
		})
		return fmt.Errorf("failed to update password: %w", err)
	}

	return nil
}

// UpdateLoginInfo updates login information for a user
func (s *UserService) UpdateLoginInfo(userID uint) error {
	updates := map[string]interface{}{
		"last_login_at": time.Now(),
		"login_count":   gorm.Expr("login_count + 1"),
	}

	if err := s.db.Model(&models.User{}).Where("id = ?", userID).Updates(updates).Error; err != nil {
		s.logger.LogError(err, map[string]interface{}{
			"action":  "update_login_info",
			"user_id": userID,
		})
		return fmt.Errorf("failed to update login info: %w", err)
	}

	return nil
}

// GetAllUsers retrieves all users with pagination and filtering
func (s *UserService) GetAllUsers(page, limit int, status, search string) ([]models.User, int64, error) {
	var users []models.User
	var total int64

	query := s.db.Model(&models.User{})

	// Apply filters
	if status != "" {
		query = query.Where("status = ?", status)
	}
	if search != "" {
		query = query.Where("first_name ILIKE ? OR last_name ILIKE ? OR email ILIKE ? OR phone_number ILIKE ?",
			"%"+search+"%", "%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		s.logger.LogError(err, map[string]interface{}{
			"action": "get_all_users_count",
			"status": status,
			"search": search,
		})
		return nil, 0, fmt.Errorf("failed to count users: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Offset(offset).Limit(limit).Find(&users).Error; err != nil {
		s.logger.LogError(err, map[string]interface{}{
			"action": "get_all_users",
			"page":   page,
			"limit":  limit,
		})
		return nil, 0, fmt.Errorf("failed to get users: %w", err)
	}

	return users, total, nil
}

// GetUserByID retrieves a user by ID
func (s *UserService) GetUserByID(userID uint) (*models.User, error) {
	var user models.User
	if err := s.db.First(&user, userID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("user not found")
		}
		s.logger.LogError(err, map[string]interface{}{
			"action":  "get_user_by_id",
			"user_id": userID,
		})
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	return &user, nil
}

// UpdateUser updates user information
func (s *UserService) UpdateUser(userID uint, updates map[string]interface{}) (*models.User, error) {
	var user models.User
	if err := s.db.First(&user, userID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("failed to find user: %w", err)
	}

	if err := s.db.Model(&user).Updates(updates).Error; err != nil {
		s.logger.LogError(err, map[string]interface{}{
			"action":  "update_user",
			"user_id": userID,
			"updates": updates,
		})
		return nil, fmt.Errorf("failed to update user: %w", err)
	}

	return &user, nil
}

// DeleteUser soft deletes a user
func (s *UserService) DeleteUser(userID uint) error {
	var user models.User
	if err := s.db.First(&user, userID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("user not found")
		}
		return fmt.Errorf("failed to find user: %w", err)
	}

	if err := s.db.Delete(&user).Error; err != nil {
		s.logger.LogError(err, map[string]interface{}{
			"action":  "delete_user",
			"user_id": userID,
		})
		return fmt.Errorf("failed to delete user: %w", err)
	}

	return nil
}

// SuspendUser suspends a user account
func (s *UserService) SuspendUser(userID uint, reason string) error {
	updates := map[string]interface{}{
		"status":            "suspended",
		"suspension_reason": reason,
		"suspended_at":      time.Now(),
	}

	if err := s.db.Model(&models.User{}).Where("id = ?", userID).Updates(updates).Error; err != nil {
		s.logger.LogError(err, map[string]interface{}{
			"action":  "suspend_user",
			"user_id": userID,
			"reason":  reason,
		})
		return fmt.Errorf("failed to suspend user: %w", err)
	}

	return nil
}

// UnsuspendUser unsuspends a user account
func (s *UserService) UnsuspendUser(userID uint) error {
	updates := map[string]interface{}{
		"status":            "active",
		"suspension_reason": "",
		"suspended_at":      nil,
	}

	if err := s.db.Model(&models.User{}).Where("id = ?", userID).Updates(updates).Error; err != nil {
		s.logger.LogError(err, map[string]interface{}{
			"action":  "unsuspend_user",
			"user_id": userID,
		})
		return fmt.Errorf("failed to unsuspend user: %w", err)
	}

	return nil
}

// UserToResponse converts a user model to response format
func (s *UserService) UserToResponse(user *models.User) *models.UserResponse {
	return &models.UserResponse{
		ID:          user.ID,
		WalletID:    user.WalletID,
		FirstName:   user.FirstName,
		LastName:    user.LastName,
		Email:       user.Email,
		PhoneNumber: user.PhoneNumber,
		IsVerified:  user.IsVerified,
		Status:      user.Status,
		LastLoginAt: user.LastLoginAt,
		LoginCount:  user.LoginCount,
		DateOfBirth: user.DateOfBirth,
		Gender:      user.Gender,
		Nationality: user.Nationality,
		Address:     user.Address,
		City:        user.City,
		Country:     user.Country,
		PostalCode:  user.PostalCode,
		Language:    user.Language,
		Timezone:    user.Timezone,
		Currency:    user.Currency,
		CreatedAt:   user.CreatedAt,
		UpdatedAt:   user.UpdatedAt,
	}
}

// Helper function to generate account number
func (s *UserService) generateAccountNumber(phoneNumber string) string {
	// Simple account number generation - in production, use a more sophisticated method
	timestamp := time.Now().Unix()
	return fmt.Sprintf("ACC%d", timestamp)
}
