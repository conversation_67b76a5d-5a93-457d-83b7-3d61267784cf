# Migration Guide: From Payment Engine to Standalone Wallet Platform

## Overview

This guide provides comprehensive instructions for migrating wallet and PayCard data from the main payment engine system to the standalone Wallet Platform. The migration process ensures data integrity, minimal downtime, and seamless transition.

## Migration Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Payment Engine │───▶│   Migration     │───▶│ Wallet Platform │
│   (Source DB)   │    │    Service      │    │  (Target DB)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Data Export   │    │   Validation    │    │  Data Import    │
│   & Mapping     │    │  & Transform    │    │  & Verification │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Data Migration Scope

The migration process transfers the following data:
- **Digital Wallets** - Phone-based wallet accounts with balances
- **PayCards** - Digital and physical card information
- **Transaction History** - Complete wallet and card transaction records
- **Security Data** - 2FA settings, device registrations, fraud alerts
- **User Preferences** - Spending limits, merchant restrictions
- **Webhook Configurations** - Event notification settings

## Pre-Migration Checklist

### 1. Environment Preparation

#### Source System (Payment Engine)
- [ ] Verify database connectivity and permissions
- [ ] Ensure all wallet and PayCard data is consistent
- [ ] Create database backup before migration
- [ ] Document current data schema and relationships
- [ ] Identify any custom fields or configurations

#### Target System (Wallet Platform)
- [ ] Deploy and configure Wallet Platform
- [ ] Verify database connectivity
- [ ] Run database migrations to create schema
- [ ] Configure environment variables
- [ ] Test API endpoints functionality

#### Migration Infrastructure
- [ ] Set up migration service with proper credentials
- [ ] Configure source and target database connections
- [ ] Verify network connectivity between systems
- [ ] Set up monitoring and logging
- [ ] Prepare rollback procedures

## Migration Methods

### 1. API-Based Migration (Recommended)

Use the built-in migration API endpoints for controlled, monitored migration.

#### Start Complete Migration
```bash
curl -X POST "http://localhost:8086/api/v1/migration/start" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

#### Migrate Specific Data Types
```bash
# Migrate only wallets
curl -X POST "http://localhost:8086/api/v1/migration/wallets" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Migrate only paycards
curl -X POST "http://localhost:8086/api/v1/migration/cards" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Migrate only transactions
curl -X POST "http://localhost:8086/api/v1/migration/transactions" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### Monitor Migration Progress
```bash
# Get overall status
curl -X GET "http://localhost:8086/api/v1/migration/status" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Get specific migration status
curl -X GET "http://localhost:8086/api/v1/migration/status/complete_migration" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Get migration summary
curl -X GET "http://localhost:8086/api/v1/migration/summary" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 2. Command Line Migration

Use the migration script for direct database migration.

#### Basic Migration
```bash
cd wallet-platform
go run scripts/migrate.go -operation=migrate
```

#### Migration with Custom Database Connections
```bash
go run scripts/migrate.go \
  -operation=migrate \
  -source="user:pass@tcp(source-host:3306)/payment_engine" \
  -target="user:pass@tcp(target-host:3306)/wallet_platform"
```

#### Dry Run (Test Without Changes)
```bash
go run scripts/migrate.go -operation=migrate -dry-run
```

#### Specific Data Type Migration
```bash
# Migrate only wallets
go run scripts/migrate.go -operation=migrate-wallets

# Migrate only cards
go run scripts/migrate.go -operation=migrate-cards

# Migrate only transactions
go run scripts/migrate.go -operation=migrate-transactions
```

## Migration Process

### Phase 1: Pre-Migration
1. **Backup Databases**
   ```bash
   mysqldump -u user -p payment_engine > payment_engine_backup.sql
   mysqldump -u user -p wallet_platform > wallet_platform_backup.sql
   ```

2. **Verify Source Data**
   ```sql
   -- Check wallet counts
   SELECT COUNT(*) FROM digital_wallets;
   
   -- Check paycard counts
   SELECT COUNT(*) FROM pay_cards;
   
   -- Check transaction counts
   SELECT COUNT(*) FROM digital_wallet_transactions;
   
   -- Check balance totals
   SELECT SUM(balance) FROM digital_wallets;
   ```

3. **Prepare Target Database**
   ```bash
   # Ensure target database is initialized
   go run cmd/migrate/main.go
   ```

### Phase 2: Migration Execution

1. **Start Migration**
   ```bash
   # Using API
   curl -X POST "http://localhost:8086/api/v1/migration/start"
   
   # Using CLI
   go run scripts/migrate.go -operation=migrate
   ```

2. **Monitor Progress**
   ```bash
   # Check status every few minutes
   curl -X GET "http://localhost:8086/api/v1/migration/status"
   ```

### Phase 3: Post-Migration Validation

1. **Validate Data Integrity**
   ```bash
   # Using API
   curl -X POST "http://localhost:8080/api/v1/migration/validate"
   
   # Using CLI
   go run scripts/migrate.go -operation=validate
   ```

2. **Manual Verification**
   ```sql
   -- Verify record counts match
   SELECT 'wallets' as table_name, COUNT(*) as count FROM wallets
   UNION ALL
   SELECT 'pay_cards', COUNT(*) FROM pay_cards
   UNION ALL
   SELECT 'wallet_transactions', COUNT(*) FROM wallet_transactions;
   
   -- Verify balance totals
   SELECT SUM(balance) as total_balance FROM wallets;
   ```

## Data Mapping

### Digital Wallets
| Source Field | Target Field | Notes |
|--------------|--------------|-------|
| id | id | Primary key preserved |
| phone_number | phone_number | Unique identifier |
| account_number | account_number | Account number preserved |
| wallet_type | wallet_type | individual/business/master |
| balance | balance | Balance amount |
| currency | currency | Currency code (SZL) |
| status | status | active/suspended/closed |
| is_verified | is_verified | KYC verification status |
| kyc_level | kyc_level | basic/enhanced/premium |
| email | email | Contact email |
| settings | settings | JSON settings |
| metadata | metadata | Additional metadata |
| created_at | created_at | Creation timestamp |
| updated_at | updated_at | Last update timestamp |
| - | daily_limit | New field (default: 1000) |
| - | monthly_limit | New field (default: 10000) |
| - | daily_spent | New field (default: 0) |
| - | monthly_spent | New field (default: 0) |
| - | last_limit_reset | New field (current time) |

### PayCards
| Source Field | Target Field | Notes |
|--------------|--------------|-------|
| id | id | Primary key preserved |
| card_number | card_number | QR code identifier |
| wallet_id | wallet_id | Foreign key to wallet |
| card_holder_name | card_holder_name | Cardholder name |
| card_type | card_type | standard/premium/business |
| status | status | active/suspended/blocked |
| spending_limit | spending_limit | Overall spending limit |
| daily_spending_limit | daily_spending_limit | Daily limit |
| monthly_spending_limit | monthly_spending_limit | Monthly limit |
| current_daily_spent | current_daily_spent | Current daily spending |
| current_monthly_spent | current_monthly_spent | Current monthly spending |
| last_spending_reset | last_spending_reset | Last reset timestamp |
| qr_code_data | qr_code_data | Encrypted QR data |
| security_pin | security_pin | Hashed PIN |
| is_pin_set | is_pin_set | PIN status |
| failed_pin_attempts | failed_pin_attempts | Failed attempts count |
| last_failed_pin_attempt | last_failed_pin_attempt | Last failure time |
| card_locked_until | card_locked_until | Lock expiry time |
| merchant_restrictions | merchant_restrictions | JSON restrictions |
| geographic_restrictions | geographic_restrictions | JSON geo restrictions |
| created_at | created_at | Creation timestamp |
| updated_at | updated_at | Last update timestamp |

### Wallet Transactions
| Source Field | Target Field | Notes |
|--------------|--------------|-------|
| id | id | Primary key preserved |
| wallet_id | wallet_id | Foreign key to wallet |
| type | type | Transaction type |
| amount | amount | Transaction amount |
| currency | currency | Currency code |
| status | status | completed/pending/failed |
| reference | reference | Unique reference |
| description | description | Transaction description |
| category | category | Transaction category |
| external_transaction_id | external_transaction_id | External reference |
| payment_method | payment_method | Payment method used |
| provider_code | provider_code | Provider identifier |
| fee | fee | Transaction fee |
| ip_address | ip_address | Client IP address |
| device_info | device_info | JSON device data |
| location_info | location_info | JSON location data |
| meta | meta | Additional metadata |
| created_at | created_at | Transaction timestamp |

## Error Handling

### Common Issues and Solutions

1. **Foreign Key Violations**
   - Ensure wallets are migrated before paycards
   - Verify wallet IDs exist in target database

2. **Duplicate Key Errors**
   - Check for existing data in target database
   - Use rollback if necessary: `go run scripts/migrate.go -operation=rollback`

3. **Connection Timeouts**
   - Increase timeout values in configuration
   - Process data in smaller batches

4. **Memory Issues**
   - Reduce batch sizes in migration service
   - Monitor system resources during migration

### Rollback Procedure

If migration fails or data integrity issues are found:

```bash
# Using API (requires confirmation)
curl -X POST "http://localhost:8080/api/v1/migration/rollback?confirm=true" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Using CLI (interactive confirmation)
go run scripts/migrate.go -operation=rollback
```

## Performance Considerations

1. **Batch Processing**: Migration processes data in configurable batches
2. **Indexing**: Ensure proper indexes exist on target tables
3. **Connection Pooling**: Configure appropriate database connection pools
4. **Memory Management**: Monitor memory usage during large migrations
5. **Parallel Processing**: Consider running different data types in parallel

## Security Considerations

1. **Access Control**: Restrict migration endpoints to admin users only
2. **Data Encryption**: Ensure sensitive data remains encrypted during transfer
3. **Audit Logging**: All migration activities are logged for audit purposes
4. **Network Security**: Use secure connections for database access

## Monitoring and Alerting

The migration service provides comprehensive monitoring:

1. **Real-time Progress**: Track migration progress via API
2. **Error Reporting**: Detailed error logs and reporting
3. **Performance Metrics**: Monitor throughput and response times
4. **Completion Notifications**: Automated notifications on completion

## Post-Migration Tasks

1. **Update Application Configuration**: Point applications to new database
2. **Update API Endpoints**: Redirect API calls to standalone platform
3. **Monitor System Performance**: Watch for performance issues
4. **User Communication**: Notify users of any service changes
5. **Cleanup**: Remove old data after successful validation (optional)

## Support and Troubleshooting

For migration issues:
1. Check migration logs in the application logs
2. Verify database connectivity and permissions
3. Ensure sufficient disk space and memory
4. Contact system administrators for infrastructure issues

## Testing Checklist

Before production migration:
- [ ] Test migration in staging environment
- [ ] Verify all data types migrate correctly
- [ ] Test rollback procedures
- [ ] Validate data integrity checks
- [ ] Test API endpoints after migration
- [ ] Verify application functionality
- [ ] Test performance under load
- [ ] Confirm backup and restore procedures
