package models

import (
	"time"

	"gorm.io/datatypes"
)

// Wallet represents a user's digital wallet in the standalone platform
type Wallet struct {
	ID            uint    `gorm:"primaryKey" json:"id"`
	PhoneNumber   string  `gorm:"type:varchar(20);uniqueIndex;not null" json:"phone_number"`
	AccountNumber string  `gorm:"type:varchar(20);uniqueIndex;not null" json:"account_number"`
	WalletType    string  `gorm:"type:varchar(20);not null;default:'individual'" json:"wallet_type"` // individual, business, master
	Balance       float64 `gorm:"type:decimal(15,2);default:0" json:"balance"`
	Currency      string  `gorm:"type:varchar(10);default:'SZL'" json:"currency"`
	Status        string  `gorm:"type:varchar(20);default:'active'" json:"status"`      // active, suspended, closed
	IsVerified    bool    `gorm:"default:false" json:"is_verified"`                     // KYC verification status
	KYCLevel      string  `gorm:"type:varchar(20);default:'basic'" json:"kyc_level"`    // basic, enhanced, premium
	Email         *string `gorm:"type:varchar(100);uniqueIndex" json:"email,omitempty"` // Optional email for communication

	// Enhanced limits and controls
	DailyLimit     float64    `gorm:"type:decimal(15,2);default:1000" json:"daily_limit"`
	MonthlyLimit   float64    `gorm:"type:decimal(15,2);default:10000" json:"monthly_limit"`
	DailySpent     float64    `gorm:"type:decimal(15,2);default:0" json:"daily_spent"`
	MonthlySpent   float64    `gorm:"type:decimal(15,2);default:0" json:"monthly_spent"`
	LastLimitReset *time.Time `json:"last_limit_reset"`

	// Metadata and settings
	Settings datatypes.JSON `json:"settings"` // JSON object for wallet settings
	Metadata datatypes.JSON `json:"metadata"` // Additional wallet metadata

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// WalletTransaction represents transactions in digital wallets
type WalletTransaction struct {
	ID           uint    `gorm:"primaryKey" json:"id"`
	WalletID     uint    `gorm:"index;not null" json:"wallet_id"`
	Wallet       Wallet  `gorm:"constraint:OnDelete:CASCADE" json:"wallet"`
	Type         string  `gorm:"type:varchar(20);not null" json:"type"` // credit, debit, transfer_in, transfer_out
	Amount       float64 `gorm:"type:decimal(15,2);not null" json:"amount"`
	BalanceAfter float64 `gorm:"type:decimal(15,2);not null" json:"balance_after"` // balance after transaction
	Reference    string  `gorm:"type:varchar(100);index" json:"reference"`
	Description  string  `gorm:"type:varchar(255)" json:"description"`
	Category     string  `gorm:"type:varchar(50)" json:"category"` // payment, transfer, topup, withdrawal, fee, service_subscription

	// Enhanced transaction details
	ExternalTransactionID *string `gorm:"type:varchar(100);index" json:"external_transaction_id"` // Reference to payment engine transaction
	PaymentMethod         string  `gorm:"type:varchar(50)" json:"payment_method"`                 // mobile_money, bank_transfer, card, etc.
	ProviderCode          string  `gorm:"type:varchar(50)" json:"provider_code"`                  // MTN_MOMO, NEDBANK, etc.
	Fee                   float64 `gorm:"type:decimal(15,2);default:0" json:"fee"`

	// Location and device info
	IPAddress    string         `gorm:"type:varchar(45)" json:"ip_address"`
	DeviceInfo   datatypes.JSON `json:"device_info"`   // Device information
	LocationInfo datatypes.JSON `json:"location_info"` // GPS coordinates if available

	Meta      datatypes.JSON `json:"meta"` // additional transaction data
	CreatedAt time.Time      `json:"created_at"`
}

// WalletTransfer represents wallet-to-wallet transfers
type WalletTransfer struct {
	ID            uint    `gorm:"primaryKey" json:"id"`
	Reference     string  `gorm:"type:varchar(100);uniqueIndex;not null" json:"reference"`
	FromWalletID  uint    `gorm:"index;not null" json:"from_wallet_id"`
	FromWallet    Wallet  `gorm:"foreignKey:FromWalletID;constraint:OnDelete:RESTRICT" json:"from_wallet"`
	ToWalletID    uint    `gorm:"index;not null" json:"to_wallet_id"`
	ToWallet      Wallet  `gorm:"foreignKey:ToWalletID;constraint:OnDelete:RESTRICT" json:"to_wallet"`
	Amount        float64 `gorm:"type:decimal(15,2);not null" json:"amount"`
	Fee           float64 `gorm:"type:decimal(15,2);default:0" json:"fee"`
	TotalAmount   float64 `gorm:"type:decimal(15,2);not null" json:"total_amount"`  // amount + fee
	Status        string  `gorm:"type:varchar(20);default:'pending'" json:"status"` // pending, processing, completed, failed, cancelled
	Description   string  `gorm:"type:varchar(255)" json:"description"`
	FailureReason string  `gorm:"type:varchar(255)" json:"failure_reason"`

	// Enhanced transfer details
	TransferType   string     `gorm:"type:varchar(20);default:'instant'" json:"transfer_type"` // instant, scheduled, recurring
	ScheduledFor   *time.Time `json:"scheduled_for"`
	RecurrenceRule string     `gorm:"type:varchar(100)" json:"recurrence_rule"` // RRULE format for recurring transfers

	// Security and audit
	IPAddress         string `gorm:"type:varchar(45)" json:"ip_address"`
	DeviceFingerprint string `gorm:"type:varchar(64)" json:"device_fingerprint"`
	RequiredApprovals int    `gorm:"default:0" json:"required_approvals"`
	CurrentApprovals  int    `gorm:"default:0" json:"current_approvals"`

	ProcessedAt *time.Time `json:"processed_at"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
}

// TransactionLimit represents transaction limits for enhanced security
type TransactionLimit struct {
	ID          uint       `json:"id" gorm:"primaryKey"`
	WalletID    uint       `json:"wallet_id" gorm:"index;not null"`
	Wallet      Wallet     `json:"wallet" gorm:"constraint:OnDelete:CASCADE"`
	LimitType   string     `json:"limit_type" gorm:"type:varchar(20);not null"` // daily, weekly, monthly, per_transaction
	Amount      float64    `json:"amount" gorm:"type:decimal(15,2);not null"`
	CurrentUsed float64    `json:"current_used" gorm:"type:decimal(15,2);default:0"`
	ResetPeriod string     `json:"reset_period" gorm:"type:varchar(20)"` // daily, weekly, monthly
	LastReset   *time.Time `json:"last_reset"`
	IsActive    bool       `json:"is_active" gorm:"default:true"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
}

// WalletBalance represents balance snapshots for audit and reconciliation
type WalletBalance struct {
	ID              uint      `gorm:"primaryKey" json:"id"`
	WalletID        uint      `gorm:"index;not null" json:"wallet_id"`
	Wallet          Wallet    `gorm:"constraint:OnDelete:CASCADE" json:"wallet"`
	Balance         float64   `gorm:"type:decimal(15,2);not null" json:"balance"`
	PreviousBalance float64   `gorm:"type:decimal(15,2);not null" json:"previous_balance"`
	ChangeAmount    float64   `gorm:"type:decimal(15,2);not null" json:"change_amount"`
	ChangeReason    string    `gorm:"type:varchar(100);not null" json:"change_reason"`
	TransactionID   *uint     `gorm:"index" json:"transaction_id"`
	SnapshotType    string    `gorm:"type:varchar(20);not null" json:"snapshot_type"` // transaction, reconciliation, adjustment
	CreatedAt       time.Time `json:"created_at"`
}

// WalletRequest represents a request to create a new wallet
type WalletRequest struct {
	PhoneNumber string `json:"phone_number" binding:"required,min=8,max=15"`
	WalletType  string `json:"wallet_type" binding:"required,oneof=individual business master"`
	IsVerified  bool   `json:"is_verified"`
	KYCLevel    string `json:"kyc_level" binding:"omitempty,oneof=basic enhanced premium"`
	Email       string `json:"email" binding:"omitempty,email"`
}

// WalletUpdateRequest represents a request to update wallet settings
type WalletUpdateRequest struct {
	Status       *string  `json:"status" binding:"omitempty,oneof=active suspended closed"`
	DailyLimit   *float64 `json:"daily_limit" binding:"omitempty,min=0"`
	MonthlyLimit *float64 `json:"monthly_limit" binding:"omitempty,min=0"`
	Email        *string  `json:"email" binding:"omitempty,email"`
	IsVerified   *bool    `json:"is_verified"`
	KYCLevel     *string  `json:"kyc_level" binding:"omitempty,oneof=basic enhanced premium"`
}

// TransferRequest represents a request to transfer funds between wallets
type TransferRequest struct {
	FromPhoneNumber string  `json:"from_phone_number" binding:"required"`
	ToPhoneNumber   string  `json:"to_phone_number" binding:"required"`
	Amount          float64 `json:"amount" binding:"required,gt=0"`
	Reference       string  `json:"reference" binding:"required"`
	Description     string  `json:"description"`
	TransferType    string  `json:"transfer_type" binding:"omitempty,oneof=instant scheduled recurring"`
	ScheduledFor    *string `json:"scheduled_for"` // ISO 8601 format
	RecurrenceRule  string  `json:"recurrence_rule"`
}

// TopupRequest represents a request to top up a wallet
type TopupRequest struct {
	Amount        float64                `json:"amount" binding:"required,gt=0"`
	PaymentMethod string                 `json:"payment_method" binding:"required"`
	ProviderCode  string                 `json:"provider_code" binding:"required"`
	Reference     string                 `json:"reference" binding:"required"`
	Metadata      map[string]interface{} `json:"metadata"`
}

// TransactionHistoryRequest represents a request for transaction history
type TransactionHistoryRequest struct {
	Page      int    `json:"page" binding:"omitempty,min=1"`
	Limit     int    `json:"limit" binding:"omitempty,min=1,max=100"`
	Type      string `json:"type" binding:"omitempty,oneof=credit debit transfer_in transfer_out"`
	Category  string `json:"category"`
	StartDate string `json:"start_date"` // ISO 8601 format
	EndDate   string `json:"end_date"`   // ISO 8601 format
}

// WalletResponse represents the response format for wallet operations
type WalletResponse struct {
	ID            uint      `json:"id"`
	PhoneNumber   string    `json:"phone_number"`
	AccountNumber string    `json:"account_number"`
	WalletType    string    `json:"wallet_type"`
	Balance       float64   `json:"balance"`
	Currency      string    `json:"currency"`
	Status        string    `json:"status"`
	IsVerified    bool      `json:"is_verified"`
	KYCLevel      string    `json:"kyc_level"`
	DailyLimit    float64   `json:"daily_limit"`
	MonthlyLimit  float64   `json:"monthly_limit"`
	DailySpent    float64   `json:"daily_spent"`
	MonthlySpent  float64   `json:"monthly_spent"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
}

// TransferResponse represents the response format for transfer operations
type TransferResponse struct {
	ID           uint       `json:"id"`
	Reference    string     `json:"reference"`
	FromWalletID uint       `json:"from_wallet_id"`
	ToWalletID   uint       `json:"to_wallet_id"`
	Amount       float64    `json:"amount"`
	Fee          float64    `json:"fee"`
	TotalAmount  float64    `json:"total_amount"`
	Status       string     `json:"status"`
	Description  string     `json:"description"`
	TransferType string     `json:"transfer_type"`
	ProcessedAt  *time.Time `json:"processed_at"`
	CreatedAt    time.Time  `json:"created_at"`
}

// TransactionResponse represents the response format for transaction operations
type TransactionResponse struct {
	ID                    uint      `json:"id"`
	Type                  string    `json:"type"`
	Amount                float64   `json:"amount"`
	BalanceAfter          float64   `json:"balance_after"`
	Reference             string    `json:"reference"`
	Description           string    `json:"description"`
	Category              string    `json:"category"`
	ExternalTransactionID *string   `json:"external_transaction_id"`
	PaymentMethod         string    `json:"payment_method"`
	ProviderCode          string    `json:"provider_code"`
	Fee                   float64   `json:"fee"`
	CreatedAt             time.Time `json:"created_at"`
}
