# Development Environment Configuration
# This file contains development-specific settings

app:
  name: "Wallet Platform (Dev)"
  version: "1.0.0"
  environment: "development"
  debug: true

server:
  port: 8086
  host: "0.0.0.0"
  environment: "development"
  read_timeout: 30
  write_timeout: 30
  idle_timeout: 120
  cors:
    allow_origins:
      - "http://localhost:3000"
      - "http://localhost:3001"
      - "http://127.0.0.1:3000"
      - "http://127.0.0.1:3001"
    allow_methods:
      - "GET"
      - "POST"
      - "PUT"
      - "DELETE"
      - "OPTIONS"
    allow_headers:
      - "*"
    allow_credentials: true
    max_age: 86400

database:
  driver: "mysql"
  host: "localhost"
  port: 3306
  username: ""  # Set via DATABASE_USERNAME env var
  password: ""  # Set via DATABASE_PASSWORD env var
  database: "wallet_platform_dev"
  ssl_mode: "false"  # Can be disabled in development
  max_open_conns: 10  # Lower for development
  max_idle_conns: 2
  conn_max_lifetime: 300
  conn_max_idle_time: 60

redis:
  enabled: false  # Optional in development
  host: "localhost"
  port: 6379
  password: ""
  database: 0
  pool_size: 5
  min_idle_conns: 1
  max_retries: 3

jwt:
  secret_key: ""  # Set via JWT_SECRET_KEY env var
  expiration_time: 7200  # 2 hours for development
  refresh_time: 86400    # 24 hours
  issuer: "wallet-platform-dev"
  audience: "wallet-platform-users"

log:
  level: "debug"  # More verbose logging in development
  format: "text"  # Human-readable format
  output: "stdout"

rate_limit:
  enabled: false  # Disabled for easier development
  default_limit: 1000
  default_window: 60
  endpoint_limits:
    "/api/v1/auth/login":
      limit: 50  # More lenient in development
      window: 300

security:
  encryption_key: ""  # Set via SECURITY_ENCRYPTION_KEY env var
  hash_salt: ""       # Set via SECURITY_HASH_SALT env var
  max_login_attempts: 10  # More lenient in development
  lockout_duration: 300   # 5 minutes
  session_timeout: 7200   # 2 hours
  require_https: false    # Can be disabled in development
  csrf_protection: false  # Can be disabled for easier testing
  content_type_no_sniff: true

internal_api:
  enabled: true
  key: ""  # Set via INTERNAL_API_KEY env var
  allowed_services:
    - "payment-engine"
    - "user-service"
    - "notification-service"
    - "admin-panel"
    - "test-service"  # Additional for development
  rate_limit:
    enabled: false  # Disabled for development
    limit: 10000
    window: 60

external:
  payment_engine:
    base_url: "http://localhost:8000"  # Local development server
    api_key: ""   # Set via EXTERNAL_PAYMENT_ENGINE_API_KEY env var
    timeout: 30
    retry_count: 3
  
  sms:
    provider: "centurion"
    api_key: ""   # Set via CENTURION_SMS_API_KEY env var
    api_url: "https://auth.centurionbd.com/api/v1/sms/send"

  email:
    provider: "centurion"
    api_key: ""   # Set via CENTURION_EMAIL_API_KEY env var
    api_url: "https://auth.centurionbd.com/api/v1/email/send"
  
  webhook:
    secret: ""    # Set via EXTERNAL_WEBHOOK_SECRET env var
    timeout: 30
    max_retries: 3

# Platform Fees Configuration (in SZL - Swazi Lilangeni)
# Lower fees for development/testing
fees:
  card_creation:
    standard: 5.00     # Reduced for testing
    premium: 10.00     # Reduced for testing
    business: 20.00    # Reduced for testing
  withdrawal_percentage: 0.5             # 0.5% withdrawal fee (reduced)
  transaction_percentage: 0.25           # 0.25% transaction fee (reduced)
  monthly_maintenance: 2.00              # Reduced for testing
  card_replacement: 5.00                 # Reduced for testing
  international_transaction_percentage: 1.0  # 1% international transaction fee (reduced)
  atm_withdrawal: 2.00                   # Reduced for testing

# Card Limits Configuration (in SZL - Swazi Lilangeni)
# Higher limits for development/testing convenience
card_limits:
  standard:
    spending_limit: 10000.00       # Higher for testing
    daily_spending_limit: 5000.00  # Higher for testing
    monthly_spending_limit: 50000.00  # Higher for testing
    max_cards_per_wallet: 5        # More cards for testing
  premium:
    spending_limit: 25000.00       # Higher for testing
    daily_spending_limit: 10000.00 # Higher for testing
    monthly_spending_limit: 100000.00  # Higher for testing
    max_cards_per_wallet: 8        # More cards for testing
  business:
    spending_limit: 100000.00      # Higher for testing
    daily_spending_limit: 50000.00 # Higher for testing
    monthly_spending_limit: 500000.00  # Higher for testing
    max_cards_per_wallet: 15       # More cards for testing
  creation:
    max_cards_per_day: 10          # More cards per day for testing
    cooldown_hours: 1              # Shorter cooldown for testing
  transaction:
    min_amount: 0.10               # Lower minimum for testing
    max_amount_standard: 10000.00  # Higher for testing
    max_amount_premium: 25000.00   # Higher for testing
    max_amount_business: 100000.00 # Higher for testing
  atm:
    daily_limit_standard: 10000.00   # Higher for testing
    daily_limit_premium: 25000.00    # Higher for testing
    daily_limit_business: 50000.00   # Higher for testing
    monthly_limit_standard: 100000.00 # Higher for testing
    monthly_limit_premium: 250000.00  # Higher for testing
    monthly_limit_business: 500000.00 # Higher for testing
