package controllers

import (
	"fmt"
	"net/http"
	"strconv"

	"wallet-platform/internal/models"
	"wallet-platform/internal/services"

	"github.com/gin-gonic/gin"
)

// WalletController handles wallet-related HTTP requests
type WalletController struct {
	container *services.Container
}

// NewWalletController creates a new wallet controller
func NewWalletController(container *services.Container) *WalletController {
	return &WalletController{
		container: container,
	}
}

// CreateWallet creates a new digital wallet
func (wc *WalletController) CreateWallet(c *gin.Context) {
	fmt.Printf("CONTROLLER DEBUG: CreateWallet method called! %s %s\n", c.Request.Method, c.Request.URL.Path)

	// Enhanced debugging for nil pointer issues
	defer func() {
		if r := recover(); r != nil {
			// Log the panic with stack trace and detailed context
			errorMsg := fmt.Sprintf("PANIC in CreateWallet: %v", r)

			// Try to log even if logger might be nil
			if wc != nil && wc.container != nil && wc.container.Logger != nil {
				wc.container.Logger.LogError(fmt.Errorf(errorMsg), map[string]interface{}{
					"method":     "CreateWallet",
					"path":       c.Request.URL.Path,
					"panic":      fmt.Sprintf("%v", r),
					"controller": "WalletController",
					"step":       "panic_recovery",
				})
			} else {
				// Fallback logging to stdout if logger is nil
				fmt.Printf("PANIC RECOVERY - %s\n", errorMsg)
				fmt.Printf("Controller nil: %v, Container nil: %v\n", wc == nil, wc == nil || wc.container == nil)
			}

			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "INTERNAL_SERVER_ERROR",
				Message: "An unexpected error occurred",
				Code:    "500",
			})
		}
	}()

	// Step 1: Check controller initialization
	fmt.Printf("DEBUG: Step 1 - Checking controller initialization\n")
	if wc == nil {
		fmt.Printf("ERROR: WalletController is nil\n")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "CONTROLLER_NOT_INITIALIZED",
			Message: "Wallet controller is not initialized",
			Code:    "500",
		})
		return
	}
	fmt.Printf("DEBUG: WalletController is not nil\n")

	// Step 2: Check container initialization
	fmt.Printf("DEBUG: Step 2 - Checking container initialization\n")
	if wc.container == nil {
		fmt.Printf("ERROR: Service container is nil\n")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "CONTAINER_NOT_INITIALIZED",
			Message: "Service container is not initialized",
			Code:    "500",
		})
		return
	}
	fmt.Printf("DEBUG: Service container is not nil\n")

	// Step 3: Check logger
	fmt.Printf("DEBUG: Step 3 - Checking logger\n")
	if wc.container.Logger == nil {
		fmt.Printf("WARNING: Logger is nil\n")
	} else {
		fmt.Printf("DEBUG: Logger is available\n")
		wc.container.Logger.WithFields(map[string]interface{}{
			"method": "CreateWallet",
			"step":   "initialization_check",
		}).Info("Starting wallet creation process")
	}

	// Step 4: Check WalletService
	fmt.Printf("DEBUG: Step 4 - Checking WalletService\n")
	if wc.container.WalletService == nil {
		fmt.Printf("ERROR: WalletService is nil\n")
		if wc.container.Logger != nil {
			wc.container.Logger.LogError(fmt.Errorf("WalletService is nil"), map[string]interface{}{
				"method": "CreateWallet",
				"path":   c.Request.URL.Path,
				"step":   "service_check",
			})
		}
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "WALLET_SERVICE_NOT_INITIALIZED",
			Message: "Wallet service is not initialized",
			Code:    "500",
		})
		return
	}
	fmt.Printf("DEBUG: WalletService is not nil\n")

	// Step 5: Parse request JSON (direct binding)
	fmt.Printf("DEBUG: Step 5 - Parsing request JSON\n")
	var req models.WalletRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		fmt.Printf("ERROR: JSON binding failed: %v\n", err)
		if wc.container.Logger != nil {
			wc.container.Logger.LogError(err, map[string]interface{}{
				"method": "CreateWallet",
				"error":  "JSON_BIND_FAILED",
				"step":   "request_parsing",
			})
		}
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}
	fmt.Printf("DEBUG: Request parsed successfully - Phone: %s, Type: %s\n", req.PhoneNumber, req.WalletType)

	// Step 6: Log the request
	fmt.Printf("DEBUG: Step 6 - Logging request details\n")
	if wc.container.Logger != nil {
		wc.container.Logger.WithFields(map[string]interface{}{
			"phone_number": req.PhoneNumber,
			"method":       "CreateWallet",
			"step":         "request_logged",
		}).Info("Creating wallet")
	}

	// Step 7: Call WalletService
	fmt.Printf("DEBUG: Step 7 - Calling WalletService.CreateWallet\n")
	walletType := req.WalletType
	if walletType == "" {
		walletType = "individual" // Default to individual if not specified
	}
	wallet, err := wc.container.WalletService.CreateWallet(req.PhoneNumber, walletType)
	if err != nil {
		fmt.Printf("ERROR: WalletService.CreateWallet failed: %v\n", err)
		if wc.container.Logger != nil {
			wc.container.Logger.LogError(err, map[string]interface{}{
				"method":       "CreateWallet",
				"phone_number": req.PhoneNumber,
				"error":        "WALLET_CREATION_FAILED",
				"step":         "service_call",
			})
		}
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "WALLET_CREATION_FAILED",
			Message: err.Error(),
			Code:    "500",
		})
		return
	}
	fmt.Printf("DEBUG: WalletService.CreateWallet succeeded\n")

	// Step 8: Return response
	fmt.Printf("DEBUG: Step 8 - Returning success response\n")
	c.JSON(http.StatusOK, models.DataResponse{
		Success: true,
		Message: "Wallet created successfully",
		Data:    wallet,
	})
	fmt.Printf("DEBUG: Response sent successfully\n")
}

// GetWallet gets wallet details
func (wc *WalletController) GetWallet(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_ID",
			Message: "Invalid wallet ID format",
			Code:    "400",
		})
		return
	}

	_, err = wc.container.WalletService.GetWallet(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{
			Error:   "WALLET_NOT_FOUND",
			Message: "Wallet not found",
			Code:    "404",
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "Wallet retrieved successfully",
	})
}

// GetBalance gets wallet balance
func (wc *WalletController) GetBalance(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_ID",
			Message: "Invalid wallet ID format",
			Code:    "400",
		})
		return
	}

	_, err = wc.container.WalletService.GetBalance(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "BALANCE_FETCH_FAILED",
			Message: "Failed to fetch balance",
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "Balance retrieved successfully",
	})
}

// Transfer transfers money between wallets
func (wc *WalletController) Transfer(c *gin.Context) {
	var req TransferRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	_, err := wc.container.WalletService.Transfer(req.FromWalletID, req.ToWalletID, req.Amount, req.Description)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "TRANSFER_FAILED",
			Message: err.Error(),
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "Transfer completed successfully",
	})
}

// GetTransactionHistory gets wallet transaction history
func (wc *WalletController) GetTransactionHistory(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_ID",
			Message: "Invalid wallet ID format",
			Code:    "400",
		})
		return
	}

	_, err = wc.container.WalletService.GetTransactionHistory(uint(id), map[string]interface{}{})
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "TRANSACTIONS_FETCH_FAILED",
			Message: "Failed to fetch transactions",
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "Transactions retrieved successfully",
	})
}

// FundOrUnfundWalletRequest is the request body for funding or unfunding a wallet by phone
type FundOrUnfundWalletRequest struct {
	Phone     string  `json:"phone" binding:"required"`
	Amount    float64 `json:"amount" binding:"required,gt=0"`
	Direction string  `json:"direction" binding:"required,oneof=debit credit"`
}

// FundOrUnfundWallet handles funding or unfunding a wallet by phone and amount
func (wc *WalletController) FundOrUnfundWallet(c *gin.Context) {
	var req FundOrUnfundWalletRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format or missing fields",
			Code:    "400",
		})
		return
	}

	err := wc.container.WalletService.FundOrUnfundWallet(req.Phone, req.Amount, req.Direction)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "FUND_OR_UNFUND_FAILED",
			Message: err.Error(),
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "Wallet updated successfully",
	})
}

// Deposit deposits money into wallet
func (wc *WalletController) Deposit(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_ID",
			Message: "Invalid wallet ID format",
			Code:    "400",
		})
		return
	}

	var req DepositRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	_, err = wc.container.WalletService.TopupWallet(uint(id), req.Amount, req.TopupPhone, req.Source, req.Reference)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "DEPOSIT_FAILED",
			Message: err.Error(),
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "Deposit completed successfully",
	})
}

type WithdrawRequest struct {
	Amount           float64 `json:"amount" binding:"required,gt=0"`
	WithdrawalMethod string  `json:"withdrawal_method" binding:"required"`
	Destination      string  `json:"destination" binding:"required"`
	Reference        string  `json:"reference"`
	Description      string  `json:"description"`
}

// Withdraw withdraws money from wallet
func (wc *WalletController) Withdraw(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_ID",
			Message: "Invalid wallet ID format",
			Code:    "400",
		})
		return
	}

	var req WithdrawRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	transaction, err := wc.container.WalletService.WithdrawFunds(uint(id), req.Amount, req.Destination, req.Reference)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "WITHDRAWAL_FAILED",
			Message: err.Error(),
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, models.DataResponse{
		Success: true,
		Message: "Withdrawal completed successfully",
		Data:    transaction,
	})
}

// GetPlatformWallet retrieves the platform wallet information
func (wc *WalletController) GetPlatformWallet(c *gin.Context) {
	platformWallet, err := wc.container.WalletService.GetPlatformWallet()
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "PLATFORM_WALLET_ERROR",
			Message: err.Error(),
			Code:    "500",
		})
		return
	}

	// Convert to response format
	response := models.WalletResponse{
		ID:           platformWallet.ID,
		PhoneNumber:  platformWallet.PhoneNumber,
		WalletType:   platformWallet.WalletType,
		Status:       platformWallet.Status,
		Balance:      platformWallet.Balance,
		Currency:     platformWallet.Currency,
		DailyLimit:   platformWallet.DailyLimit,
		MonthlyLimit: platformWallet.MonthlyLimit,
		DailySpent:   platformWallet.DailySpent,
		MonthlySpent: platformWallet.MonthlySpent,
		IsVerified:   platformWallet.IsVerified,
		CreatedAt:    platformWallet.CreatedAt,
		UpdatedAt:    platformWallet.UpdatedAt,
	}

	c.JSON(http.StatusOK, models.DataResponse{
		Success: true,
		Message: "Platform wallet retrieved successfully",
		Data:    response,
	})
}

// GetMyWallet gets current user's wallet
func (wc *WalletController) GetMyWallet(c *gin.Context) {
	// Simplified - just return success
	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "Wallet retrieved successfully",
	})
}

// UpdateWallet updates wallet information
func (wc *WalletController) UpdateWallet(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_ID",
			Message: "Invalid wallet ID format",
			Code:    "400",
		})
		return
	}

	var req map[string]interface{}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	_, err = wc.container.WalletService.UpdateWallet(uint(id), req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "UPDATE_FAILED",
			Message: err.Error(),
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "Wallet updated successfully",
	})
}

// GetWalletBalance gets wallet balance
func (wc *WalletController) GetWalletBalance(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_ID",
			Message: "Invalid wallet ID format",
			Code:    "400",
		})
		return
	}

	_, err = wc.container.WalletService.GetBalance(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "BALANCE_FETCH_FAILED",
			Message: "Failed to fetch balance",
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "Balance retrieved successfully",
	})
}

// TopupWallet tops up wallet
func (wc *WalletController) TopupWallet(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_ID",
			Message: "Invalid wallet ID format",
			Code:    "400",
		})
		return
	}

	var req DepositRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	_, err = wc.container.WalletService.TopupWallet(uint(id), req.Amount, req.TopupPhone, req.Source, req.Reference)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "TOPUP_FAILED",
			Message: err.Error(),
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "Topup completed successfully",
	})
}

// TransferFunds transfers funds between wallets
func (wc *WalletController) TransferFunds(c *gin.Context) {
	var req TransferRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	_, err := wc.container.WalletService.Transfer(req.FromWalletID, req.ToWalletID, req.Amount, req.Description)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "TRANSFER_FAILED",
			Message: err.Error(),
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "Transfer completed successfully",
	})
}

// GetWalletTransactions gets wallet transactions
func (wc *WalletController) GetWalletTransactions(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_ID",
			Message: "Invalid wallet ID format",
			Code:    "400",
		})
		return
	}

	_, err = wc.container.WalletService.GetTransactionHistory(uint(id), map[string]interface{}{})
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "TRANSACTIONS_FETCH_FAILED",
			Message: "Failed to fetch transactions",
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "Transactions retrieved successfully",
	})
}

// Phone-based convenience endpoints

// TransferByPhone transfers money between wallets using phone numbers
func (wc *WalletController) TransferByPhone(c *gin.Context) {
	var req PhoneTransferRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	transfer, err := wc.container.WalletService.TransferByPhone(req.FromPhoneNumber, req.ToPhoneNumber, req.Amount, req.Description)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "TRANSFER_FAILED",
			Message: err.Error(),
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, models.DataResponse{
		Success: true,
		Message: "Transfer completed successfully",
		Data:    transfer,
	})
}

// TopupWalletByPhone tops up wallet using phone number
func (wc *WalletController) TopupWalletByPhone(c *gin.Context) {
	phoneNumber := c.Param("phone")

	var req TopupRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	transaction, err := wc.container.WalletService.TopupWalletByPhone(phoneNumber, req.Amount, req.PaymentMethod, req.Reference)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "TOPUP_FAILED",
			Message: err.Error(),
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, models.DataResponse{
		Success: true,
		Message: "Topup completed successfully",
		Data:    transaction,
	})
}

// GetBalanceByPhone gets wallet balance using phone number
func (wc *WalletController) GetBalanceByPhone(c *gin.Context) {
	phoneNumber := c.Param("phone")

	balance, err := wc.container.WalletService.GetBalanceByPhone(phoneNumber)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "BALANCE_FETCH_FAILED",
			Message: err.Error(),
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, models.DataResponse{
		Success: true,
		Message: "Balance retrieved successfully",
		Data: map[string]interface{}{
			"phone_number": phoneNumber,
			"balance":      balance,
		},
	})
}

// GetTransactionHistoryByPhone gets wallet transaction history using phone number
func (wc *WalletController) GetTransactionHistoryByPhone(c *gin.Context) {
	phoneNumber := c.Param("phone")

	// Parse query parameters for filters
	filters := make(map[string]interface{})
	if startDate := c.Query("start_date"); startDate != "" {
		filters["start_date"] = startDate
	}
	if endDate := c.Query("end_date"); endDate != "" {
		filters["end_date"] = endDate
	}
	if txType := c.Query("type"); txType != "" {
		filters["type"] = txType
	}
	if category := c.Query("category"); category != "" {
		filters["category"] = category
	}

	transactions, err := wc.container.WalletService.GetTransactionHistoryByPhone(phoneNumber, filters)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "TRANSACTIONS_FETCH_FAILED",
			Message: err.Error(),
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, models.DataResponse{
		Success: true,
		Message: "Transactions retrieved successfully",
		Data: map[string]interface{}{
			"phone_number": phoneNumber,
			"transactions": transactions,
		},
	})
}

// Request/Response types
type CreateWalletRequest struct {
	PhoneNumber string `json:"phone_number" binding:"required"`
	PIN         string `json:"pin" binding:"omitempty,len=4"` // Optional PIN
}

type TransferRequest struct {
	FromWalletID uint    `json:"from_wallet_id" binding:"required"`
	ToWalletID   uint    `json:"to_wallet_id" binding:"required"`
	Amount       float64 `json:"amount" binding:"required,gt=0"`
	Description  string  `json:"description"`
}

// Phone-based transfer request
type PhoneTransferRequest struct {
	FromPhoneNumber string  `json:"from_phone_number" binding:"required"`
	ToPhoneNumber   string  `json:"to_phone_number" binding:"required"`
	Amount          float64 `json:"amount" binding:"required,gt=0"`
	Description     string  `json:"description"`
}

// Topup request
type TopupRequest struct {
	Amount        float64 `json:"amount" binding:"required,gt=0"`
	PaymentMethod string  `json:"payment_method" binding:"required"`
	Reference     string  `json:"reference" binding:"required"`
}

type DepositRequest struct {
	Amount      float64 `json:"amount" binding:"required,gt=0"`
	TopupPhone  string  `json:"topup_phone" binding:"required"`
	Source      string  `json:"source" binding:"required"`
	Reference   string  `json:"reference"`
	Description string  `json:"description"`
}
