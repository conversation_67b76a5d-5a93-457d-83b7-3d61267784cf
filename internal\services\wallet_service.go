package services

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"os"
	"regexp"
	"strings"
	"time"

	"wallet-platform/internal/config"
	"wallet-platform/internal/models"
	"wallet-platform/pkg/logger"
	"wallet-platform/pkg/redis"

	"github.com/google/uuid"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// WalletService implements the WalletServiceInterface
type WalletService struct {
	db     *gorm.DB
	redis  *redis.Client
	logger *logger.Logger
	config *config.Config
}

// Platform wallet constants
const (
	PlatformWalletPhone = "+268PLATFORM"
	PlatformWalletType  = "platform"
)

// NewWalletService creates a new wallet service
func NewWalletService(db *gorm.DB, redisClient *redis.Client, log *logger.Logger, cfg *config.Config) *WalletService {
	return &WalletService{
		db:     db,
		redis:  redisClient,
		logger: log,
		config: cfg,
	}
}

// CreateWallet creates a new wallet
func (s *WalletService) CreateWallet(phoneNumber, walletType string) (*models.WalletResponse, error) {
	// Enhanced debugging with detailed logging
	fmt.Printf("SERVICE DEBUG: CreateWallet called with phone=%s, type=%s\n", phoneNumber, walletType)

	// Defensive checks for nil pointers
	fmt.Printf("SERVICE DEBUG: Step 1 - Checking service initialization\n")
	if s == nil {
		fmt.Printf("SERVICE ERROR: WalletService is nil\n")
		return nil, fmt.Errorf("wallet service is nil")
	}
	fmt.Printf("SERVICE DEBUG: WalletService is not nil\n")

	fmt.Printf("SERVICE DEBUG: Step 2 - Checking database connection\n")
	if s.db == nil {
		fmt.Printf("SERVICE ERROR: Database connection is nil\n")
		return nil, fmt.Errorf("database connection is nil")
	}
	fmt.Printf("SERVICE DEBUG: Database connection is not nil\n")

	fmt.Printf("SERVICE DEBUG: Step 3 - Checking logger\n")
	if s.logger == nil {
		fmt.Printf("SERVICE WARNING: Logger is nil\n")
		// Don't fail on nil logger, just continue
	} else {
		fmt.Printf("SERVICE DEBUG: Logger is available\n")
	}

	fmt.Printf("SERVICE DEBUG: Step 4 - Checking config\n")
	if s.config == nil {
		fmt.Printf("SERVICE WARNING: Config is nil\n")
	} else {
		fmt.Printf("SERVICE DEBUG: Config is available\n")
	}

	// Check if wallet already exists
	fmt.Printf("SERVICE DEBUG: Step 5 - Checking for existing wallet\n")
	var existingWallet models.Wallet
	if err := s.db.Where("phone_number = ?", phoneNumber).First(&existingWallet).Error; err == nil {
		fmt.Printf("SERVICE ERROR: Wallet already exists for phone number: %s\n", phoneNumber)
		return nil, fmt.Errorf("wallet already exists for phone number: %s", phoneNumber)
	}
	fmt.Printf("SERVICE DEBUG: No existing wallet found, proceeding with creation\n")

	// Generate account number
	fmt.Printf("SERVICE DEBUG: Step 6 - Generating account number\n")
	accountNumber, err := s.generateAccountNumber(walletType)
	if err != nil {
		fmt.Printf("SERVICE ERROR: Account number generation failed: %v\n", err)
		return nil, fmt.Errorf("failed to generate account number: %w", err)
	}
	fmt.Printf("SERVICE DEBUG: Account number generated: %s\n", accountNumber)

	// Validate phone number
	fmt.Printf("SERVICE DEBUG: Step 7 - Validating phone number: %s\n", phoneNumber)
	if !isValidPhoneNumber(phoneNumber) {
		fmt.Printf("SERVICE ERROR: Invalid phone number format: %s\n", phoneNumber)
		return nil, fmt.Errorf("invalid phone number format")
	}
	fmt.Printf("SERVICE DEBUG: Phone number validation passed\n")

	// Validate wallet type
	fmt.Printf("SERVICE DEBUG: Step 8 - Validating wallet type: %s\n", walletType)
	if !isValidWalletType(walletType) {
		fmt.Printf("SERVICE ERROR: Invalid wallet type: %s\n", walletType)
		return nil, fmt.Errorf("invalid wallet type")
	}
	fmt.Printf("SERVICE DEBUG: Wallet type validation passed\n")

	// Create wallet
	fmt.Printf("SERVICE DEBUG: Step 9 - Creating wallet struct\n")
	wallet := models.Wallet{
		PhoneNumber:   phoneNumber,
		AccountNumber: accountNumber,
		WalletType:    walletType,
		Balance:       0.0,
		Currency:      "SZL", // Default currency
		Status:        "active",
		IsVerified:    true, // Assuming verification is handled externally
		KYCLevel:      "tier1",
		Metadata:      datatypes.JSON("{}"),
	}
	fmt.Printf("SERVICE DEBUG: Wallet struct created successfully\n")

	fmt.Printf("SERVICE DEBUG: Step 6 - Creating wallet in database\n")
	if err := s.db.Create(&wallet).Error; err != nil {
		fmt.Printf("SERVICE ERROR: Database create failed: %v\n", err)
		if s.logger != nil {
			s.logger.LogError(err, map[string]interface{}{
				"action":       "create_wallet",
				"phone_number": phoneNumber,
			})
		}
		return nil, fmt.Errorf("failed to create wallet: %w", err)
	}
	fmt.Printf("SERVICE DEBUG: Wallet created successfully in database with ID: %d\n", wallet.ID)

	if s.logger != nil {
		s.logger.LogTransaction("", fmt.Sprintf("%d", wallet.ID), "wallet_created", 0, "success")
	}

	fmt.Printf("SERVICE DEBUG: Step 10 - Converting wallet to response\n")
	response := s.walletToResponse(&wallet)
	if response == nil {
		fmt.Printf("SERVICE ERROR: walletToResponse returned nil\n")
		return nil, fmt.Errorf("failed to convert wallet to response")
	}
	fmt.Printf("SERVICE DEBUG: Wallet creation completed successfully\n")
	return response, nil
}

// FundOrUnfundWallet funds or unfunds a wallet by phone number and amount.
// direction: "debit" to add funds, "credit" to remove funds.
func (s *WalletService) FundOrUnfundWallet(phone string, amount float64, direction string) error {
	// Find wallet by phone
	walletResp, err := s.GetWalletByPhone(phone)
	if err != nil {
		return fmt.Errorf("wallet not found: %w", err)
	}

	// Get the actual wallet model (assuming GetWalletByPhone returns WalletResponse)
	var wallet models.Wallet
	if err := s.db.First(&wallet, walletResp.ID).Error; err != nil {
		return fmt.Errorf("wallet not found in db: %w", err)
	}

	// Start DB transaction
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Check wallet status
	if err := s.checkWalletStatus(&wallet); err != nil {
		tx.Rollback()
		return fmt.Errorf("wallet status error: %w", err)
	}

	// Business logic: debit = add funds, credit = remove funds
	if direction == "debit" {
		wallet.Balance += amount
	} else if direction == "credit" {
		if wallet.Balance < amount {
			tx.Rollback()
			return fmt.Errorf("insufficient funds")
		}
		wallet.Balance -= amount
	} else {
		tx.Rollback()
		return fmt.Errorf("invalid direction: must be 'debit' or 'credit'")
	}

	// Save wallet
	if err := tx.Save(&wallet).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to update wallet: %w", err)
	}

	// Commit transaction
	return tx.Commit().Error
}

// Helper functions for validation
func isValidPhoneNumber(phoneNumber string) bool {
	// Basic phone number validation - adjust regex as needed
	phoneRegex := regexp.MustCompile(`^\+?[1-9]\d{1,14}$`)
	return phoneRegex.MatchString(phoneNumber)
}

func isValidWalletType(walletType string) bool {
	validTypes := []string{"individual", "business", "master"}
	for _, validType := range validTypes {
		if strings.ToLower(walletType) == validType {
			return true
		}
	}
	return false
}

// GetWallet retrieves a wallet by ID
func (s *WalletService) GetWallet(id uint) (*models.WalletResponse, error) {
	var wallet models.Wallet
	if err := s.db.First(&wallet, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("wallet not found")
		}
		return nil, fmt.Errorf("failed to get wallet: %w", err)
	}

	return s.walletToResponse(&wallet), nil
}

// GetWalletByPhone retrieves a wallet by phone number
func (s *WalletService) GetWalletByPhone(phoneNumber string) (*models.WalletResponse, error) {
	var wallet models.Wallet
	if err := s.db.Where("phone_number = ?", phoneNumber).First(&wallet).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("wallet not found for phone number: %s", phoneNumber)
		}
		return nil, fmt.Errorf("failed to get wallet: %w", err)
	}

	return s.walletToResponse(&wallet), nil
}

// UpdateWallet updates wallet information
func (s *WalletService) UpdateWallet(id uint, updates map[string]interface{}) (*models.WalletResponse, error) {
	var wallet models.Wallet
	if err := s.db.First(&wallet, id).Error; err != nil {
		return nil, fmt.Errorf("wallet not found")
	}

	if err := s.db.Model(&wallet).Updates(updates).Error; err != nil {
		s.logger.LogError(err, map[string]interface{}{
			"action":    "update_wallet",
			"wallet_id": id,
			"updates":   updates,
		})
		return nil, fmt.Errorf("failed to update wallet: %w", err)
	}

	return s.walletToResponse(&wallet), nil
}

// GetBalance retrieves wallet balance
func (s *WalletService) GetBalance(walletID uint) (float64, error) {
	var wallet models.Wallet
	if err := s.db.Select("balance").First(&wallet, walletID).Error; err != nil {
		return 0, fmt.Errorf("wallet not found")
	}

	return wallet.Balance, nil
}

// TransferByPhone performs a wallet-to-wallet transfer using phone numbers
func (s *WalletService) TransferByPhone(fromPhoneNumber, toPhoneNumber string, amount float64, description string) (*models.TransferResponse, error) {
	// Get wallet IDs from phone numbers
	fromWallet, err := s.GetWalletByPhoneRaw(fromPhoneNumber)
	if err != nil {
		return nil, fmt.Errorf("sender wallet not found: %w", err)
	}

	toWallet, err := s.GetWalletByPhoneRaw(toPhoneNumber)
	if err != nil {
		return nil, fmt.Errorf("recipient wallet not found: %w", err)
	}

	// Prevent self-transfer
	if fromWallet.ID == toWallet.ID {
		return nil, fmt.Errorf("cannot transfer to the same wallet")
	}

	// Use existing transfer method with wallet IDs
	return s.Transfer(fromWallet.ID, toWallet.ID, amount, description)
}

// Transfer performs a wallet-to-wallet transfer
func (s *WalletService) Transfer(fromWalletID, toWalletID uint, amount float64, description string) (*models.TransferResponse, error) {
	// Start transaction
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Get source wallet
	var fromWallet models.Wallet
	if err := tx.First(&fromWallet, fromWalletID).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("source wallet not found")
	}

	// Get destination wallet
	var toWallet models.Wallet
	if err := tx.First(&toWallet, toWalletID).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("destination wallet not found")
	}

	// Enhanced security checks
	if err := s.checkWalletStatus(&fromWallet); err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("from wallet error: %w", err)
	}

	if err := s.checkWalletStatus(&toWallet); err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("to wallet error: %w", err)
	}

	// Calculate transfer fee
	feePercentage := 0.01 // Default 1% fee
	if s.config != nil {
		feePercentage = s.config.Fees.TransactionPercentage / 100.0
	}
	fee := amount * feePercentage
	totalAmount := amount + fee

	// Check transfer limits
	if err := s.validateTransferLimits(&fromWallet, totalAmount); err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("transfer limit exceeded: %w", err)
	}

	// Check balance
	if fromWallet.Balance < totalAmount {
		tx.Rollback()
		return nil, fmt.Errorf("insufficient balance including fees")
	}

	// Update balances
	fromWallet.Balance -= totalAmount
	toWallet.Balance += amount

	if err := tx.Save(&fromWallet).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("failed to update source wallet: %w", err)
	}

	if err := tx.Save(&toWallet).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("failed to update destination wallet: %w", err)
	}

	// Create transfer record
	reference := s.generateTransferReference()
	transfer := models.WalletTransfer{
		FromWalletID: fromWalletID,
		ToWalletID:   toWalletID,
		Amount:       amount,
		Fee:          fee,
		TotalAmount:  totalAmount,
		Reference:    reference,
		Description:  description,
		Status:       "completed",
	}

	if err := tx.Create(&transfer).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("failed to create transfer record: %w", err)
	}

	// Create transaction records
	debitTx := models.WalletTransaction{
		WalletID:     fromWalletID,
		Type:         "debit",
		Amount:       totalAmount,
		BalanceAfter: fromWallet.Balance - totalAmount,
		Reference:    reference,
		Description:  fmt.Sprintf("Transfer to %s", toWallet.PhoneNumber),
		Category:     "transfer",
	}

	creditTx := models.WalletTransaction{
		WalletID:     toWalletID,
		Type:         "credit",
		Amount:       amount,
		BalanceAfter: toWallet.Balance + amount,
		Reference:    reference,
		Description:  fmt.Sprintf("Transfer from %s", fromWallet.PhoneNumber),
		Category:     "transfer",
	}

	if err := tx.Create(&debitTx).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("failed to create debit transaction: %w", err)
	}

	if err := tx.Create(&creditTx).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("failed to create credit transaction: %w", err)
	}

	// Update spending limits for the sender
	if err := s.updateSpendingLimits(fromWalletID, totalAmount); err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("failed to update spending limits: %w", err)
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("failed to commit transfer: %w", err)
	}

	// Create audit logs
	s.createAuditLog(fromWalletID, "transfer_sent", map[string]interface{}{
		"to_wallet_id": toWalletID,
		"amount":       amount,
		"fee":          fee,
		"reference":    reference,
	})

	s.createAuditLog(toWalletID, "transfer_received", map[string]interface{}{
		"from_wallet_id": fromWalletID,
		"amount":         amount,
		"reference":      reference,
	})

	s.logger.LogTransaction(reference, fmt.Sprintf("%d", fromWalletID), "transfer", totalAmount, "completed")

	return &models.TransferResponse{
		ID:           transfer.ID,
		Reference:    reference,
		FromWalletID: fromWalletID,
		ToWalletID:   toWalletID,
		Amount:       amount,
		Fee:          fee,
		TotalAmount:  totalAmount,
		Status:       "completed",
		Description:  description,
		TransferType: "wallet_transfer",
		ProcessedAt:  &transfer.CreatedAt,
		CreatedAt:    transfer.CreatedAt,
	}, nil
}

// GetTransactionHistory retrieves transaction history for a wallet
func (s *WalletService) GetTransactionHistory(walletID uint, filters map[string]interface{}) ([]models.TransactionResponse, error) {
	var transactions []models.WalletTransaction
	query := s.db.Where("wallet_id = ?", walletID)

	// Apply filters
	if startDate, ok := filters["start_date"]; ok {
		query = query.Where("created_at >= ?", startDate)
	}
	if endDate, ok := filters["end_date"]; ok {
		query = query.Where("created_at <= ?", endDate)
	}
	if txType, ok := filters["type"]; ok {
		query = query.Where("type = ?", txType)
	}
	if category, ok := filters["category"]; ok {
		query = query.Where("category = ?", category)
	}

	if err := query.Order("created_at DESC").Find(&transactions).Error; err != nil {
		return nil, fmt.Errorf("failed to get transaction history: %w", err)
	}

	var response []models.TransactionResponse
	for _, tx := range transactions {
		response = append(response, models.TransactionResponse{
			ID:                    tx.ID,
			Type:                  tx.Type,
			Amount:                tx.Amount,
			BalanceAfter:          tx.BalanceAfter,
			Reference:             tx.Reference,
			Description:           tx.Description,
			Category:              tx.Category,
			ExternalTransactionID: tx.ExternalTransactionID,
			PaymentMethod:         tx.PaymentMethod,
			ProviderCode:          tx.ProviderCode,
			Fee:                   tx.Fee,
			CreatedAt:             tx.CreatedAt,
		})
	}

	return response, nil
}

// TopupWalletByPhone adds funds to a wallet using phone number
func (s *WalletService) TopupWalletByPhone(phoneNumber string, amount float64, paymentMethod, reference string) (*models.TransactionResponse, error) {
	// Get wallet by phone number
	wallet, err := s.GetWalletByPhoneRaw(phoneNumber)
	if err != nil {
		return nil, fmt.Errorf("wallet not found: %w", err)
	}

	// Use existing topup method with wallet ID
	return s.TopupWallet(wallet.ID, amount, phoneNumber, paymentMethod, reference)
}

// GetBalanceByPhone retrieves wallet balance using phone number
func (s *WalletService) GetBalanceByPhone(phoneNumber string) (float64, error) {
	wallet, err := s.GetWalletByPhoneRaw(phoneNumber)
	if err != nil {
		return 0, fmt.Errorf("wallet not found: %w", err)
	}

	return wallet.Balance, nil
}

// GetTransactionHistoryByPhone retrieves transaction history using phone number
func (s *WalletService) GetTransactionHistoryByPhone(phoneNumber string, filters map[string]interface{}) ([]models.TransactionResponse, error) {
	wallet, err := s.GetWalletByPhoneRaw(phoneNumber)
	if err != nil {
		return nil, fmt.Errorf("wallet not found: %w", err)
	}

	return s.GetTransactionHistory(wallet.ID, filters)
}

// FreezeWalletByPhone freezes a wallet using phone number (admin only)
func (s *WalletService) FreezeWalletByPhone(phoneNumber string, reason string) error {
	wallet, err := s.GetWalletByPhoneRaw(phoneNumber)
	if err != nil {
		return fmt.Errorf("wallet not found: %w", err)
	}

	return s.FreezeWallet(wallet.ID, reason)
}

// UnfreezeWalletByPhone unfreezes a wallet using phone number (admin only)
func (s *WalletService) UnfreezeWalletByPhone(phoneNumber string) error {
	wallet, err := s.GetWalletByPhoneRaw(phoneNumber)
	if err != nil {
		return fmt.Errorf("wallet not found: %w", err)
	}

	return s.UnfreezeWallet(wallet.ID)
}

// TopupWallet adds funds to a wallet via external payment engine
func (s *WalletService) TopupWallet(walletID uint, amount float64, topupPhone, paymentMethod, reference string) (*models.TransactionResponse, error) {
	// Start database transaction
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Get wallet
	var wallet models.Wallet
	if err := tx.First(&wallet, walletID).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("wallet not found")
	}

	// Check wallet status
	if err := s.checkWalletStatus(&wallet); err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("wallet status error: %w", err)
	}

	// Extract phone number for external payment (remove country code if present)
	phone := topupPhone
	phone = strings.TrimPrefix(phone, "268")  // Remove country code if present
	phone = strings.TrimPrefix(phone, "+268") // Remove +268 country code if present

	// Process external payment first (debit = collecting funds to our platform)
	if err := s.makePayment(paymentMethod, "debit", phone, amount); err != nil {
		tx.Rollback()
		s.logger.LogError(err, map[string]interface{}{
			"action":         "topup_external_payment_failed",
			"wallet_id":      walletID,
			"phone_number":   wallet.PhoneNumber,
			"amount":         amount,
			"payment_method": paymentMethod,
		})
		return nil, fmt.Errorf("external payment failed: %w", err)
	}

	// External payment successful, now update wallet balance
	wallet.Balance += amount
	if err := tx.Save(&wallet).Error; err != nil {
		tx.Rollback()
		// Note: External payment was successful but wallet update failed
		// This should trigger a reconciliation process
		s.logger.LogError(err, map[string]interface{}{
			"action":         "topup_wallet_update_failed",
			"wallet_id":      walletID,
			"amount":         amount,
			"payment_method": paymentMethod,
			"critical":       true, // Requires manual reconciliation
		})
		return nil, fmt.Errorf("failed to update wallet balance after successful external payment: %w", err)
	}

	// Create transaction record
	transaction := models.WalletTransaction{
		WalletID:      walletID,
		Type:          "credit",
		Amount:        amount,
		BalanceAfter:  wallet.Balance,
		Reference:     reference,
		Description:   fmt.Sprintf("Wallet topup via %s (external payment)", paymentMethod),
		Category:      "topup",
		PaymentMethod: paymentMethod,
	}

	if err := tx.Create(&transaction).Error; err != nil {
		tx.Rollback()
		// Note: External payment was successful but transaction record failed
		s.logger.LogError(err, map[string]interface{}{
			"action":         "topup_transaction_record_failed",
			"wallet_id":      walletID,
			"amount":         amount,
			"payment_method": paymentMethod,
			"critical":       true, // Requires manual reconciliation
		})
		return nil, fmt.Errorf("failed to create transaction record after successful external payment: %w", err)
	}

	// Commit database transaction
	if err := tx.Commit().Error; err != nil {
		// Note: External payment was successful but database commit failed
		s.logger.LogError(err, map[string]interface{}{
			"action":         "topup_commit_failed",
			"wallet_id":      walletID,
			"amount":         amount,
			"payment_method": paymentMethod,
			"critical":       true, // Requires manual reconciliation
		})
		return nil, fmt.Errorf("failed to commit topup after successful external payment: %w", err)
	}

	// Create audit log
	s.createAuditLog(walletID, "wallet_topup", map[string]interface{}{
		"amount":         amount,
		"payment_method": paymentMethod,
		"reference":      reference,
	})

	s.logger.LogTransaction(reference, fmt.Sprintf("%d", walletID), "topup", amount, "completed")

	return &models.TransactionResponse{
		ID:                    transaction.ID,
		Type:                  transaction.Type,
		Amount:                transaction.Amount,
		BalanceAfter:          transaction.BalanceAfter,
		Reference:             transaction.Reference,
		Description:           transaction.Description,
		Category:              transaction.Category,
		ExternalTransactionID: transaction.ExternalTransactionID,
		PaymentMethod:         transaction.PaymentMethod,
		ProviderCode:          transaction.ProviderCode,
		Fee:                   transaction.Fee,
		CreatedAt:             transaction.CreatedAt,
	}, nil
}

// GetAllWallets retrieves all wallets with pagination and filtering (admin only)
func (s *WalletService) GetAllWallets(page, limit int, status, walletType string) ([]models.WalletResponse, int64, error) {
	var wallets []models.Wallet
	var total int64

	// Build query
	query := s.db.Model(&models.Wallet{})

	// Apply filters
	if status != "" {
		query = query.Where("status = ?", status)
	}
	if walletType != "" {
		query = query.Where("wallet_type = ?", walletType)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		s.logger.LogError(err, map[string]interface{}{
			"action":      "get_all_wallets_count",
			"status":      status,
			"wallet_type": walletType,
		})
		return nil, 0, fmt.Errorf("failed to count wallets: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Offset(offset).Limit(limit).Order("created_at DESC").Find(&wallets).Error; err != nil {
		s.logger.LogError(err, map[string]interface{}{
			"action":      "get_all_wallets",
			"page":        page,
			"limit":       limit,
			"status":      status,
			"wallet_type": walletType,
		})
		return nil, 0, fmt.Errorf("failed to fetch wallets: %w", err)
	}

	// Convert to response format
	var responses []models.WalletResponse
	for _, wallet := range wallets {
		responses = append(responses, *s.walletToResponse(&wallet))
	}

	return responses, total, nil
}

// FreezeWallet freezes a wallet (admin only)
func (s *WalletService) FreezeWallet(walletID uint, reason string) error {
	var wallet models.Wallet
	if err := s.db.First(&wallet, walletID).Error; err != nil {
		return fmt.Errorf("wallet not found")
	}

	if wallet.Status == "frozen" {
		return fmt.Errorf("wallet is already frozen")
	}

	// Update wallet status
	updates := map[string]interface{}{
		"status": "frozen",
	}

	if err := s.db.Model(&wallet).Updates(updates).Error; err != nil {
		s.logger.LogError(err, map[string]interface{}{
			"action":    "freeze_wallet",
			"wallet_id": walletID,
			"reason":    reason,
		})
		return fmt.Errorf("failed to freeze wallet: %w", err)
	}

	// Create audit log
	s.createAuditLog(walletID, "wallet_frozen", map[string]interface{}{
		"reason": reason,
	})

	return nil
}

// UnfreezeWallet unfreezes a wallet (admin only)
func (s *WalletService) UnfreezeWallet(walletID uint) error {
	var wallet models.Wallet
	if err := s.db.First(&wallet, walletID).Error; err != nil {
		return fmt.Errorf("wallet not found")
	}

	if wallet.Status != "frozen" {
		return fmt.Errorf("wallet is not frozen")
	}

	// Update wallet status
	updates := map[string]interface{}{
		"status": "active",
	}

	if err := s.db.Model(&wallet).Updates(updates).Error; err != nil {
		s.logger.LogError(err, map[string]interface{}{
			"action":    "unfreeze_wallet",
			"wallet_id": walletID,
		})
		return fmt.Errorf("failed to unfreeze wallet: %w", err)
	}

	// Create audit log
	s.createAuditLog(walletID, "wallet_unfrozen", map[string]interface{}{})

	return nil
}

// Helper methods
func (s *WalletService) walletToResponse(wallet *models.Wallet) *models.WalletResponse {
	if wallet == nil {
		fmt.Printf("SERVICE ERROR: walletToResponse called with nil wallet\n")
		return nil
	}

	fmt.Printf("SERVICE DEBUG: Converting wallet ID %d to response\n", wallet.ID)
	return &models.WalletResponse{
		ID:            wallet.ID,
		PhoneNumber:   wallet.PhoneNumber,
		AccountNumber: wallet.AccountNumber,
		WalletType:    wallet.WalletType,
		Balance:       wallet.Balance,
		Currency:      wallet.Currency,
		Status:        wallet.Status,
		IsVerified:    wallet.IsVerified,
		KYCLevel:      wallet.KYCLevel,
		DailyLimit:    wallet.DailyLimit,
		MonthlyLimit:  wallet.MonthlyLimit,
		DailySpent:    wallet.DailySpent,
		MonthlySpent:  wallet.MonthlySpent,
		CreatedAt:     wallet.CreatedAt,
		UpdatedAt:     wallet.UpdatedAt,
	}
}

// generateAccountNumber generates a unique account number with wallet type prefix
func (s *WalletService) generateAccountNumber(walletType string) (string, error) {
	fmt.Printf("SERVICE DEBUG: generateAccountNumber called with type: %s\n", walletType)

	if s.db == nil {
		fmt.Printf("SERVICE ERROR: Database is nil in generateAccountNumber\n")
		return "", fmt.Errorf("database connection is nil")
	}

	prefix, err := s.getWalletTypePrefix(walletType)
	if err != nil {
		fmt.Printf("SERVICE ERROR: Failed to get wallet type prefix: %v\n", err)
		return "", err
	}
	fmt.Printf("SERVICE DEBUG: Wallet type prefix: %s\n", prefix)

	maxAttempts := 100
	for i := 0; i < maxAttempts; i++ {
		fmt.Printf("SERVICE DEBUG: Account number generation attempt %d/%d\n", i+1, maxAttempts)

		// Generate 6-character alphanumeric suffix
		suffix := s.generateAlphanumericString(6)
		fmt.Printf("SERVICE DEBUG: Generated suffix: %s\n", suffix)

		// Combine prefix and suffix to create 8-character account number
		accountNumber := prefix + suffix
		fmt.Printf("SERVICE DEBUG: Generated account number: %s\n", accountNumber)

		// Check if account number already exists
		var count int64
		if err := s.db.Model(&models.Wallet{}).Where("account_number = ?", accountNumber).Count(&count).Error; err != nil {
			fmt.Printf("SERVICE ERROR: Database query failed in generateAccountNumber: %v\n", err)
			return "", err
		}
		fmt.Printf("SERVICE DEBUG: Account number check - count: %d\n", count)

		if count == 0 {
			fmt.Printf("SERVICE DEBUG: Unique account number generated: %s\n", accountNumber)
			return accountNumber, nil
		}
		fmt.Printf("SERVICE DEBUG: Account number %s already exists, trying again\n", accountNumber)
	}

	fmt.Printf("SERVICE ERROR: Failed to generate unique account number after %d attempts\n", maxAttempts)
	return "", fmt.Errorf("failed to generate unique account number")
}

// getWalletTypePrefix returns the 2-character prefix for the wallet type
func (s *WalletService) getWalletTypePrefix(walletType string) (string, error) {
	switch strings.ToLower(walletType) {
	case "individual":
		return "IN", nil
	case "business":
		return "BU", nil
	case "master":
		return "MA", nil
	default:
		return "", fmt.Errorf("invalid wallet type: %s", walletType)
	}
}

// generateAlphanumericString generates a random alphanumeric string of specified length
func (s *WalletService) generateAlphanumericString(length int) string {
	const charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	result := make([]byte, length)

	for i := range result {
		result[i] = charset[rand.Intn(len(charset))]
	}

	return string(result)
}

func (s *WalletService) generateTransferReference() string {
	return fmt.Sprintf("TXN_%s", uuid.New().String()[:8])
}

// Enhanced security and validation methods

// validateTransferLimits checks if the transfer amount is within allowed limits
func (s *WalletService) validateTransferLimits(wallet *models.Wallet, amount float64) error {
	// Check daily limit
	if wallet.DailySpent+amount > wallet.DailyLimit {
		return fmt.Errorf("transfer amount exceeds daily limit")
	}

	// Check monthly limit
	if wallet.MonthlySpent+amount > wallet.MonthlyLimit {
		return fmt.Errorf("transfer amount exceeds monthly limit")
	}

	return nil
}

// updateSpendingLimits updates the wallet's spending limits after a transaction
func (s *WalletService) updateSpendingLimits(walletID uint, amount float64) error {
	now := time.Now()

	// Get wallet with current spending
	var wallet models.Wallet
	if err := s.db.First(&wallet, walletID).Error; err != nil {
		return err
	}

	// Check if we need to reset daily limits (new day)
	if wallet.LastLimitReset == nil || wallet.LastLimitReset.Day() != now.Day() {
		wallet.DailySpent = 0
		wallet.LastLimitReset = &now
	}

	// Check if we need to reset monthly limits (new month)
	if wallet.LastLimitReset == nil || wallet.LastLimitReset.Month() != now.Month() {
		wallet.MonthlySpent = 0
		wallet.LastLimitReset = &now
	}

	// Update spending amounts
	wallet.DailySpent += amount
	wallet.MonthlySpent += amount

	return s.db.Save(&wallet).Error
}

// checkWalletStatus validates that the wallet is active and can perform transactions
func (s *WalletService) checkWalletStatus(wallet *models.Wallet) error {
	switch wallet.Status {
	case "suspended":
		return fmt.Errorf("wallet is suspended")
	case "closed":
		return fmt.Errorf("wallet is closed")
	case "active":
		return nil
	default:
		return fmt.Errorf("invalid wallet status: %s", wallet.Status)
	}
}

// Enhanced transaction processing with audit trail
func (s *WalletService) createAuditLog(walletID uint, action string, details map[string]interface{}) {
	s.logger.LogTransaction(
		fmt.Sprintf("%d", walletID),
		"",
		action,
		0,
		"audit",
	)
}

// GetWalletByPhoneRaw retrieves a wallet model by phone number (for auth)
func (s *WalletService) GetWalletByPhoneRaw(phoneNumber string) (*models.Wallet, error) {
	var wallet models.Wallet
	if err := s.db.Where("phone_number = ?", phoneNumber).First(&wallet).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("wallet not found for phone number: %s", phoneNumber)
		}
		s.logger.LogError(err, map[string]interface{}{
			"action":       "get_wallet_by_phone_raw",
			"phone_number": phoneNumber,
		})
		return nil, fmt.Errorf("failed to retrieve wallet: %w", err)
	}

	return &wallet, nil
}

// makePayment processes payment through external payment engine
// direction: "debit" for collecting funds to our platform (topups), "credit" for sending funds out (withdrawals)
func (s *WalletService) makePayment(provider, direction, phone string, amount float64) error {
	// Prepare payment request
	payReq := map[string]interface{}{
		"provider_code": provider,
		"reference":     uuid.New().String(),
		"amount":        amount,
		"direction":     direction,
		"meta": map[string]interface{}{
			"msisdn": "268" + phone,
		},
	}

	paymentEngineURL := os.Getenv("EXTERNAL_PAYMENT_ENGINE_BASE_URL")
	apiKey := os.Getenv("EXTERNAL_PAYMENT_ENGINE_API_KEY")

	if paymentEngineURL == "" || apiKey == "" {
		return fmt.Errorf("payment engine configuration missing")
	}

	client := &http.Client{Timeout: 30 * time.Second}
	bodyBytes, err := json.Marshal(payReq)
	if err != nil {
		return fmt.Errorf("failed to marshal payment request: %w", err)
	}

	httpReq, err := http.NewRequest("POST", paymentEngineURL, bytes.NewBuffer(bodyBytes))
	if err != nil {
		return fmt.Errorf("failed to create HTTP request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("X-API-Key", apiKey)

	resp, err := client.Do(httpReq)
	if err != nil {
		s.logger.LogError(err, map[string]interface{}{
			"action":    "external_payment",
			"provider":  provider,
			"direction": direction,
			"phone":     phone,
			"amount":    amount,
		})
		return fmt.Errorf("failed to make payment request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		respBody, _ := io.ReadAll(resp.Body)
		s.logger.LogError(fmt.Errorf("payment request failed"), map[string]interface{}{
			"status_code": resp.StatusCode,
			"response":    string(respBody),
			"provider":    provider,
			"direction":   direction,
			"phone":       phone,
			"amount":      amount,
		})
		return fmt.Errorf("payment request failed with status %d: %s", resp.StatusCode, string(respBody))
	}

	var payResp struct {
		Message     string                 `json:"message"`
		Transaction map[string]interface{} `json:"transaction"`
	}
	if err := json.NewDecoder(resp.Body).Decode(&payResp); err != nil {
		s.logger.LogError(err, map[string]interface{}{
			"action": "parse_payment_response",
		})
		return fmt.Errorf("failed to parse payment response: %w", err)
	}

	// Check transaction status
	status, ok := payResp.Transaction["Status"].(string)
	if !ok || status != "success" {
		s.logger.LogError(fmt.Errorf("payment not successful"), map[string]interface{}{
			"status":      status,
			"transaction": payResp.Transaction,
		})
		return fmt.Errorf("payment not successful, status: %s", status)
	}

	// Log successful payment
	s.logger.LogTransaction(
		fmt.Sprintf("external_payment_%s", direction),
		payReq["reference"].(string),
		"external_payment_success",
		amount,
		fmt.Sprintf("External payment successful via %s", provider),
	)

	return nil
}

// WithdrawFunds withdraws funds from a wallet via external payment engine
// WithdrawFunds withdraws funds from a wallet via external payment engine
func (s *WalletService) WithdrawFunds(walletID uint, amount float64, destination, reference string) (*models.TransactionResponse, error) {
	fmt.Printf("SERVICE DEBUG: WithdrawFunds called with walletID=%d, amount=%.2f, destination=%s, reference=%s\n", walletID, amount, destination, reference)
	s.logger.LogTransaction(
		fmt.Sprintf("%d", walletID),
		reference,
		"withdrawal_initiated",
		amount,
		fmt.Sprintf("Withdrawal initiated to %s", destination),
	)

	// Start database transaction
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			fmt.Printf("SERVICE ERROR: Panic recovered in WithdrawFunds: %v\n", r)
			if s.logger != nil {
				s.logger.LogError(fmt.Errorf("panic: %v", r), map[string]interface{}{
					"action":    "withdraw_panic",
					"wallet_id": walletID,
				})
			}
		}
	}()

	// Get wallet
	var wallet models.Wallet
	if err := tx.First(&wallet, walletID).Error; err != nil {
		tx.Rollback()
		fmt.Printf("SERVICE ERROR: Wallet not found for ID %d\n", walletID)
		if s.logger != nil {
			s.logger.LogError(err, map[string]interface{}{
				"action":    "withdraw_wallet_not_found",
				"wallet_id": walletID,
			})
		}
		return nil, fmt.Errorf("wallet not found")
	}
	fmt.Printf("SERVICE DEBUG: Wallet found: %+v\n", wallet)

	// Check wallet status
	if err := s.checkWalletStatus(&wallet); err != nil {
		tx.Rollback()
		fmt.Printf("SERVICE ERROR: Wallet status error: %v\n", err)
		if s.logger != nil {
			s.logger.LogError(err, map[string]interface{}{
				"action":    "withdraw_wallet_status_error",
				"wallet_id": walletID,
			})
		}
		return nil, fmt.Errorf("wallet status error: %w", err)
	}
	fmt.Printf("SERVICE DEBUG: Wallet status is valid\n")

	// Calculate withdrawal fee
	feePercentage := 0.01 // Default 1% withdrawal fee
	if s.config != nil {
		feePercentage = s.config.Fees.WithdrawalPercentage / 100.0
	}
	fee := amount * feePercentage
	totalAmount := amount + fee
	fmt.Printf("SERVICE DEBUG: Calculated fee: %.2f, totalAmount (amount+fee): %.2f\n", fee, totalAmount)

	// Check balance
	if wallet.Balance < totalAmount {
		tx.Rollback()
		fmt.Printf("SERVICE ERROR: Insufficient balance: required %.2f, available %.2f\n", totalAmount, wallet.Balance)
		if s.logger != nil {
			s.logger.LogError(fmt.Errorf("insufficient balance"), map[string]interface{}{
				"action":      "withdraw_insufficient_balance",
				"wallet_id":   walletID,
				"required":    totalAmount,
				"available":   wallet.Balance,
				"amount":      amount,
				"fee":         fee,
				"destination": destination,
			})
		}
		return nil, fmt.Errorf("insufficient balance including fees (%.2f required, %.2f available)", totalAmount, wallet.Balance)
	}
	fmt.Printf("SERVICE DEBUG: Sufficient balance for withdrawal\n")

	// Check withdrawal limits
	if err := s.validateWithdrawalLimits(&wallet, totalAmount); err != nil {
		tx.Rollback()
		fmt.Printf("SERVICE ERROR: Withdrawal limit exceeded: %v\n", err)
		if s.logger != nil {
			s.logger.LogError(err, map[string]interface{}{
				"action":    "withdraw_limit_exceeded",
				"wallet_id": walletID,
				"amount":    amount,
				"fee":       fee,
			})
		}
		return nil, fmt.Errorf("withdrawal limit exceeded: %w", err)
	}
	fmt.Printf("SERVICE DEBUG: Withdrawal limits validated\n")

	// Extract phone number for external payment (remove country code if present)
	phone := wallet.PhoneNumber
	phone = strings.TrimPrefix(phone, "268")  // Remove country code if present
	phone = strings.TrimPrefix(phone, "+268") // Remove +268 country code if present
	fmt.Printf("SERVICE DEBUG: Phone for external payment: %s\n", phone)

	// Process external payment first (credit = sending funds from our platform)
	fmt.Printf("SERVICE DEBUG: Initiating external payment via makePayment\n")
	if err := s.makePayment(destination, "credit", phone, amount); err != nil {
		tx.Rollback()
		fmt.Printf("SERVICE ERROR: External payment failed: %v\n", err)
		if s.logger != nil {
			s.logger.LogError(err, map[string]interface{}{
				"action":       "withdrawal_external_payment_failed",
				"wallet_id":    walletID,
				"phone_number": wallet.PhoneNumber,
				"amount":       amount,
				"destination":  destination,
			})
		}
		return nil, fmt.Errorf("external payment failed: %w", err)
	}
	fmt.Printf("SERVICE DEBUG: External payment successful\n")

	// External payment successful, now update wallet balance
	wallet.Balance -= totalAmount
	fmt.Printf("SERVICE DEBUG: New wallet balance after withdrawal: %.2f\n", wallet.Balance)
	if err := tx.Save(&wallet).Error; err != nil {
		tx.Rollback()
		fmt.Printf("SERVICE ERROR: Failed to update wallet balance after withdrawal\n")
		if s.logger != nil {
			s.logger.LogError(err, map[string]interface{}{
				"action":      "withdrawal_wallet_update_failed",
				"wallet_id":   walletID,
				"amount":      totalAmount,
				"destination": destination,
				"critical":    true,
			})
		}
		return nil, fmt.Errorf("failed to update wallet balance after successful external payment: %w", err)
	}
	fmt.Printf("SERVICE DEBUG: Wallet balance updated in DB\n")

	// Create transaction record for withdrawal
	transaction := models.WalletTransaction{
		WalletID:      walletID,
		Type:          "debit",
		Amount:        amount,
		BalanceAfter:  wallet.Balance,
		Reference:     reference,
		Description:   fmt.Sprintf("Wallet withdrawal to %s (external payment)", destination),
		Category:      "withdrawal",
		PaymentMethod: destination,
	}
	fmt.Printf("SERVICE DEBUG: Creating transaction record: %+v\n", transaction)

	if err := tx.Create(&transaction).Error; err != nil {
		tx.Rollback()
		fmt.Printf("SERVICE ERROR: Failed to create withdrawal transaction record\n")
		if s.logger != nil {
			s.logger.LogError(err, map[string]interface{}{
				"action":      "withdrawal_transaction_record_failed",
				"wallet_id":   walletID,
				"amount":      amount,
				"destination": destination,
				"critical":    true,
			})
		}
		return nil, fmt.Errorf("failed to create transaction record after successful external payment: %w", err)
	}
	fmt.Printf("SERVICE DEBUG: Transaction record created\n")

	// Commit database transaction first
	if err := tx.Commit().Error; err != nil {
		fmt.Printf("SERVICE ERROR: Failed to commit withdrawal transaction\n")
		if s.logger != nil {
			s.logger.LogError(err, map[string]interface{}{
				"action":      "withdrawal_commit_failed",
				"wallet_id":   walletID,
				"amount":      totalAmount,
				"destination": destination,
				"critical":    true,
			})
		}
		return nil, fmt.Errorf("failed to commit withdrawal after successful external payment: %w", err)
	}
	fmt.Printf("SERVICE DEBUG: Withdrawal transaction committed\n")

	// Collect withdrawal fee to platform wallet if fee > 0
	if fee > 0 {
		fmt.Printf("SERVICE DEBUG: Collecting withdrawal fee: %.2f\n", fee)
		if err := s.CollectPlatformFee(walletID, fee, "withdrawal_fee", fmt.Sprintf("Withdrawal fee for %s", destination)); err != nil {
			fmt.Printf("SERVICE WARNING: Withdrawal fee collection failed: %v\n", err)
			if s.logger != nil {
				s.logger.LogError(err, map[string]interface{}{
					"action":      "withdrawal_fee_collection_failed",
					"wallet_id":   walletID,
					"fee_amount":  fee,
					"destination": destination,
					"warning":     true,
				})
			}
		} else {
			fmt.Printf("SERVICE DEBUG: Withdrawal fee collected successfully\n")
		}
	}

	// Create audit log
	s.createAuditLog(walletID, "wallet_withdrawal", map[string]interface{}{
		"amount":      amount,
		"fee":         fee,
		"total":       totalAmount,
		"destination": destination,
		"reference":   reference,
	})
	fmt.Printf("SERVICE DEBUG: Audit log created for withdrawal\n")

	// Log successful withdrawal
	if s.logger != nil {
		s.logger.LogTransaction(
			fmt.Sprintf("%d", walletID),
			reference,
			"withdrawal_completed",
			totalAmount,
			fmt.Sprintf("Withdrawal of %.2f (fee: %.2f) to %s completed successfully", amount, fee, destination),
		)
	}
	fmt.Printf("SERVICE DEBUG: Withdrawal completed successfully\n")

	return &models.TransactionResponse{
		ID:            transaction.ID,
		Type:          transaction.Type,
		Amount:        transaction.Amount,
		BalanceAfter:  transaction.BalanceAfter,
		Reference:     transaction.Reference,
		Description:   transaction.Description,
		Category:      transaction.Category,
		PaymentMethod: transaction.PaymentMethod,
		Fee:           fee,
		CreatedAt:     transaction.CreatedAt,
	}, nil
}

// validateWithdrawalLimits validates withdrawal limits for a wallet
func (s *WalletService) validateWithdrawalLimits(wallet *models.Wallet, amount float64) error {
	// Check daily limit
	if wallet.DailySpent+amount > wallet.DailyLimit {
		return fmt.Errorf("daily withdrawal limit exceeded")
	}

	// Check monthly limit
	if wallet.MonthlySpent+amount > wallet.MonthlyLimit {
		return fmt.Errorf("monthly withdrawal limit exceeded")
	}

	// Additional business rules can be added here
	return nil
}

// EnsurePlatformWallet creates the platform wallet if it doesn't exist
func (s *WalletService) EnsurePlatformWallet() (*models.Wallet, error) {
	// Check if platform wallet already exists
	var platformWallet models.Wallet
	err := s.db.Where("phone_number = ?", PlatformWalletPhone).First(&platformWallet).Error

	if err == nil {
		// Platform wallet exists
		return &platformWallet, nil
	}

	if err != gorm.ErrRecordNotFound {
		// Database error
		s.logger.LogError(err, map[string]interface{}{
			"action": "check_platform_wallet",
		})
		return nil, fmt.Errorf("failed to check platform wallet: %w", err)
	}

	// Platform wallet doesn't exist, create it
	now := time.Now()
	platformWallet = models.Wallet{
		PhoneNumber:    PlatformWalletPhone,
		WalletType:     PlatformWalletType,
		Status:         "active",
		Balance:        0.0,
		DailyLimit:     1000000.0,  // High limit for platform operations
		MonthlyLimit:   30000000.0, // High limit for platform operations
		DailySpent:     0.0,
		MonthlySpent:   0.0,
		LastLimitReset: &now, // Initialize with current time
		Currency:       "SZL",
		IsVerified:     true,
		Metadata: datatypes.JSON(`{
			"is_platform_wallet": true,
			"purpose": "Platform fees and charges collection",
			"created_by": "system"
		}`),
	}

	if err := s.db.Create(&platformWallet).Error; err != nil {
		s.logger.LogError(err, map[string]interface{}{
			"action": "create_platform_wallet",
		})
		return nil, fmt.Errorf("failed to create platform wallet: %w", err)
	}

	// Log platform wallet creation
	s.logger.LogTransaction(
		"system",
		"platform_wallet_creation",
		"platform_wallet_created",
		0.0,
		"Platform wallet created for fee collection",
	)

	return &platformWallet, nil
}

// GetPlatformWallet retrieves the platform wallet
func (s *WalletService) GetPlatformWallet() (*models.Wallet, error) {
	return s.EnsurePlatformWallet()
}

// CollectPlatformFee transfers fees to the platform wallet
func (s *WalletService) CollectPlatformFee(fromWalletID uint, amount float64, feeType, description string) error {
	if amount <= 0 {
		return fmt.Errorf("fee amount must be positive")
	}

	// Start database transaction
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Get source wallet
	var sourceWallet models.Wallet
	if err := tx.First(&sourceWallet, fromWalletID).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("source wallet not found: %w", err)
	}

	// Check if source wallet has sufficient balance
	if sourceWallet.Balance < amount {
		tx.Rollback()
		return fmt.Errorf("insufficient balance for fee collection")
	}

	// Get or create platform wallet
	platformWallet, err := s.EnsurePlatformWallet()
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to get platform wallet: %w", err)
	}

	// Deduct from source wallet
	sourceWallet.Balance -= amount
	if err := tx.Save(&sourceWallet).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to deduct fee from source wallet: %w", err)
	}

	// Add to platform wallet
	platformWallet.Balance += amount
	if err := tx.Save(platformWallet).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to add fee to platform wallet: %w", err)
	}

	// Create transaction record for source wallet (debit)
	sourceTransaction := models.WalletTransaction{
		WalletID:      fromWalletID,
		Type:          "debit",
		Amount:        amount,
		BalanceAfter:  sourceWallet.Balance,
		Reference:     uuid.New().String(),
		Description:   fmt.Sprintf("%s: %s", feeType, description),
		Category:      "fee",
		PaymentMethod: "platform_fee",
	}

	if err := tx.Create(&sourceTransaction).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create source transaction record: %w", err)
	}

	// Create transaction record for platform wallet (credit)
	platformTransaction := models.WalletTransaction{
		WalletID:      platformWallet.ID,
		Type:          "credit",
		Amount:        amount,
		BalanceAfter:  platformWallet.Balance,
		Reference:     sourceTransaction.Reference,
		Description:   fmt.Sprintf("Fee collection from wallet %d: %s", fromWalletID, description),
		Category:      "fee_collection",
		PaymentMethod: "platform_fee",
	}

	if err := tx.Create(&platformTransaction).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create platform transaction record: %w", err)
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit fee collection: %w", err)
	}

	// Log successful fee collection
	s.logger.LogTransaction(
		fmt.Sprintf("%d", fromWalletID),
		sourceTransaction.Reference,
		"fee_collected",
		amount,
		fmt.Sprintf("%s fee collected: %s", feeType, description),
	)

	return nil
}
