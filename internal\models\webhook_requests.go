package models

import "time"

// Webhook-specific request and response models

// WebhookRegistrationRequest represents webhook endpoint registration
type WebhookRegistrationRequest struct {
	URL         string   `json:"url" binding:"required,url"`
	Secret      string   `json:"secret" binding:"required,min=16"`
	Events      []string `json:"events" binding:"required,min=1"`
	Description string   `json:"description"`
	IsActive    bool     `json:"is_active"`
	RetryCount  int      `json:"retry_count" binding:"min=0,max=10"`
	Timeout     int      `json:"timeout" binding:"min=5,max=60"` // seconds
}

// WebhookUpdateRequest represents webhook endpoint update
type WebhookUpdateRequest struct {
	URL         string   `json:"url" binding:"omitempty,url"`
	Secret      string   `json:"secret" binding:"omitempty,min=16"`
	Events      []string `json:"events" binding:"omitempty,min=1"`
	Description string   `json:"description"`
	IsActive    *bool    `json:"is_active"`
	RetryCount  *int     `json:"retry_count" binding:"omitempty,min=0,max=10"`
	Timeout     *int     `json:"timeout" binding:"omitempty,min=5,max=60"`
}

// ExternalWebhookPayload represents incoming external webhook payload
type ExternalWebhookPayload struct {
	Event     string                 `json:"event" binding:"required"`
	Data      map[string]interface{} `json:"data" binding:"required"`
	Source    string                 `json:"source" binding:"required"`
	Timestamp string                 `json:"timestamp"`
	Version   string                 `json:"version"`
	Signature string                 `json:"signature"`
}

// PaymentEngineWebhook represents payment engine webhook payload
type PaymentEngineWebhook struct {
	EventType     string                 `json:"event_type" binding:"required"`
	TransactionID string                 `json:"transaction_id" binding:"required"`
	WalletID      uint                   `json:"wallet_id" binding:"required"`
	Amount        float64                `json:"amount" binding:"required"`
	Currency      string                 `json:"currency" binding:"required"`
	Status        string                 `json:"status" binding:"required"`
	Metadata      map[string]interface{} `json:"metadata"`
	ProcessedAt   time.Time              `json:"processed_at"`
}

// SMSProviderWebhook represents SMS provider webhook payload
type SMSProviderWebhook struct {
	EventType   string                 `json:"event_type" binding:"required"`
	MessageID   string                 `json:"message_id" binding:"required"`
	PhoneNumber string                 `json:"phone_number" binding:"required"`
	Status      string                 `json:"status" binding:"required"`
	DeliveredAt *time.Time             `json:"delivered_at"`
	FailReason  string                 `json:"fail_reason"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// EmailProviderWebhook represents email provider webhook payload
type EmailProviderWebhook struct {
	EventType   string                 `json:"event_type" binding:"required"`
	MessageID   string                 `json:"message_id" binding:"required"`
	Email       string                 `json:"email" binding:"required"`
	Status      string                 `json:"status" binding:"required"`
	DeliveredAt *time.Time             `json:"delivered_at"`
	OpenedAt    *time.Time             `json:"opened_at"`
	ClickedAt   *time.Time             `json:"clicked_at"`
	FailReason  string                 `json:"fail_reason"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// FraudDetectionWebhook represents fraud detection webhook payload
type FraudDetectionWebhook struct {
	EventType     string                 `json:"event_type" binding:"required"`
	AlertID       string                 `json:"alert_id" binding:"required"`
	WalletID      uint                   `json:"wallet_id" binding:"required"`
	RiskScore     float64                `json:"risk_score" binding:"required"`
	RiskLevel     string                 `json:"risk_level" binding:"required"`
	Reasons       []string               `json:"reasons" binding:"required"`
	Recommendation string                `json:"recommendation" binding:"required"`
	TransactionID string                 `json:"transaction_id"`
	Metadata      map[string]interface{} `json:"metadata"`
	DetectedAt    time.Time              `json:"detected_at"`
}

// InternalWebhookRequest represents internal service webhook
type InternalWebhookRequest struct {
	Event     string                 `json:"event" binding:"required"`
	Data      map[string]interface{} `json:"data" binding:"required"`
	Source    string                 `json:"source" binding:"required"`
	Target    string                 `json:"target"`
	Timestamp string                 `json:"timestamp"`
	RequestID string                 `json:"request_id"`
	Priority  string                 `json:"priority" binding:"oneof=low normal high critical"`
}

// BatchWalletLookupRequest represents batch wallet lookup request
type BatchWalletLookupRequest struct {
	WalletIDs    []uint   `json:"wallet_ids" binding:"required_without_all=PhoneNumbers UserIDs"`
	PhoneNumbers []string `json:"phone_numbers" binding:"required_without_all=WalletIDs UserIDs"`
	UserIDs      []uint   `json:"user_ids" binding:"required_without_all=WalletIDs PhoneNumbers"`
	IncludeCards bool     `json:"include_cards"`
	IncludeBalance bool   `json:"include_balance"`
}

// WebhookResponse represents webhook endpoint data
type WebhookResponse struct {
	ID          uint     `json:"id"`
	URL         string   `json:"url"`
	Events      []string `json:"events"`
	Description string   `json:"description"`
	IsActive    bool     `json:"is_active"`
	RetryCount  int      `json:"retry_count"`
	Timeout     int      `json:"timeout"`
	CreatedAt   string   `json:"created_at"`
	UpdatedAt   string   `json:"updated_at"`
	LastUsed    string   `json:"last_used,omitempty"`
}

// WebhookEventResponse represents webhook event data
type WebhookEventResponse struct {
	ID          uint                   `json:"id"`
	WebhookID   uint                   `json:"webhook_id"`
	Event       string                 `json:"event"`
	Payload     map[string]interface{} `json:"payload"`
	Status      string                 `json:"status"`
	Attempts    int                    `json:"attempts"`
	LastAttempt string                 `json:"last_attempt,omitempty"`
	Response    string                 `json:"response,omitempty"`
	CreatedAt   string                 `json:"created_at"`
}

// WebhookDeliveryResponse represents webhook delivery status
type WebhookDeliveryResponse struct {
	EventID      uint   `json:"event_id"`
	Status       string `json:"status"`
	StatusCode   int    `json:"status_code"`
	Response     string `json:"response"`
	Duration     int    `json:"duration_ms"`
	Attempt      int    `json:"attempt"`
	NextRetry    string `json:"next_retry,omitempty"`
	DeliveredAt  string `json:"delivered_at,omitempty"`
}

// InternalAPIResponse represents internal API response
type InternalAPIResponse struct {
	Success   bool        `json:"success"`
	Data      interface{} `json:"data"`
	Message   string      `json:"message"`
	Timestamp string      `json:"timestamp"`
	RequestID string      `json:"request_id"`
	Source    string      `json:"source"`
}

// BatchLookupResponse represents batch lookup response
type BatchLookupResponse struct {
	WalletLookups []WalletLookupResult `json:"wallet_lookups,omitempty"`
	PhoneLookups  []PhoneLookupResult  `json:"phone_lookups,omitempty"`
	UserLookups   []UserLookupResult   `json:"user_lookups,omitempty"`
	Summary       BatchLookupSummary   `json:"summary"`
}

// WalletLookupResult represents individual wallet lookup result
type WalletLookupResult struct {
	WalletID uint        `json:"wallet_id"`
	Found    bool        `json:"found"`
	Wallet   interface{} `json:"wallet,omitempty"`
	Cards    interface{} `json:"cards,omitempty"`
	Error    string      `json:"error,omitempty"`
}

// PhoneLookupResult represents individual phone lookup result
type PhoneLookupResult struct {
	PhoneNumber string      `json:"phone_number"`
	Found       bool        `json:"found"`
	Wallet      interface{} `json:"wallet,omitempty"`
	Cards       interface{} `json:"cards,omitempty"`
	Error       string      `json:"error,omitempty"`
}

// UserLookupResult represents individual user lookup result
type UserLookupResult struct {
	UserID uint        `json:"user_id"`
	Found  bool        `json:"found"`
	Wallet interface{} `json:"wallet,omitempty"`
	Cards  interface{} `json:"cards,omitempty"`
	Error  string      `json:"error,omitempty"`
}

// BatchLookupSummary represents batch lookup summary
type BatchLookupSummary struct {
	TotalRequested int `json:"total_requested"`
	TotalFound     int `json:"total_found"`
	TotalErrors    int `json:"total_errors"`
}
