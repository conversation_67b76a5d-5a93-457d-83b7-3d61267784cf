package unit

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"

	"wallet-platform/internal/models"
	"wallet-platform/internal/services"
	"wallet-platform/pkg/logger"
)

type PayCardServiceTestSuite struct {
	suite.Suite
	db             *gorm.DB
	walletService  *services.WalletService
	payCardService *services.PayCardService
	logger         *logger.Logger
	testWallet     *models.Wallet
}

func (suite *PayCardServiceTestSuite) SetupTest() {
	// Setup in-memory SQLite database for testing
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	assert.NoError(suite.T(), err)

	// Auto-migrate the schema
	err = db.AutoMigrate(
		&models.Wallet{},
		&models.PayCard{},
		&models.CardTransaction{},
		&models.CardLimit{},
		&models.CardSecurity{},
		&models.WalletTransaction{},
	)
	assert.NoError(suite.T(), err)

	suite.db = db
	suite.logger = logger.New()
	suite.walletService = services.NewWalletService(db, suite.logger)
	suite.payCardService = services.NewPayCardService(db, suite.logger)

	// Create test wallet
	wallet, err := suite.walletService.CreateWallet("+256701234567", "individual")
	assert.NoError(suite.T(), err)
	suite.testWallet = wallet
}

func (suite *PayCardServiceTestSuite) TearDownTest() {
	// Clean up database
	sqlDB, _ := suite.db.DB()
	sqlDB.Close()
}

func (suite *PayCardServiceTestSuite) TestCreateCard_Success() {
	// Test data
	cardType := "virtual"
	cardName := "Test Card"

	// Create card
	card, err := suite.payCardService.CreateCard(suite.testWallet.ID, cardType, cardName)

	// Assertions
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), card)
	assert.Equal(suite.T(), suite.testWallet.ID, card.WalletID)
	assert.Equal(suite.T(), cardType, card.CardType)
	assert.Equal(suite.T(), cardName, card.CardName)
	assert.NotEmpty(suite.T(), card.CardNumber)
	assert.NotEmpty(suite.T(), card.CVV)
	assert.NotEmpty(suite.T(), card.ExpiryDate)
	assert.Equal(suite.T(), "active", card.Status)
}

func (suite *PayCardServiceTestSuite) TestCreateCard_InvalidWallet() {
	// Try to create card for non-existent wallet
	_, err := suite.payCardService.CreateCard(999, "virtual", "Test Card")

	// Assertions
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "wallet not found")
}

func (suite *PayCardServiceTestSuite) TestGetCard_Success() {
	// Create test card
	card, err := suite.payCardService.CreateCard(suite.testWallet.ID, "virtual", "Test Card")
	assert.NoError(suite.T(), err)

	// Get card by ID
	retrievedCard, err := suite.payCardService.GetCard(card.ID)

	// Assertions
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), retrievedCard)
	assert.Equal(suite.T(), card.ID, retrievedCard.ID)
	assert.Equal(suite.T(), card.CardNumber, retrievedCard.CardNumber)
}

func (suite *PayCardServiceTestSuite) TestGetCard_NotFound() {
	// Try to get non-existent card
	_, err := suite.payCardService.GetCard(999)

	// Assertions
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "card not found")
}

func (suite *PayCardServiceTestSuite) TestGetCardsByWallet_Success() {
	// Create multiple test cards
	card1, err := suite.payCardService.CreateCard(suite.testWallet.ID, "virtual", "Card 1")
	assert.NoError(suite.T(), err)
	card2, err := suite.payCardService.CreateCard(suite.testWallet.ID, "physical", "Card 2")
	assert.NoError(suite.T(), err)

	// Get cards by wallet
	cards, err := suite.payCardService.GetCardsByWallet(suite.testWallet.ID)

	// Assertions
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), cards, 2)
	
	// Verify cards are returned
	cardIDs := []uint{cards[0].ID, cards[1].ID}
	assert.Contains(suite.T(), cardIDs, card1.ID)
	assert.Contains(suite.T(), cardIDs, card2.ID)
}

func (suite *PayCardServiceTestSuite) TestUpdateCardPIN_Success() {
	// Create test card
	card, err := suite.payCardService.CreateCard(suite.testWallet.ID, "virtual", "Test Card")
	assert.NoError(suite.T(), err)

	// Update PIN
	newPIN := "5678"
	err = suite.payCardService.UpdateCardPIN(card.ID, newPIN)

	// Assertions
	assert.NoError(suite.T(), err)

	// Verify PIN updated (in real implementation, PIN would be hashed)
	updatedCard, err := suite.payCardService.GetCard(card.ID)
	assert.NoError(suite.T(), err)
	assert.NotEmpty(suite.T(), updatedCard.PIN) // PIN should be hashed
}

func (suite *PayCardServiceTestSuite) TestBlockCard_Success() {
	// Create test card
	card, err := suite.payCardService.CreateCard(suite.testWallet.ID, "virtual", "Test Card")
	assert.NoError(suite.T(), err)

	// Block card
	reason := "Lost card"
	err = suite.payCardService.BlockCard(card.ID, reason)

	// Assertions
	assert.NoError(suite.T(), err)

	// Verify card blocked
	updatedCard, err := suite.payCardService.GetCard(card.ID)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "blocked", updatedCard.Status)
}

func (suite *PayCardServiceTestSuite) TestUnblockCard_Success() {
	// Create and block test card
	card, err := suite.payCardService.CreateCard(suite.testWallet.ID, "virtual", "Test Card")
	assert.NoError(suite.T(), err)
	err = suite.payCardService.BlockCard(card.ID, "Test block")
	assert.NoError(suite.T(), err)

	// Unblock card
	err = suite.payCardService.UnblockCard(card.ID)

	// Assertions
	assert.NoError(suite.T(), err)

	// Verify card unblocked
	updatedCard, err := suite.payCardService.GetCard(card.ID)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "active", updatedCard.Status)
}

func (suite *PayCardServiceTestSuite) TestProcessTransaction_Success() {
	// Create test card and topup wallet
	card, err := suite.payCardService.CreateCard(suite.testWallet.ID, "virtual", "Test Card")
	assert.NoError(suite.T(), err)
	err = suite.walletService.TopupWallet(suite.testWallet.ID, 200.0, "mobile_money", "TOPUP123")
	assert.NoError(suite.T(), err)

	// Process transaction
	amount := 50.0
	merchantInfo := map[string]interface{}{
		"merchant_name": "Test Store",
		"merchant_id":   "MERCHANT123",
		"location":      "Kampala, Uganda",
	}
	
	transaction, err := suite.payCardService.ProcessTransaction(
		card.ID, amount, "purchase", merchantInfo,
	)

	// Assertions
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), transaction)
	assert.Equal(suite.T(), card.ID, transaction.CardID)
	assert.Equal(suite.T(), amount, transaction.Amount)
	assert.Equal(suite.T(), "purchase", transaction.TransactionType)
	assert.Equal(suite.T(), "completed", transaction.Status)

	// Verify wallet balance deducted
	updatedWallet, err := suite.walletService.GetWallet(suite.testWallet.ID)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), 150.0, updatedWallet.Balance)
}

func (suite *PayCardServiceTestSuite) TestProcessTransaction_InsufficientBalance() {
	// Create test card without sufficient wallet balance
	card, err := suite.payCardService.CreateCard(suite.testWallet.ID, "virtual", "Test Card")
	assert.NoError(suite.T(), err)

	// Try to process transaction
	amount := 100.0
	merchantInfo := map[string]interface{}{
		"merchant_name": "Test Store",
		"merchant_id":   "MERCHANT123",
	}
	
	_, err = suite.payCardService.ProcessTransaction(
		card.ID, amount, "purchase", merchantInfo,
	)

	// Assertions
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "insufficient balance")
}

func (suite *PayCardServiceTestSuite) TestProcessTransaction_BlockedCard() {
	// Create and block test card
	card, err := suite.payCardService.CreateCard(suite.testWallet.ID, "virtual", "Test Card")
	assert.NoError(suite.T(), err)
	err = suite.payCardService.BlockCard(card.ID, "Test block")
	assert.NoError(suite.T(), err)

	// Try to process transaction
	amount := 50.0
	merchantInfo := map[string]interface{}{
		"merchant_name": "Test Store",
		"merchant_id":   "MERCHANT123",
	}
	
	_, err = suite.payCardService.ProcessTransaction(
		card.ID, amount, "purchase", merchantInfo,
	)

	// Assertions
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "card is blocked")
}

func (suite *PayCardServiceTestSuite) TestGetCardTransactions_Success() {
	// Create test card and process transactions
	card, err := suite.payCardService.CreateCard(suite.testWallet.ID, "virtual", "Test Card")
	assert.NoError(suite.T(), err)
	err = suite.walletService.TopupWallet(suite.testWallet.ID, 200.0, "mobile_money", "TOPUP123")
	assert.NoError(suite.T(), err)

	// Process multiple transactions
	merchantInfo := map[string]interface{}{
		"merchant_name": "Test Store",
		"merchant_id":   "MERCHANT123",
	}
	_, err = suite.payCardService.ProcessTransaction(card.ID, 30.0, "purchase", merchantInfo)
	assert.NoError(suite.T(), err)
	_, err = suite.payCardService.ProcessTransaction(card.ID, 20.0, "purchase", merchantInfo)
	assert.NoError(suite.T(), err)

	// Get transactions
	transactions, err := suite.payCardService.GetCardTransactions(card.ID, 1, 10)

	// Assertions
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), transactions, 2)
	assert.Equal(suite.T(), "purchase", transactions[0].TransactionType)
	assert.Equal(suite.T(), "purchase", transactions[1].TransactionType)
}

func (suite *PayCardServiceTestSuite) TestSetCardLimits_Success() {
	// Create test card
	card, err := suite.payCardService.CreateCard(suite.testWallet.ID, "virtual", "Test Card")
	assert.NoError(suite.T(), err)

	// Set limits
	dailyLimit := 500.0
	monthlyLimit := 5000.0
	err = suite.payCardService.SetCardLimits(card.ID, dailyLimit, monthlyLimit)

	// Assertions
	assert.NoError(suite.T(), err)

	// Verify limits set
	var cardLimit models.CardLimit
	err = suite.db.Where("card_id = ?", card.ID).First(&cardLimit).Error
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), dailyLimit, cardLimit.DailyLimit)
	assert.Equal(suite.T(), monthlyLimit, cardLimit.MonthlyLimit)
}

func TestPayCardServiceTestSuite(t *testing.T) {
	suite.Run(t, new(PayCardServiceTestSuite))
}
