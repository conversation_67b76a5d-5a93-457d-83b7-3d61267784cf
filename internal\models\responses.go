package models

import "time"

// Common response structures used across all controllers

// ErrorResponse represents a standard error response
type ErrorResponse struct {
	Error     string `json:"error"`
	Message   string `json:"message"`
	Code      string `json:"code"`
	Timestamp string `json:"timestamp,omitempty"`
	RequestID string `json:"request_id,omitempty"`
}

// SuccessResponse represents a standard success response
type SuccessResponse struct {
	Success   bool   `json:"success"`
	Message   string `json:"message"`
	Timestamp string `json:"timestamp,omitempty"`
	RequestID string `json:"request_id,omitempty"`
}

// DataResponse represents a standard response with data
type DataResponse struct {
	Success   bool        `json:"success"`
	Message   string      `json:"message"`
	Data      interface{} `json:"data"`
	Timestamp string      `json:"timestamp,omitempty"`
	RequestID string      `json:"request_id,omitempty"`
}

// PaginatedResponse represents a paginated response
type PaginatedResponse struct {
	Success    bool        `json:"success"`
	Message    string      `json:"message"`
	Data       interface{} `json:"data"`
	Pagination Pagination  `json:"pagination"`
	Timestamp  string      `json:"timestamp,omitempty"`
	RequestID  string      `json:"request_id,omitempty"`
}

// Pagination represents pagination metadata
type Pagination struct {
	Page       int `json:"page"`
	Limit      int `json:"limit"`
	Total      int `json:"total"`
	TotalPages int `json:"total_pages"`
	HasNext    bool `json:"has_next"`
	HasPrev    bool `json:"has_prev"`
}

// AuthResponse represents authentication response
type AuthResponse struct {
	Success   bool   `json:"success"`
	Message   string `json:"message"`
	Token     string `json:"token"`
	UserID    uint   `json:"user_id"`
	WalletID  uint   `json:"wallet_id"`
	ExpiresIn int    `json:"expires_in"`
	Timestamp string `json:"timestamp,omitempty"`
	RequestID string `json:"request_id,omitempty"`
}

// HealthResponse represents health check response
type HealthResponse struct {
	Status    string            `json:"status"`
	Timestamp time.Time         `json:"timestamp"`
	Services  map[string]string `json:"services"`
	Uptime    string            `json:"uptime"`
	Version   string            `json:"version"`
}

// ValidationErrorResponse represents validation error response
type ValidationErrorResponse struct {
	Error     string                 `json:"error"`
	Message   string                 `json:"message"`
	Code      string                 `json:"code"`
	Errors    map[string]interface{} `json:"errors"`
	Timestamp string                 `json:"timestamp,omitempty"`
	RequestID string                 `json:"request_id,omitempty"`
}

// Helper functions to create standard responses

// NewErrorResponse creates a new error response
func NewErrorResponse(error, message, code string) *ErrorResponse {
	return &ErrorResponse{
		Error:     error,
		Message:   message,
		Code:      code,
		Timestamp: time.Now().UTC().Format(time.RFC3339),
	}
}

// NewSuccessResponse creates a new success response
func NewSuccessResponse(message string) *SuccessResponse {
	return &SuccessResponse{
		Success:   true,
		Message:   message,
		Timestamp: time.Now().UTC().Format(time.RFC3339),
	}
}

// NewDataResponse creates a new data response
func NewDataResponse(message string, data interface{}) *DataResponse {
	return &DataResponse{
		Success:   true,
		Message:   message,
		Data:      data,
		Timestamp: time.Now().UTC().Format(time.RFC3339),
	}
}

// NewPaginatedResponse creates a new paginated response
func NewPaginatedResponse(message string, data interface{}, pagination Pagination) *PaginatedResponse {
	return &PaginatedResponse{
		Success:    true,
		Message:    message,
		Data:       data,
		Pagination: pagination,
		Timestamp:  time.Now().UTC().Format(time.RFC3339),
	}
}

// NewAuthResponse creates a new auth response
func NewAuthResponse(message, token string, userID, walletID uint, expiresIn int) *AuthResponse {
	return &AuthResponse{
		Success:   true,
		Message:   message,
		Token:     token,
		UserID:    userID,
		WalletID:  walletID,
		ExpiresIn: expiresIn,
		Timestamp: time.Now().UTC().Format(time.RFC3339),
	}
}

// NewValidationErrorResponse creates a new validation error response
func NewValidationErrorResponse(error, message, code string, errors map[string]interface{}) *ValidationErrorResponse {
	return &ValidationErrorResponse{
		Error:     error,
		Message:   message,
		Code:      code,
		Errors:    errors,
		Timestamp: time.Now().UTC().Format(time.RFC3339),
	}
}

// CalculatePagination calculates pagination metadata
func CalculatePagination(page, limit, total int) Pagination {
	totalPages := (total + limit - 1) / limit
	if totalPages == 0 {
		totalPages = 1
	}

	return Pagination{
		Page:       page,
		Limit:      limit,
		Total:      total,
		TotalPages: totalPages,
		HasNext:    page < totalPages,
		HasPrev:    page > 1,
	}
}
