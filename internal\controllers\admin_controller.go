package controllers

import (
	"net/http"
	"strconv"

	"wallet-platform/internal/services"

	"github.com/gin-gonic/gin"
)

// AdminController handles admin-related requests
type AdminController struct {
	container *services.Container
}

// NewAdminController creates a new admin controller
func NewAdminController(container *services.Container) *AdminController {
	return &AdminController{
		container: container,
	}
}

// UserUpdateRequest represents admin user update request
type UserUpdateRequest struct {
	FirstName   string `json:"first_name"`
	LastName    string `json:"last_name"`
	Email       string `json:"email"`
	PhoneNumber string `json:"phone_number"`
	Status      string `json:"status"`
	KYCLevel    string `json:"kyc_level"`
}

// WalletUpdateRequest represents admin wallet update request
type WalletUpdateRequest struct {
	Status       string  `json:"status"`
	WalletType   string  `json:"wallet_type"`
	KYCLevel     string  `json:"kyc_level"`
	DailyLimit   float64 `json:"daily_limit"`
	MonthlyLimit float64 `json:"monthly_limit"`
}

// ServiceCreateRequest represents service creation request
type ServiceCreateRequest struct {
	Name        string  `json:"name" binding:"required"`
	Description string  `json:"description" binding:"required"`
	ServiceType string  `json:"service_type" binding:"required"`
	Price       float64 `json:"price" binding:"required"`
	Currency    string  `json:"currency" binding:"required"`
	IsActive    bool    `json:"is_active"`
	IsPublic    bool    `json:"is_public"`
}

// System Management

// SystemHealthCheck performs comprehensive system health check
func (ac *AdminController) SystemHealthCheck(c *gin.Context) {
	// Get system health from monitoring service
	health, err := ac.container.MonitoringService.GetSystemHealth()
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "HEALTH_CHECK_FAILED",
			Message: err.Error(),
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    health,
	})
}

// GetSystemMetrics retrieves system performance metrics
func (ac *AdminController) GetSystemMetrics(c *gin.Context) {
	// Get performance metrics from monitoring service
	metrics, err := ac.container.MonitoringService.GetPerformanceMetrics()
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "METRICS_FETCH_FAILED",
			Message: err.Error(),
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    metrics,
	})
}

// GetSystemAnalytics retrieves system analytics
func (ac *AdminController) GetSystemAnalytics(c *gin.Context) {
	period := c.DefaultQuery("period", "24h")
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")

	analytics, err := ac.container.AnalyticsService.GenerateSystemAnalytics(period, startDate, endDate)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "ANALYTICS_FETCH_FAILED",
			Message: err.Error(),
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    analytics,
	})
}

// EnableMaintenanceMode enables system maintenance mode
func (ac *AdminController) EnableMaintenanceMode(c *gin.Context) {
	var requestBody struct {
		Message  string `json:"message" binding:"required"`
		Duration int    `json:"duration" binding:"required"` // in minutes
	}
	if err := c.ShouldBindJSON(&requestBody); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Message and duration are required",
			Code:    "400",
		})
		return
	}

	err := ac.container.MonitoringService.EnableMaintenanceMode(requestBody.Message, requestBody.Duration)
	if err != nil {
		ac.container.Logger.LogError(err, map[string]interface{}{
			"action":   "enable_maintenance_mode",
			"message":  requestBody.Message,
			"duration": requestBody.Duration,
		})
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "MAINTENANCE_FAILED",
			Message: "Failed to enable maintenance mode",
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Maintenance mode enabled",
		"data": gin.H{
			"maintenance_message": requestBody.Message,
			"duration_minutes":    requestBody.Duration,
		},
	})
}

// DisableMaintenanceMode disables system maintenance mode
func (ac *AdminController) DisableMaintenanceMode(c *gin.Context) {
	err := ac.container.MonitoringService.DisableMaintenanceMode()
	if err != nil {
		ac.container.Logger.LogError(err, map[string]interface{}{
			"action": "disable_maintenance_mode",
		})
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "MAINTENANCE_FAILED",
			Message: "Failed to disable maintenance mode",
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Maintenance mode disabled",
	})
}

// User Management

// GetAllUsers retrieves all users with pagination
func (ac *AdminController) GetAllUsers(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	status := c.Query("status")
	search := c.Query("search")

	users, total, err := ac.container.UserService.GetAllUsers(page, limit, status, search)
	if err != nil {
		ac.container.Logger.LogError(err, map[string]interface{}{
			"action": "admin_get_all_users",
			"page":   page,
			"limit":  limit,
			"status": status,
			"search": search,
		})
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "FETCH_FAILED",
			Message: "Failed to fetch users",
			Code:    "500",
		})
		return
	}

	// Convert users to response format
	userResponses := make([]interface{}, len(users))
	for i, user := range users {
		userResponses[i] = ac.container.UserService.UserToResponse(&user)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Users retrieved successfully",
		"data": gin.H{
			"users":  userResponses,
			"page":   page,
			"limit":  limit,
			"total":  total,
			"status": status,
			"search": search,
		},
	})
}

// GetUser retrieves a specific user
func (ac *AdminController) GetUser(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_ID",
			Message: "Invalid user ID",
			Code:    "400",
		})
		return
	}

	user, err := ac.container.UserService.GetUserByID(uint(userID))
	if err != nil {
		if err.Error() == "user not found" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:   "USER_NOT_FOUND",
				Message: "User not found",
				Code:    "404",
			})
			return
		}

		ac.container.Logger.LogError(err, map[string]interface{}{
			"action":  "admin_get_user",
			"user_id": userID,
		})
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "FETCH_FAILED",
			Message: "Failed to fetch user",
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "User retrieved successfully",
		"data":    ac.container.UserService.UserToResponse(user),
	})
}

// UpdateUser updates user information
func (ac *AdminController) UpdateUser(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_ID",
			Message: "Invalid user ID",
			Code:    "400",
		})
		return
	}

	var req UserUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	// Build updates map with only non-empty fields
	updates := make(map[string]interface{})
	if req.FirstName != "" {
		updates["first_name"] = req.FirstName
	}
	if req.LastName != "" {
		updates["last_name"] = req.LastName
	}
	if req.Email != "" {
		updates["email"] = req.Email
	}
	if req.PhoneNumber != "" {
		updates["phone_number"] = req.PhoneNumber
	}
	if req.Status != "" {
		updates["status"] = req.Status
	}
	if req.KYCLevel != "" {
		updates["kyc_level"] = req.KYCLevel
	}

	if len(updates) == 0 {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "NO_UPDATES",
			Message: "No valid fields to update",
			Code:    "400",
		})
		return
	}

	user, err := ac.container.UserService.UpdateUser(uint(userID), updates)
	if err != nil {
		if err.Error() == "user not found" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:   "USER_NOT_FOUND",
				Message: "User not found",
				Code:    "404",
			})
			return
		}

		ac.container.Logger.LogError(err, map[string]interface{}{
			"action":  "admin_update_user",
			"user_id": userID,
			"updates": updates,
		})
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "UPDATE_FAILED",
			Message: "Failed to update user",
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "User updated successfully",
		"data":    ac.container.UserService.UserToResponse(user),
	})
}

// DeleteUser deletes a user
func (ac *AdminController) DeleteUser(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_ID",
			Message: "Invalid user ID",
			Code:    "400",
		})
		return
	}

	err = ac.container.UserService.DeleteUser(uint(userID))
	if err != nil {
		if err.Error() == "user not found" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:   "USER_NOT_FOUND",
				Message: "User not found",
				Code:    "404",
			})
			return
		}

		ac.container.Logger.LogError(err, map[string]interface{}{
			"action":  "admin_delete_user",
			"user_id": userID,
		})
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "DELETE_FAILED",
			Message: "Failed to delete user",
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "User deleted successfully",
		"data": gin.H{
			"user_id": userID,
		},
	})
}

// SuspendUser suspends a user account
func (ac *AdminController) SuspendUser(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_ID",
			Message: "Invalid user ID",
			Code:    "400",
		})
		return
	}

	var requestBody struct {
		Reason string `json:"reason" binding:"required"`
	}
	if err := c.ShouldBindJSON(&requestBody); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Reason is required",
			Code:    "400",
		})
		return
	}

	err = ac.container.UserService.SuspendUser(uint(userID), requestBody.Reason)
	if err != nil {
		if err.Error() == "user not found" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:   "USER_NOT_FOUND",
				Message: "User not found",
				Code:    "404",
			})
			return
		}

		ac.container.Logger.LogError(err, map[string]interface{}{
			"action":  "admin_suspend_user",
			"user_id": userID,
			"reason":  requestBody.Reason,
		})
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "SUSPEND_FAILED",
			Message: "Failed to suspend user",
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "User suspended successfully",
		"data": gin.H{
			"user_id": userID,
			"reason":  requestBody.Reason,
		},
	})
}

// UnsuspendUser unsuspends a user account
func (ac *AdminController) UnsuspendUser(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_ID",
			Message: "Invalid user ID",
			Code:    "400",
		})
		return
	}

	err = ac.container.UserService.UnsuspendUser(uint(userID))
	if err != nil {
		if err.Error() == "user not found" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:   "USER_NOT_FOUND",
				Message: "User not found",
				Code:    "404",
			})
			return
		}

		ac.container.Logger.LogError(err, map[string]interface{}{
			"action":  "admin_unsuspend_user",
			"user_id": userID,
		})
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "UNSUSPEND_FAILED",
			Message: "Failed to unsuspend user",
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "User unsuspended successfully",
		"data": gin.H{
			"user_id": userID,
		},
	})
}

// Wallet Management

// GetAllWallets retrieves all wallets with pagination
func (ac *AdminController) GetAllWallets(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	status := c.Query("status")
	walletType := c.Query("wallet_type")

	// Get wallets from service
	wallets, total, err := ac.container.WalletService.GetAllWallets(page, limit, status, walletType)
	if err != nil {
		ac.container.Logger.LogError(err, map[string]interface{}{
			"action":      "admin_get_all_wallets",
			"page":        page,
			"limit":       limit,
			"status":      status,
			"wallet_type": walletType,
		})
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "FETCH_FAILED",
			Message: "Failed to fetch wallets",
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Wallets retrieved successfully",
		"data": gin.H{
			"wallets":     wallets,
			"page":        page,
			"limit":       limit,
			"total":       total,
			"status":      status,
			"wallet_type": walletType,
		},
	})
}

// GetWalletDetails retrieves detailed wallet information
func (ac *AdminController) GetWalletDetails(c *gin.Context) {
	walletIDStr := c.Param("id")
	walletID, err := strconv.ParseUint(walletIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_ID",
			Message: "Invalid wallet ID",
			Code:    "400",
		})
		return
	}

	wallet, err := ac.container.WalletService.GetWallet(uint(walletID))
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{
			Error:   "WALLET_NOT_FOUND",
			Message: err.Error(),
			Code:    "404",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    wallet,
	})
}

// AdminUpdateWallet updates wallet information (admin only)
func (ac *AdminController) AdminUpdateWallet(c *gin.Context) {
	walletIDStr := c.Param("id")
	walletID, err := strconv.ParseUint(walletIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_ID",
			Message: "Invalid wallet ID",
			Code:    "400",
		})
		return
	}

	var req WalletUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	// Build updates map
	updates := make(map[string]interface{})
	if req.Status != "" {
		updates["status"] = req.Status
	}
	if req.WalletType != "" {
		updates["wallet_type"] = req.WalletType
	}
	if req.KYCLevel != "" {
		updates["kyc_level"] = req.KYCLevel
	}
	if req.DailyLimit > 0 {
		updates["daily_limit"] = req.DailyLimit
	}
	if req.MonthlyLimit > 0 {
		updates["monthly_limit"] = req.MonthlyLimit
	}

	// Update wallet
	wallet, err := ac.container.WalletService.UpdateWallet(uint(walletID), updates)
	if err != nil {
		ac.container.Logger.LogError(err, map[string]interface{}{
			"action":    "admin_update_wallet",
			"wallet_id": walletID,
			"updates":   updates,
		})
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "UPDATE_FAILED",
			Message: err.Error(),
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Wallet updated successfully",
		"data":    wallet,
	})
}

// FreezeWallet freezes a wallet
func (ac *AdminController) FreezeWallet(c *gin.Context) {
	walletIDStr := c.Param("id")
	walletID, err := strconv.ParseUint(walletIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_ID",
			Message: "Invalid wallet ID",
			Code:    "400",
		})
		return
	}

	var requestBody struct {
		Reason string `json:"reason" binding:"required"`
	}
	if err := c.ShouldBindJSON(&requestBody); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Reason is required",
			Code:    "400",
		})
		return
	}

	// Freeze wallet
	err = ac.container.WalletService.FreezeWallet(uint(walletID), requestBody.Reason)
	if err != nil {
		ac.container.Logger.LogError(err, map[string]interface{}{
			"action":    "admin_freeze_wallet",
			"wallet_id": walletID,
			"reason":    requestBody.Reason,
		})
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "FREEZE_FAILED",
			Message: err.Error(),
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Wallet frozen successfully",
		"data": gin.H{
			"wallet_id": walletID,
			"reason":    requestBody.Reason,
		},
	})
}

// UnfreezeWallet unfreezes a wallet
func (ac *AdminController) UnfreezeWallet(c *gin.Context) {
	walletIDStr := c.Param("id")
	walletID, err := strconv.ParseUint(walletIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_ID",
			Message: "Invalid wallet ID",
			Code:    "400",
		})
		return
	}

	// Unfreeze wallet
	err = ac.container.WalletService.UnfreezeWallet(uint(walletID))
	if err != nil {
		ac.container.Logger.LogError(err, map[string]interface{}{
			"action":    "admin_unfreeze_wallet",
			"wallet_id": walletID,
		})
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "UNFREEZE_FAILED",
			Message: err.Error(),
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Wallet unfrozen successfully",
		"data": gin.H{
			"wallet_id": walletID,
		},
	})
}

// GetWalletAudit retrieves wallet audit trail
func (ac *AdminController) GetWalletAudit(c *gin.Context) {
	walletIDStr := c.Param("id")
	walletID, err := strconv.ParseUint(walletIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_ID",
			Message: "Invalid wallet ID",
			Code:    "400",
		})
		return
	}

	// TODO: Implement GetWalletAudit in WalletService
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Wallet audit not yet implemented",
		"data": gin.H{
			"wallet_id":   walletID,
			"audit_trail": []interface{}{},
		},
	})
}

// Card Management

// GetAllCards retrieves all cards with pagination
func (ac *AdminController) GetAllCards(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	status := c.Query("status")
	cardType := c.Query("card_type")
	search := c.Query("search")

	// Get cards from service
	cards, total, err := ac.container.PayCardService.GetAllCards(page, limit, status, cardType, search)
	if err != nil {
		ac.container.Logger.LogError(err, map[string]interface{}{
			"action":    "admin_get_all_cards",
			"page":      page,
			"limit":     limit,
			"status":    status,
			"card_type": cardType,
			"search":    search,
		})
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "FETCH_FAILED",
			Message: "Failed to fetch cards",
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Cards retrieved successfully",
		"data": gin.H{
			"cards":     cards,
			"page":      page,
			"limit":     limit,
			"total":     total,
			"status":    status,
			"card_type": cardType,
			"search":    search,
		},
	})
}

// GetCardDetails retrieves detailed card information
func (ac *AdminController) GetCardDetails(c *gin.Context) {
	cardID := c.Param("id")
	id, err := strconv.ParseUint(cardID, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_ID",
			Message: "Invalid card ID format",
			Code:    "400",
		})
		return
	}

	// TODO: Implement GetCardDetails in PayCardService
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Card details not yet implemented",
		"data": gin.H{
			"card_id": id,
			"details": gin.H{},
		},
	})
}

// AdminUpdateCard updates card information (admin only)
func (ac *AdminController) AdminUpdateCard(c *gin.Context) {
	cardID := c.Param("id")
	id, err := strconv.ParseUint(cardID, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_ID",
			Message: "Invalid card ID format",
			Code:    "400",
		})
		return
	}

	var req struct {
		Status      string                 `json:"status"`
		Limits      map[string]interface{} `json:"limits"`
		AdminNotes  string                 `json:"admin_notes"`
		ForceUpdate bool                   `json:"force_update"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	// TODO: Implement AdminUpdateCard in PayCardService
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Card update not yet implemented",
		"data": gin.H{
			"card_id": id,
			"updates": req,
		},
	})
}

// ForceBlockCard forcibly blocks a card (admin only)
func (ac *AdminController) ForceBlockCard(c *gin.Context) {
	cardID := c.Param("id")
	id, err := strconv.ParseUint(cardID, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_ID",
			Message: "Invalid card ID format",
			Code:    "400",
		})
		return
	}

	var req struct {
		Reason     string `json:"reason" binding:"required"`
		AdminNotes string `json:"admin_notes"`
		NotifyUser bool   `json:"notify_user"`
		Permanent  bool   `json:"permanent"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	// TODO: Implement ForceBlockCard in PayCardService
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Card force block not yet implemented",
		"data": gin.H{
			"card_id": id,
			"reason":  req.Reason,
			"blocked": true,
		},
	})
}

// GetCardAudit retrieves card audit trail
func (ac *AdminController) GetCardAudit(c *gin.Context) {
	cardID := c.Param("id")
	id, err := strconv.ParseUint(cardID, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_ID",
			Message: "Invalid card ID format",
			Code:    "400",
		})
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "50"))
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")

	// TODO: Implement GetCardAudit in PayCardService
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Card audit not yet implemented",
		"data": gin.H{
			"card_id":    id,
			"audit_logs": []interface{}{},
			"page":       page,
			"limit":      limit,
			"total":      0,
			"start_date": startDate,
			"end_date":   endDate,
		},
	})
}

// Security Management

// GetAllSecurityEvents retrieves all security events
func (ac *AdminController) GetAllSecurityEvents(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "50"))
	eventType := c.Query("event_type")
	severity := c.Query("severity")
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")

	events, total, err := ac.container.SecurityService.GetAllSecurityEvents(page, limit, eventType, severity, startDate, endDate)
	if err != nil {
		ac.container.Logger.LogError(err, map[string]interface{}{
			"action":     "admin_get_security_events",
			"page":       page,
			"limit":      limit,
			"event_type": eventType,
			"severity":   severity,
		})
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "FETCH_FAILED",
			Message: "Failed to fetch security events",
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Security events retrieved successfully",
		"data": gin.H{
			"events":     events,
			"page":       page,
			"limit":      limit,
			"total":      total,
			"event_type": eventType,
			"severity":   severity,
			"start_date": startDate,
			"end_date":   endDate,
		},
	})
}

// GetAllFraudAlerts retrieves all fraud alerts
func (ac *AdminController) GetAllFraudAlerts(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	status := c.Query("status")
	severity := c.Query("severity")
	alertType := c.Query("alert_type")

	alerts, total, err := ac.container.SecurityService.GetAllFraudAlerts(page, limit, status, severity, alertType)
	if err != nil {
		ac.container.Logger.LogError(err, map[string]interface{}{
			"action":     "admin_get_fraud_alerts",
			"page":       page,
			"limit":      limit,
			"status":     status,
			"severity":   severity,
			"alert_type": alertType,
		})
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "FETCH_FAILED",
			Message: "Failed to fetch fraud alerts",
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Fraud alerts retrieved successfully",
		"data": gin.H{
			"alerts":     alerts,
			"page":       page,
			"limit":      limit,
			"total":      total,
			"status":     status,
			"severity":   severity,
			"alert_type": alertType,
		},
	})
}

// UpdateFraudAlert updates a fraud alert
func (ac *AdminController) UpdateFraudAlert(c *gin.Context) {
	alertID := c.Param("id")
	id, err := strconv.ParseUint(alertID, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_ID",
			Message: "Invalid alert ID format",
			Code:    "400",
		})
		return
	}

	var req struct {
		Status     string `json:"status"`
		Resolution string `json:"resolution"`
		AdminNotes string `json:"admin_notes"`
		AssignedTo string `json:"assigned_to"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	// Build updates map with only non-empty fields
	updates := make(map[string]interface{})
	if req.Status != "" {
		updates["status"] = req.Status
	}
	if req.Resolution != "" {
		updates["resolution"] = req.Resolution
	}
	if req.AdminNotes != "" {
		updates["admin_notes"] = req.AdminNotes
	}
	if req.AssignedTo != "" {
		updates["assigned_to"] = req.AssignedTo
	}

	if len(updates) == 0 {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "NO_UPDATES",
			Message: "No valid fields to update",
			Code:    "400",
		})
		return
	}

	alert, err := ac.container.SecurityService.UpdateFraudAlert(uint(id), updates)
	if err != nil {
		if err.Error() == "fraud alert not found" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:   "ALERT_NOT_FOUND",
				Message: "Fraud alert not found",
				Code:    "404",
			})
			return
		}

		ac.container.Logger.LogError(err, map[string]interface{}{
			"action":   "admin_update_fraud_alert",
			"alert_id": id,
			"updates":  updates,
		})
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "UPDATE_FAILED",
			Message: "Failed to update fraud alert",
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Fraud alert updated successfully",
		"data":    alert,
	})
}

// GetRiskProfiles retrieves all risk profiles
func (ac *AdminController) GetRiskProfiles(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	riskLevel := c.Query("risk_level")
	status := c.Query("status")

	// TODO: Implement GetRiskProfiles in SecurityService
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Risk profiles listing not yet implemented",
		"data": gin.H{
			"profiles":   []interface{}{},
			"page":       page,
			"limit":      limit,
			"total":      0,
			"risk_level": riskLevel,
			"status":     status,
		},
	})
}

// UpdateRiskProfile updates a risk profile
func (ac *AdminController) UpdateRiskProfile(c *gin.Context) {
	profileID := c.Param("id")
	id, err := strconv.ParseUint(profileID, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_ID",
			Message: "Invalid profile ID format",
			Code:    "400",
		})
		return
	}

	var req struct {
		RiskLevel  string                 `json:"risk_level"`
		Rules      map[string]interface{} `json:"rules"`
		Thresholds map[string]interface{} `json:"thresholds"`
		AdminNotes string                 `json:"admin_notes"`
		IsActive   bool                   `json:"is_active"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	// TODO: Implement UpdateRiskProfile in SecurityService
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Risk profile update not yet implemented",
		"data": gin.H{
			"profile_id": id,
			"updates":    req,
		},
	})
}

// Service Management

// GetAllServices retrieves all services
func (ac *AdminController) GetAllServices(c *gin.Context) {
	services, err := ac.container.ServiceManager.GetAvailableServices()
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "FETCH_FAILED",
			Message: err.Error(),
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    services,
	})
}

// CreateService creates a new service
func (ac *AdminController) CreateService(c *gin.Context) {
	var req ServiceCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	// TODO: Implement CreateService in ServiceManager
	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "Service creation not yet implemented",
		"data":    req,
	})
}

// UpdateService updates an existing service
func (ac *AdminController) UpdateService(c *gin.Context) {
	serviceIDStr := c.Param("id")
	serviceID, err := strconv.ParseUint(serviceIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_ID",
			Message: "Invalid service ID",
			Code:    "400",
		})
		return
	}

	var req ServiceCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	// TODO: Implement UpdateService in ServiceManager
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Service update not yet implemented",
		"data": gin.H{
			"service_id": serviceID,
			"updates":    req,
		},
	})
}

// DeleteService deletes a service
func (ac *AdminController) DeleteService(c *gin.Context) {
	serviceIDStr := c.Param("id")
	serviceID, err := strconv.ParseUint(serviceIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_ID",
			Message: "Invalid service ID",
			Code:    "400",
		})
		return
	}

	// TODO: Implement DeleteService in ServiceManager
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Service deletion not yet implemented",
		"data": gin.H{
			"service_id": serviceID,
		},
	})
}

// GetAllSubscriptions retrieves all service subscriptions
func (ac *AdminController) GetAllSubscriptions(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	status := c.Query("status")
	serviceID := c.Query("service_id")

	// TODO: Implement GetAllSubscriptions in ServiceManager
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Subscription listing not yet implemented",
		"data": gin.H{
			"subscriptions": []interface{}{},
			"page":          page,
			"limit":         limit,
			"total":         0,
			"status":        status,
			"service_id":    serviceID,
		},
	})
}

// GetServiceBilling retrieves service billing information
func (ac *AdminController) GetServiceBilling(c *gin.Context) {
	period := c.DefaultQuery("period", "monthly")
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")

	// TODO: Implement GetServiceBilling in ServiceManager
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Service billing not yet implemented",
		"data": gin.H{
			"period":     period,
			"start_date": startDate,
			"end_date":   endDate,
			"billing":    []interface{}{},
		},
	})
}

// Analytics and Reporting

// GetRevenueAnalytics retrieves revenue analytics
func (ac *AdminController) GetRevenueAnalytics(c *gin.Context) {
	period := c.DefaultQuery("period", "monthly")
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")

	// TODO: Implement GetRevenueAnalytics in AnalyticsService
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Revenue analytics not yet implemented",
		"data": gin.H{
			"period":     period,
			"start_date": startDate,
			"end_date":   endDate,
			"revenue":    []interface{}{},
		},
	})
}

// GetMerchantAnalytics retrieves merchant analytics
func (ac *AdminController) GetMerchantAnalytics(c *gin.Context) {
	period := c.DefaultQuery("period", "monthly")
	merchantID := c.Query("merchant_id")

	// TODO: Implement GetMerchantAnalytics in AnalyticsService
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Merchant analytics not yet implemented",
		"data": gin.H{
			"period":      period,
			"merchant_id": merchantID,
			"analytics":   []interface{}{},
		},
	})
}

// GetAllReports retrieves all generated reports
func (ac *AdminController) GetAllReports(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	reportType := c.Query("type")

	// TODO: Implement GetAllReports in AnalyticsService
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Report listing not yet implemented",
		"data": gin.H{
			"reports": []interface{}{},
			"page":    page,
			"limit":   limit,
			"total":   0,
			"type":    reportType,
		},
	})
}

// GenerateAdminReport generates a new admin report
func (ac *AdminController) GenerateAdminReport(c *gin.Context) {
	var requestBody struct {
		ReportType string                 `json:"report_type" binding:"required"`
		Period     string                 `json:"period" binding:"required"`
		Filters    map[string]interface{} `json:"filters"`
	}

	if err := c.ShouldBindJSON(&requestBody); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	report, err := ac.container.AnalyticsService.CreateReport(
		requestBody.ReportType,
		requestBody.Period,
		requestBody.Filters,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "REPORT_GENERATION_FAILED",
			Message: err.Error(),
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "Report generated successfully",
		"data":    report,
	})
}
