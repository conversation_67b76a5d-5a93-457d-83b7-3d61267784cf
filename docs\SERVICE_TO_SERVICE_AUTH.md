# Service-to-Service Authentication

The Wallet Platform supports service-to-service authentication using internal API keys, allowing other services in your ecosystem to access wallet functionality without requiring direct user authentication.

## Overview

The service-to-service authentication system provides:

- **Internal API Key Authentication**: Uses `X-Internal-Key` header for service authentication
- **Dual Authentication Support**: Works alongside existing user authentication methods
- **Service Context**: Allows passing user context from calling services
- **Enhanced Security**: Separate rate limiting and access controls for internal services
- **Flexible Integration**: Easy integration with larger platform ecosystems

## Authentication Methods

### 1. Internal API Key Authentication

For service-to-service communication, use the `X-Internal-Key` header:

```bash
curl -X POST "http://localhost:8086/api/v1/internal/wallets" \
  -H "Content-Type: application/json" \
  -H "X-Internal-Key: your-internal-api-key" \
  -H "X-Service-Name: payment-engine" \
  -H "X-User-ID: user123" \
  -d '{
    "phone_number": "+1234567890",
    "wallet_type": "personal"
  }'
```

### 2. Regular User Authentication

For direct user access, use the standard Bearer token:

```bash
curl -X POST "http://localhost:8086/api/v1/wallets" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token" \
  -d '{
    "phone_number": "+1234567890",
    "wallet_type": "personal"
  }'
```

## Configuration

### Environment Variables

Set the internal API key in your environment:

```bash
# Required: Internal API key for service authentication
INTERNAL_API_KEY=your-internal-api-key-for-service-communication
```

### Configuration File

Update `configs/config.yaml`:

```yaml
internal_api:
  enabled: true
  key: "your-internal-api-key-here"  # Should be set via environment variable
  allowed_services:
    - "payment-engine"
    - "user-service"
    - "notification-service"
    - "admin-panel"
  rate_limit:
    enabled: true
    limit: 1000  # Higher limit for internal services
    window: 60
```

## Internal API Endpoints

The following endpoints are available under `/api/v1/internal/` for service-to-service communication:

### Wallet Operations

- `POST /api/v1/internal/wallets` - Create wallet
- `GET /api/v1/internal/wallets/:id` - Get wallet details
- `GET /api/v1/internal/wallets/phone/:phone` - Get wallet by phone number
- `PUT /api/v1/internal/wallets/:id` - Update wallet
- `GET /api/v1/internal/wallets/:id/balance` - Get wallet balance
- `POST /api/v1/internal/wallets/:id/topup` - Top up wallet
- `POST /api/v1/internal/wallets/transfer` - Transfer funds
- `GET /api/v1/internal/wallets/:id/transactions` - Get wallet transactions

### PayCard Operations

- `POST /api/v1/internal/cards` - Create PayCard
- `GET /api/v1/internal/cards/:id` - Get card details
- `GET /api/v1/internal/cards/wallet/:wallet_id` - Get cards by wallet
- `PUT /api/v1/internal/cards/:id` - Update card
- `POST /api/v1/internal/cards/:id/pin` - Update card PIN
- `POST /api/v1/internal/cards/:id/block` - Block card
- `POST /api/v1/internal/cards/:id/unblock` - Unblock card
- `POST /api/v1/internal/cards/:id/transactions` - Process card transaction
- `GET /api/v1/internal/cards/:id/transactions` - Get card transactions
- `POST /api/v1/internal/cards/contactless/pay` - Process contactless payment

### Analytics Operations

- `GET /api/v1/internal/analytics/wallets/:wallet_id` - Get wallet analytics
- `GET /api/v1/internal/analytics/cards/:card_id` - Get card analytics
- `GET /api/v1/internal/analytics/system` - Get system analytics

### Webhook Operations

- `POST /api/v1/internal/webhooks/send` - Send webhook
- `GET /api/v1/internal/webhooks/events` - Get webhook events

## Request Headers

### Required Headers

- `X-Internal-Key`: Your internal API key
- `Content-Type`: application/json (for POST/PUT requests)

### Optional Headers

- `X-Service-Name`: Name of the calling service (for logging and monitoring)
- `X-User-ID`: User ID from the calling service (for user context)
- `X-Wallet-ID`: Wallet ID from the calling service (for wallet context)
- `X-Request-ID`: Request ID for tracing (auto-generated if not provided)

## Response Format

All responses follow the standard format:

### Success Response

```json
{
  "success": true,
  "data": {
    // Response data
  },
  "message": "Operation completed successfully",
  "timestamp": "2024-01-01T12:00:00Z",
  "request_id": "req_123456789"
}
```

### Error Response

```json
{
  "success": false,
  "error": {
    "code": "INVALID_INTERNAL_KEY",
    "message": "Invalid internal API key",
    "details": {}
  },
  "timestamp": "2024-01-01T12:00:00Z",
  "request_id": "req_123456789"
}
```

## Security Considerations

### API Key Management

1. **Environment Variables**: Store API keys in environment variables, never in code
2. **Key Rotation**: Regularly rotate internal API keys
3. **Access Control**: Limit which services can access which endpoints
4. **Monitoring**: Monitor internal API usage for anomalies

### Rate Limiting

Internal services have higher rate limits but are still subject to rate limiting:

- **Default Limit**: 1000 requests per minute
- **Burst Handling**: Temporary spikes are allowed
- **Service-Specific Limits**: Can be configured per service

### Logging and Monitoring

All internal API calls are logged with:

- Service name (from `X-Service-Name` header)
- Request details and response status
- User context (if provided)
- Performance metrics

## Integration Examples

### Payment Engine Integration

```go
// Example: Creating a wallet from payment engine
func CreateWalletForUser(userID, phoneNumber string) (*Wallet, error) {
    client := &http.Client{Timeout: 30 * time.Second}
    
    payload := map[string]interface{}{
        "phone_number": phoneNumber,
        "wallet_type": "personal",
    }
    
    jsonData, _ := json.Marshal(payload)
    
    req, _ := http.NewRequest("POST", "http://wallet-platform:8086/api/v1/internal/wallets", bytes.NewBuffer(jsonData))
    req.Header.Set("Content-Type", "application/json")
    req.Header.Set("X-Internal-Key", os.Getenv("INTERNAL_API_KEY"))
    req.Header.Set("X-Service-Name", "payment-engine")
    req.Header.Set("X-User-ID", userID)
    
    resp, err := client.Do(req)
    if err != nil {
        return nil, err
    }
    defer resp.Body.Close()
    
    // Handle response...
}
```

### User Service Integration

```javascript
// Example: Getting wallet balance from user service
async function getWalletBalance(walletId) {
    const response = await fetch(`http://wallet-platform:8086/api/v1/internal/wallets/${walletId}/balance`, {
        method: 'GET',
        headers: {
            'X-Internal-Key': process.env.INTERNAL_API_KEY,
            'X-Service-Name': 'user-service',
            'Content-Type': 'application/json'
        }
    });
    
    if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return await response.json();
}
```

## Error Codes

Common error codes for internal API:

- `INTERNAL_KEY_NOT_CONFIGURED`: Internal API key not set in environment
- `INVALID_INTERNAL_KEY`: Provided internal API key is invalid
- `MISSING_AUTHENTICATION`: No authentication method provided
- `RATE_LIMIT_EXCEEDED`: Rate limit exceeded for internal service
- `SERVICE_NOT_ALLOWED`: Service not in allowed services list

## Best Practices

1. **Use Specific Endpoints**: Use internal endpoints (`/internal/`) for service-to-service communication
2. **Pass User Context**: Always include `X-User-ID` when available for proper auditing
3. **Handle Errors Gracefully**: Implement proper error handling and retry logic
4. **Monitor Usage**: Track internal API usage for capacity planning
5. **Secure Communication**: Use HTTPS in production environments
6. **Timeout Handling**: Set appropriate timeouts for internal API calls
7. **Circuit Breaker**: Implement circuit breaker pattern for resilience

## Testing

### Unit Tests

Test internal authentication middleware:

```bash
go test ./internal/middleware -v -run TestInternalAuth
```

### Integration Tests

Test internal API endpoints:

```bash
go test ./internal/controllers -v -run TestInternalAPI
```

### Load Testing

Test internal API performance:

```bash
# Using Apache Bench
ab -n 1000 -c 10 -H "X-Internal-Key: test-key" http://localhost:8086/api/v1/internal/wallets/123/balance
```
