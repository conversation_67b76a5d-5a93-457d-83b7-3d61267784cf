# Internal API Endpoints Documentation

## Overview

This document provides comprehensive documentation for all internal API endpoints available through the WalletService JavaScript client. These endpoints are designed for service-to-service communication and require proper authentication headers to be injected server-side.

## Base URL

```
Production: https://your-api.com/api/internal
Development: http://localhost:8086/api/internal
```

## Authentication

All internal API endpoints require server-side authentication header injection:

### Headers (Injected Server-Side)
```
Authorization: Bearer <jwt_token>
X-Internal-Key: <internal_api_key>
X-Service-Name: frontend-service
X-Request-ID: <auto_generated_uuid>
Content-Type: application/json
```

## Response Format

All API responses follow a standardized format:

### Success Response
```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": { ... },
  "timestamp": "2024-01-01T00:00:00Z",
  "request_id": "uuid"
}
```

### Error Response
```json
{
  "error": "ERROR_CODE",
  "message": "Human readable error message",
  "code": "400",
  "timestamp": "2024-01-01T00:00:00Z",
  "request_id": "uuid"
}
```

### Paginated Response
```json
{
  "success": true,
  "data": {
    "items": [...],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "total_pages": 5,
      "has_next": true,
      "has_prev": false
    }
  }
}
```

## Wallet Operations

### Create Wallet
```javascript
WalletService.createWallet(data)
```

**Endpoint:** `POST /internal/wallets`

**Request Body:**
```json
{
  "phone_number": "+1234567890",
  "wallet_type": "personal",
  "is_verified": true,
  "email": "<EMAIL>"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Wallet created successfully",
  "data": {
    "id": "wallet_123",
    "phone_number": "+1234567890",
    "wallet_type": "personal",
    "balance": 0.00,
    "currency": "SZL",
    "status": "active",
    "is_verified": true,
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### Get Wallet Details
```javascript
WalletService.getWallet(walletId)
```

**Endpoint:** `GET /internal/wallets/{id}`

**Parameters:**
- `walletId` (string): Wallet ID

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "wallet_123",
    "phone_number": "+1234567890",
    "wallet_type": "personal",
    "balance": 1000.50,
    "currency": "SZL",
    "status": "active",
    "daily_limit": 5000.00,
    "monthly_limit": 50000.00,
    "settings": {
      "notifications": true,
      "auto_topup": false
    },
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

### Get Wallet by Phone Number
```javascript
WalletService.getWalletByPhone(phoneNumber)
```

**Endpoint:** `GET /internal/wallets/phone/{phone}`

**Parameters:**
- `phoneNumber` (string): Phone number (URL encoded)

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "wallet_123",
    "phone_number": "+1234567890",
    "wallet_type": "personal",
    "balance": 1000.50,
    "currency": "SZL",
    "status": "active"
  }
}
```

### Update Wallet
```javascript
WalletService.updateWallet(walletId, data)
```

**Endpoint:** `PUT /internal/wallets/{id}`

**Parameters:**
- `walletId` (string): Wallet ID

**Request Body:**
```json
{
  "daily_limit": 10000.00,
  "monthly_limit": 100000.00,
  "settings": {
    "notifications": false,
    "auto_topup": true
  },
  "status": "active"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Wallet updated successfully",
  "data": {
    "id": "wallet_123",
    "daily_limit": 10000.00,
    "monthly_limit": 100000.00,
    "settings": {
      "notifications": false,
      "auto_topup": true
    },
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

### Get Wallet Balance
```javascript
WalletService.getBalance(walletId)
```

**Endpoint:** `GET /internal/wallets/{id}/balance`

**Parameters:**
- `walletId` (string): Wallet ID

**Response:**
```json
{
  "success": true,
  "data": {
    "wallet_id": "wallet_123",
    "balance": 1000.50,
    "currency": "SZL",
    "available_balance": 950.50,
    "pending_balance": 50.00,
    "reserved_balance": 0.00,
    "last_updated": "2024-01-01T12:00:00Z"
  }
}
```

### Top Up Wallet
```javascript
WalletService.topUp(walletId, data)
```

**Endpoint:** `POST /internal/wallets/{id}/topup`

**Parameters:**
- `walletId` (string): Wallet ID

**Request Body:**
```json
{
  "amount": 100.00,
  "topup_phone": "78001122",
  "reference": "TOPUP1704067200000",
  "description": "Mobile money deposit",
  "external_reference": "MM1704067200000"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Wallet topped up successfully",
  "data": {
    "transaction_id": "txn_123",
    "wallet_id": "wallet_123",
    "amount": 100.00,
    "new_balance": 1100.50,
    "reference": "TOPUP1704067200000",
    "status": "completed",
    "created_at": "2024-01-01T12:00:00Z"
  }
}
```

### Withdraw Funds
```javascript
WalletService.withdrawFunds(walletId, data)
```

**Endpoint:** `POST /internal/wallets/{id}/withdraw`

**Parameters:**
- `walletId` (string): Wallet ID

**Request Body:**
```json
{
  "amount": 50.00,
  "withdrawal_method": "bank_transfer",
  "destination": "bank_account_123",
  "reference": "WITHDRAW123",
  "description": "Cash withdrawal"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Withdrawal initiated successfully",
  "data": {
    "transaction_id": "txn_124",
    "wallet_id": "wallet_123",
    "amount": 50.00,
    "new_balance": 1050.50,
    "withdrawal_method": "bank_transfer",
    "status": "pending",
    "estimated_completion": "2024-01-01T14:00:00Z",
    "created_at": "2024-01-01T12:00:00Z"
  }
}
```

### Transfer Funds
```javascript
WalletService.transfer(data)
```

**Endpoint:** `POST /internal/wallets/transfer`

**Request Body:**
```json
{
  "from_wallet_id": "wallet_123",
  "to_wallet_id": "wallet_456",
  "to_phone_number": "+**********",
  "amount": 25.00,
  "reference": "TRANSFER123",
  "description": "Payment to friend"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Transfer completed successfully",
  "data": {
    "transaction_id": "txn_125",
    "from_wallet_id": "wallet_123",
    "to_wallet_id": "wallet_456",
    "amount": 25.00,
    "fee": 1.00,
    "total_amount": 26.00,
    "reference": "TRANSFER123",
    "status": "completed",
    "created_at": "2024-01-01T12:00:00Z"
  }
}
```

### Get Transaction History
```javascript
WalletService.getTransactionHistory(walletId, params)
```

**Endpoint:** `GET /internal/wallets/{id}/transactions`

**Parameters:**
- `walletId` (string): Wallet ID
- `params` (object): Query parameters

**Query Parameters:**
```javascript
{
  page: 1,
  limit: 50,
  start_date: "2024-01-01",
  end_date: "2024-01-31",
  transaction_type: "all" // "all", "credit", "debit", "transfer"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "txn_123",
        "wallet_id": "wallet_123",
        "type": "credit",
        "amount": 100.00,
        "balance_after": 1100.50,
        "description": "Mobile money deposit",
        "reference": "TOPUP1704067200000",
        "status": "completed",
        "created_at": "2024-01-01T12:00:00Z"
      },
      {
        "id": "txn_124",
        "wallet_id": "wallet_123",
        "type": "debit",
        "amount": 25.00,
        "balance_after": 1075.50,
        "description": "Payment to friend",
        "reference": "TRANSFER123",
        "status": "completed",
        "created_at": "2024-01-01T11:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 50,
      "total": 2,
      "total_pages": 1,
      "has_next": false,
      "has_prev": false
    }
  }
}
```

### Batch Wallet Lookup
```javascript
WalletService.batchWalletLookup(phoneNumbers)
```

**Endpoint:** `POST /internal/wallets/batch-lookup`

**Request Body:**
```json
{
  "phone_numbers": [
    "+1234567890",
    "+**********",
    "+1122334455"
  ]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "results": [
      {
        "phone_number": "+1234567890",
        "found": true,
        "wallet": {
          "id": "wallet_123",
          "phone_number": "+1234567890",
          "status": "active",
          "wallet_type": "personal"
        }
      },
      {
        "phone_number": "+**********",
        "found": true,
        "wallet": {
          "id": "wallet_456",
          "phone_number": "+**********",
          "status": "active",
          "wallet_type": "business"
        }
      },
      {
        "phone_number": "+1122334455",
        "found": false,
        "wallet": null
      }
    ],
    "summary": {
      "total_requested": 3,
      "found": 2,
      "not_found": 1
    }
  }
}
```

## PayCard Operations

### Create PayCard
```javascript
WalletService.createPayCard(data)
```

**Endpoint:** `POST /internal/cards`

**Request Body:**
```json
{
  "wallet_id": "wallet_123",
  "card_type": "virtual",
  "spending_limit": 5000.00,
  "currency": "SZL",
  "card_holder_name": "John Doe",
  "daily_spending_limit": 1000.00,
  "monthly_spending_limit": 10000.00,
  "security_pin": "1234",
  "merchant_restrictions": ["online", "retail"],
  "geographic_restrictions": ["SZ", "ZA"]
}
```

**Response:**
```json
{
  "success": true,
  "message": "PayCard created successfully",
  "data": {
    "id": "card_123",
    "wallet_id": "wallet_123",
    "card_number": "****-****-****-1234",
    "card_type": "virtual",
    "card_holder_name": "John Doe",
    "status": "active",
    "spending_limit": 5000.00,
    "daily_spending_limit": 1000.00,
    "monthly_spending_limit": 10000.00,
    "currency": "SZL",
    "expiry_date": "12/27",
    "created_at": "2024-01-01T12:00:00Z"
  }
}
```

### Get PayCard Details
```javascript
WalletService.getPayCardDetails(cardId)
```

**Endpoint:** `GET /internal/cards/{id}`

**Parameters:**
- `cardId` (string): Card ID

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "card_123",
    "wallet_id": "wallet_123",
    "card_number": "****-****-****-1234",
    "card_type": "virtual",
    "card_holder_name": "John Doe",
    "status": "active",
    "spending_limit": 5000.00,
    "daily_spending_limit": 1000.00,
    "monthly_spending_limit": 10000.00,
    "current_daily_spent": 150.00,
    "current_monthly_spent": 1250.00,
    "currency": "SZL",
    "expiry_date": "12/27",
    "merchant_restrictions": ["online", "retail"],
    "geographic_restrictions": ["SZ", "ZA"],
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

### Get Cards by Wallet
```javascript
WalletService.getWalletPayCards(walletId)
```

**Endpoint:** `GET /internal/cards/wallet/{wallet_id}`

**Parameters:**
- `walletId` (string): Wallet ID

**Response:**
```json
{
  "success": true,
  "data": {
    "wallet_id": "wallet_123",
    "cards": [
      {
        "id": "card_123",
        "card_number": "****-****-****-1234",
        "card_type": "virtual",
        "card_holder_name": "John Doe",
        "status": "active",
        "spending_limit": 5000.00,
        "currency": "SZL",
        "expiry_date": "12/27",
        "created_at": "2024-01-01T12:00:00Z"
      },
      {
        "id": "card_124",
        "card_number": "****-****-****-5678",
        "card_type": "physical",
        "card_holder_name": "John Doe",
        "status": "active",
        "spending_limit": 10000.00,
        "currency": "SZL",
        "expiry_date": "12/27",
        "created_at": "2024-01-02T12:00:00Z"
      }
    ],
    "total_cards": 2
  }
}
```

### Update PayCard
```javascript
WalletService.updatePayCard(cardId, data)
```

**Endpoint:** `PUT /internal/cards/{id}`

**Parameters:**
- `cardId` (string): Card ID

**Request Body:**
```json
{
  "card_holder_name": "John Smith",
  "spending_limit": 7500.00,
  "daily_spending_limit": 1500.00,
  "monthly_spending_limit": 15000.00,
  "status": "active",
  "merchant_restrictions": ["online", "retail", "atm"],
  "geographic_restrictions": ["SZ", "ZA", "BW"]
}
```

**Response:**
```json
{
  "success": true,
  "message": "PayCard updated successfully",
  "data": {
    "id": "card_123",
    "card_holder_name": "John Smith",
    "spending_limit": 7500.00,
    "daily_spending_limit": 1500.00,
    "monthly_spending_limit": 15000.00,
    "status": "active",
    "merchant_restrictions": ["online", "retail", "atm"],
    "geographic_restrictions": ["SZ", "ZA", "BW"],
    "updated_at": "2024-01-01T14:00:00Z"
  }
}
```

### Update PayCard PIN
```javascript
WalletService.updatePayCardPin(cardId, data)
```

**Endpoint:** `POST /internal/cards/{id}/pin`

**Parameters:**
- `cardId` (string): Card ID

**Request Body:**
```json
{
  "current_pin": "1234",
  "new_pin": "5678"
}
```

**Response:**
```json
{
  "success": true,
  "message": "PIN updated successfully",
  "data": {
    "card_id": "card_123",
    "pin_updated": true,
    "updated_at": "2024-01-01T14:00:00Z"
  }
}
```

### Block Card
```javascript
WalletService.blockCard(cardId, data)
```

**Endpoint:** `POST /internal/cards/{id}/block`

**Parameters:**
- `cardId` (string): Card ID

**Request Body:**
```json
{
  "reason": "lost",
  "notes": "Card reported lost by customer"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Card blocked successfully",
  "data": {
    "card_id": "card_123",
    "status": "blocked",
    "reason": "lost",
    "blocked_at": "2024-01-01T14:00:00Z"
  }
}
```

### Unblock Card
```javascript
WalletService.unblockCard(cardId, data)
```

**Endpoint:** `POST /internal/cards/{id}/unblock`

**Parameters:**
- `cardId` (string): Card ID

**Request Body:**
```json
{
  "reason": "found",
  "notes": "Card found by customer"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Card unblocked successfully",
  "data": {
    "card_id": "card_123",
    "status": "active",
    "reason": "found",
    "unblocked_at": "2024-01-01T15:00:00Z"
  }
}
```

### Process Card Transaction
```javascript
WalletService.processCardTransaction(cardId, data)
```

**Endpoint:** `POST /internal/cards/{id}/transactions`

**Parameters:**
- `cardId` (string): Card ID

**Request Body:**
```json
{
  "amount": 50.00,
  "merchant_name": "Coffee Shop",
  "merchant_category": "food_beverage",
  "merchant_location": "Mbabane, SZ",
  "reference": "TXN123456",
  "device_info": {
    "terminal_id": "TERM001",
    "device_type": "pos"
  },
  "location_info": {
    "latitude": -26.3054,
    "longitude": 31.1367
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Transaction processed successfully",
  "data": {
    "transaction_id": "txn_126",
    "card_id": "card_123",
    "amount": 50.00,
    "merchant_name": "Coffee Shop",
    "merchant_category": "food_beverage",
    "reference": "TXN123456",
    "status": "approved",
    "authorization_code": "AUTH123",
    "remaining_balance": 4950.00,
    "created_at": "2024-01-01T15:30:00Z"
  }
}
```

### Get Card Transactions
```javascript
WalletService.getPayCardTransactions(cardId, params)
```

**Endpoint:** `GET /internal/cards/{id}/transactions`

**Parameters:**
- `cardId` (string): Card ID
- `params` (object): Query parameters

**Query Parameters:**
```javascript
{
  page: 1,
  limit: 20
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "card_id": "card_123",
    "transactions": [
      {
        "id": "txn_126",
        "amount": 50.00,
        "merchant_name": "Coffee Shop",
        "merchant_category": "food_beverage",
        "status": "approved",
        "authorization_code": "AUTH123",
        "created_at": "2024-01-01T15:30:00Z"
      },
      {
        "id": "txn_125",
        "amount": 25.00,
        "merchant_name": "Gas Station",
        "merchant_category": "fuel",
        "status": "approved",
        "authorization_code": "AUTH122",
        "created_at": "2024-01-01T14:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 2,
      "total_pages": 1,
      "has_next": false,
      "has_prev": false
    }
  }
}
```

### Process Contactless Payment
```javascript
WalletService.processContactlessPayment(data)
```

**Endpoint:** `POST /internal/cards/contactless/pay`

**Request Body:**
```json
{
  "card_id": "card_123",
  "amount": 30.00,
  "merchant_id": "merchant_456",
  "terminal_id": "TERM002"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Contactless payment processed successfully",
  "data": {
    "transaction_id": "txn_127",
    "card_id": "card_123",
    "amount": 30.00,
    "merchant_id": "merchant_456",
    "terminal_id": "TERM002",
    "payment_method": "contactless",
    "status": "approved",
    "authorization_code": "AUTH124",
    "created_at": "2024-01-01T16:00:00Z"
  }
}
```

## Analytics Operations

### Get Wallet Analytics
```javascript
WalletService.getWalletAnalytics(walletId, params)
```

**Endpoint:** `GET /internal/analytics/wallets/{wallet_id}`

**Parameters:**
- `walletId` (string): Wallet ID
- `params` (object): Query parameters

**Query Parameters:**
```javascript
{
  period: "monthly", // "daily", "weekly", "monthly", "yearly"
  start_date: "2024-01-01",
  end_date: "2024-01-31"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "wallet_id": "wallet_123",
    "period": "monthly",
    "date_range": {
      "start_date": "2024-01-01",
      "end_date": "2024-01-31"
    },
    "summary": {
      "total_transactions": 45,
      "total_volume": 2500.00,
      "total_fees": 25.00,
      "average_transaction": 55.56,
      "balance_start": 1000.00,
      "balance_end": 1475.00
    },
    "transaction_breakdown": {
      "credits": {
        "count": 15,
        "volume": 1500.00
      },
      "debits": {
        "count": 30,
        "volume": 1000.00
      }
    },
    "daily_activity": [
      {
        "date": "2024-01-01",
        "transactions": 3,
        "volume": 150.00
      }
    ]
  }
}
```

### Get Card Analytics
```javascript
WalletService.getPayCardAnalytics(cardId, params)
```

**Endpoint:** `GET /internal/analytics/cards/{card_id}`

**Parameters:**
- `cardId` (string): Card ID
- `params` (object): Query parameters

**Query Parameters:**
```javascript
{
  period: "monthly", // "daily", "weekly", "monthly", "yearly"
  start_date: "2024-01-01",
  end_date: "2024-01-31"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "card_id": "card_123",
    "period": "monthly",
    "date_range": {
      "start_date": "2024-01-01",
      "end_date": "2024-01-31"
    },
    "summary": {
      "total_transactions": 25,
      "total_spent": 1250.00,
      "average_transaction": 50.00,
      "declined_transactions": 2,
      "spending_limit_utilization": 25.0
    },
    "merchant_breakdown": [
      {
        "category": "food_beverage",
        "transactions": 10,
        "amount": 500.00
      },
      {
        "category": "retail",
        "transactions": 8,
        "amount": 400.00
      }
    ],
    "daily_spending": [
      {
        "date": "2024-01-01",
        "transactions": 2,
        "amount": 75.00
      }
    ]
  }
}
```

### Get System Analytics
```javascript
WalletService.getSystemAnalytics(params)
```

**Endpoint:** `GET /internal/analytics/system`

**Query Parameters:**
```javascript
{
  period: "monthly", // "daily", "weekly", "monthly", "yearly"
  start_date: "2024-01-01",
  end_date: "2024-01-31"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "period": "monthly",
    "date_range": {
      "start_date": "2024-01-01",
      "end_date": "2024-01-31"
    },
    "system_summary": {
      "total_wallets": 1250,
      "active_wallets": 1100,
      "total_cards": 2500,
      "active_cards": 2200,
      "total_transaction_volume": 125000.00,
      "total_fees_collected": 1250.00
    },
    "growth_metrics": {
      "new_wallets": 50,
      "new_cards": 75,
      "wallet_growth_rate": 4.17,
      "card_growth_rate": 3.13
    },
    "performance_metrics": {
      "average_response_time": 150,
      "success_rate": 99.5,
      "uptime": 99.9
    }
  }
}
```

## Webhook Operations

### Send Webhook
```javascript
WalletService.sendWebhook(data)
```

**Endpoint:** `POST /internal/webhooks/send`

**Request Body:**
```json
{
  "event_type": "wallet.transaction.completed",
  "webhook_url": "https://your-app.com/webhooks/wallet",
  "payload": {
    "transaction_id": "txn_123",
    "wallet_id": "wallet_123",
    "amount": 100.00,
    "status": "completed"
  },
  "retry_count": 3,
  "timeout": 30
}
```

**Response:**
```json
{
  "success": true,
  "message": "Webhook sent successfully",
  "data": {
    "webhook_id": "webhook_123",
    "event_type": "wallet.transaction.completed",
    "status": "sent",
    "response_code": 200,
    "sent_at": "2024-01-01T16:30:00Z"
  }
}
```

### Get Webhook Events
```javascript
WalletService.getWebhookEvents(params)
```

**Endpoint:** `GET /internal/webhooks/events`

**Query Parameters:**
```javascript
{
  page: 1,
  limit: 50,
  status: "sent" // "sent", "failed", "pending", "retrying"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "events": [
      {
        "id": "webhook_123",
        "event_type": "wallet.transaction.completed",
        "webhook_url": "https://your-app.com/webhooks/wallet",
        "status": "sent",
        "response_code": 200,
        "retry_count": 0,
        "sent_at": "2024-01-01T16:30:00Z"
      },
      {
        "id": "webhook_124",
        "event_type": "card.transaction.declined",
        "webhook_url": "https://your-app.com/webhooks/card",
        "status": "failed",
        "response_code": 500,
        "retry_count": 2,
        "last_attempt": "2024-01-01T16:25:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 50,
      "total": 2,
      "total_pages": 1,
      "has_next": false,
      "has_prev": false
    }
  }
}
```

## Platform Operations

### Get Platform Wallet
```javascript
WalletService.getPlatformWallet()
```

**Endpoint:** `GET /internal/platform/wallet`

**Response:**
```json
{
  "success": true,
  "data": {
    "platform_wallet_id": "platform_wallet_001",
    "balance": 50000.00,
    "currency": "SZL",
    "total_fees_collected": 2500.00,
    "total_transactions_processed": 10000,
    "last_settlement": "2024-01-01T00:00:00Z",
    "next_settlement": "2024-02-01T00:00:00Z"
  }
}
```

### Collect Platform Fee
```javascript
WalletService.collectPlatformFee(data)
```

**Endpoint:** `POST /internal/platform/collect-fee`

**Request Body:**
```json
{
  "transaction_id": "txn_123",
  "fee_amount": 2.50,
  "fee_type": "transaction_fee",
  "source_wallet_id": "wallet_123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Platform fee collected successfully",
  "data": {
    "fee_collection_id": "fee_123",
    "transaction_id": "txn_123",
    "fee_amount": 2.50,
    "fee_type": "transaction_fee",
    "platform_wallet_balance": 50002.50,
    "collected_at": "2024-01-01T17:00:00Z"
  }
}
```

## Subscription Operations

### Create Subscription
```javascript
WalletService.createSubscription(data)
```

**Endpoint:** `POST /internal/subscriptions`

**Request Body:**
```json
{
  "wallet_id": "wallet_123",
  "service_id": "service_456",
  "plan_type": "premium",
  "amount": 29.99
}
```

**Response:**
```json
{
  "success": true,
  "message": "Subscription created successfully",
  "data": {
    "subscription_id": "sub_123",
    "wallet_id": "wallet_123",
    "service_id": "service_456",
    "plan_type": "premium",
    "amount": 29.99,
    "status": "active",
    "billing_cycle": "monthly",
    "next_billing_date": "2024-02-01T00:00:00Z",
    "created_at": "2024-01-01T17:00:00Z"
  }
}
```

### Get Wallet Subscriptions
```javascript
WalletService.getWalletSubscriptions(walletId)
```

**Endpoint:** `GET /internal/subscriptions/wallet/{wallet_id}`

**Parameters:**
- `walletId` (string): Wallet ID

**Response:**
```json
{
  "success": true,
  "data": {
    "wallet_id": "wallet_123",
    "subscriptions": [
      {
        "subscription_id": "sub_123",
        "service_id": "service_456",
        "service_name": "Premium Analytics",
        "plan_type": "premium",
        "amount": 29.99,
        "status": "active",
        "billing_cycle": "monthly",
        "next_billing_date": "2024-02-01T00:00:00Z",
        "created_at": "2024-01-01T17:00:00Z"
      },
      {
        "subscription_id": "sub_124",
        "service_id": "service_789",
        "service_name": "SMS Notifications",
        "plan_type": "basic",
        "amount": 9.99,
        "status": "active",
        "billing_cycle": "monthly",
        "next_billing_date": "2024-02-01T00:00:00Z",
        "created_at": "2024-01-01T16:00:00Z"
      }
    ],
    "total_subscriptions": 2,
    "total_monthly_cost": 39.98
  }
}
```

### Cancel Subscription
```javascript
WalletService.cancelSubscription(subscriptionId, data)
```

**Endpoint:** `POST /internal/subscriptions/{id}/cancel`

**Parameters:**
- `subscriptionId` (string): Subscription ID

**Request Body:**
```json
{
  "reason": "user_request",
  "notes": "Customer requested cancellation",
  "effective_date": "2024-01-31T23:59:59Z"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Subscription cancelled successfully",
  "data": {
    "subscription_id": "sub_123",
    "status": "cancelled",
    "cancellation_reason": "user_request",
    "cancelled_at": "2024-01-01T17:30:00Z",
    "effective_date": "2024-01-31T23:59:59Z",
    "refund_amount": 0.00
  }
}
```

## System Operations

### Health Check
```javascript
WalletService.healthCheck()
```

**Endpoint:** `GET /internal/health`

**Response:**
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "service": "wallet-platform-internal",
    "version": "4.0.0",
    "timestamp": "2024-01-01T18:00:00Z",
    "uptime": 86400,
    "dependencies": {
      "database": "healthy",
      "payment_engine": "healthy",
      "cache": "healthy"
    }
  }
}
```

### Validate API Key
```javascript
WalletService.validateAPIKey()
```

**Endpoint:** `POST /internal/auth/validate-key`

**Response:**
```json
{
  "success": true,
  "message": "Internal API key is valid",
  "service": "frontend-service",
  "request_id": "req_123456789",
  "timestamp": "2024-01-01T18:00:00Z"
}
```

### Get Service Info
```javascript
WalletService.getServiceInfo()
```

**Endpoint:** `GET /internal/service/info`

**Response:**
```json
{
  "success": true,
  "service": "locked-payments-service",
  "version": "4.0.0",
  "endpoints": {
    "wallets": 10,
    "cards": 11,
    "analytics": 3,
    "webhooks": 2,
    "platform": 2,
    "subscriptions": 3,
    "system": 3,
    "total": 34
  },
  "capabilities": [
    "wallet_management",
    "paycard_operations",
    "analytics_reporting",
    "webhook_handling",
    "platform_operations",
    "subscription_management"
  ],
  "authentication": "internal_api_key",
  "request_id": "req_123456789",
  "timestamp": "2024-01-01T18:00:00Z"
}
```

## Legacy Compatibility Methods

### Legacy Get Balance
```javascript
WalletService.getBalanceLegacy(data)
```

**Parameters:**
```javascript
{
  user_id: "user_123",
  phone_number: "+1234567890"
}
```

**Note:** This method performs a phone number lookup to find the wallet, then retrieves the balance. Use the direct `getBalance(walletId)` method for better performance.

### Legacy Top Up
```javascript
WalletService.topUpLegacy(data)
```

**Parameters:**
```javascript
{
  user_id: "user_123",
  phone_number: "+1234567890",
  amount: 100.00,
  reference: "TOPUP123",
  description: "Mobile money deposit",
  payment_method: "mobile_money",
  external_reference: "MM123"
}
```

**Note:** This method performs a phone number lookup to find the wallet, then processes the top-up. Use the direct `topUp(walletId, data)` method for better performance.

### Legacy Transfer
```javascript
WalletService.transferLegacy(data)
```

**Parameters:**
```javascript
{
  user_id: "user_123",
  from_phone_number: "+1234567890",
  to_phone_number: "+**********",
  amount: 25.00,
  reference: "TRANSFER123",
  description: "Payment to friend"
}
```

**Note:** This method performs a phone number lookup to find the source wallet, then processes the transfer. Use the direct `transfer(data)` method with wallet IDs for better performance.

### Legacy Transaction History
```javascript
WalletService.getTransactionHistoryLegacy(data)
```

**Parameters:**
```javascript
{
  user_id: "user_123",
  phone_number: "+1234567890",
  limit: 50,
  offset: 0,
  start_date: "2024-01-01",
  end_date: "2024-01-31",
  transaction_type: "all"
}
```

**Note:** This method performs a phone number lookup to find the wallet, then retrieves transaction history. Use the direct `getTransactionHistory(walletId, params)` method for better performance.

## Error Codes

### Common Error Responses

#### 400 Bad Request
```json
{
  "error": "INVALID_REQUEST",
  "message": "Invalid request parameters",
  "code": "400",
  "timestamp": "2024-01-01T18:00:00Z",
  "request_id": "req_123456789"
}
```

#### 401 Unauthorized
```json
{
  "error": "UNAUTHORIZED",
  "message": "Invalid or missing authentication credentials",
  "code": "401",
  "timestamp": "2024-01-01T18:00:00Z",
  "request_id": "req_123456789"
}
```

#### 404 Not Found
```json
{
  "error": "RESOURCE_NOT_FOUND",
  "message": "The requested resource was not found",
  "code": "404",
  "timestamp": "2024-01-01T18:00:00Z",
  "request_id": "req_123456789"
}
```

#### 500 Internal Server Error
```json
{
  "error": "INTERNAL_SERVER_ERROR",
  "message": "An internal server error occurred",
  "code": "500",
  "timestamp": "2024-01-01T18:00:00Z",
  "request_id": "req_123456789"
}
```

## Rate Limiting

All internal API endpoints are subject to rate limiting:

- **Rate Limit**: 1000 requests per minute per service
- **Burst Limit**: 100 requests per 10 seconds
- **Headers**: Rate limit information is returned in response headers:
  - `X-RateLimit-Limit`: Total requests allowed per minute
  - `X-RateLimit-Remaining`: Remaining requests in current window
  - `X-RateLimit-Reset`: Time when the rate limit resets

## Request Tracing

All requests include tracing headers for debugging:

- **`X-Request-ID`**: Unique identifier for each request (auto-generated)
- **`X-Service-Name`**: Identifying the calling service
- **Response Headers**: Include the same `X-Request-ID` for correlation

## Security Considerations

1. **Authentication**: All endpoints require server-side internal API key injection
2. **Authorization**: User context is forwarded when available
3. **Data Protection**: Sensitive data is masked in logs
4. **Request Validation**: All inputs are validated and sanitized
5. **Rate Limiting**: Prevents abuse and ensures fair usage
6. **Audit Logging**: All operations are logged for security auditing
