package services

import (
	"sort"
	"time"

	"wallet-platform/internal/models"
)

// Helper methods for analytics calculations

// User Behavior Analytics Helpers

func (e *EnhancedAnalyticsService) calculateAverageFrequency(patterns []UserBehaviorPattern) float64 {
	if len(patterns) == 0 {
		return 0.0
	}

	total := 0.0
	for _, pattern := range patterns {
		total += float64(len(pattern.TypicalTransactionHours)) // Use length of typical hours as frequency
	}
	return total / float64(len(patterns))
}

func (e *EnhancedAnalyticsService) calculatePeakUsageHours(patterns []UserBehaviorPattern) []int {
	hourCounts := make(map[int]int)

	for _, pattern := range patterns {
		// Use typical transaction hours from pattern
		for _, hour := range pattern.TypicalTransactionHours {
			hourCounts[hour]++
		}
	}

	// Find top 3 peak hours
	type hourCount struct {
		hour  int
		count int
	}

	var hours []hourCount
	for h, c := range hourCounts {
		hours = append(hours, hourCount{hour: h, count: c})
	}

	sort.Slice(hours, func(i, j int) bool {
		return hours[i].count > hours[j].count
	})

	var peakHours []int
	for i := 0; i < 3 && i < len(hours); i++ {
		peakHours = append(peakHours, hours[i].hour)
	}

	return peakHours
}

func (e *EnhancedAnalyticsService) calculateUserSegments(patterns []UserBehaviorPattern) map[string]int {
	segments := map[string]int{
		"high_frequency":   0,
		"medium_frequency": 0,
		"low_frequency":    0,
	}

	for _, pattern := range patterns {
		frequency := len(pattern.TypicalTransactionHours) // Use hours count as frequency
		if frequency >= 10 {
			segments["high_frequency"]++
		} else if frequency >= 5 {
			segments["medium_frequency"]++
		} else {
			segments["low_frequency"]++
		}
	}

	return segments
}

func (e *EnhancedAnalyticsService) calculateRetentionRate(startDate, endDate time.Time) float64 {
	// Calculate user retention rate
	var totalUsers, retainedUsers int64

	// Get users who were active at start of period
	e.db.Model(&models.WalletTransaction{}).
		Where("DATE(created_at) = ?", startDate.Format("2006-01-02")).
		Distinct("wallet_id").Count(&totalUsers)

	// Get users who were still active at end of period
	e.db.Model(&models.WalletTransaction{}).
		Where("DATE(created_at) = ? AND wallet_id IN (?)",
			endDate.Format("2006-01-02"),
			e.db.Model(&models.WalletTransaction{}).
				Select("DISTINCT wallet_id").
				Where("DATE(created_at) = ?", startDate.Format("2006-01-02"))).
		Distinct("wallet_id").Count(&retainedUsers)

	if totalUsers == 0 {
		return 0.0
	}

	return float64(retainedUsers) / float64(totalUsers) * 100.0
}

func (e *EnhancedAnalyticsService) calculateChurnRate(startDate, endDate time.Time) float64 {
	retentionRate := e.calculateRetentionRate(startDate, endDate)
	return 100.0 - retentionRate
}

// Security Analytics Helpers

func (e *EnhancedAnalyticsService) calculateFraudRate(startDate, endDate time.Time) float64 {
	var totalTxns, fraudTxns int64

	e.db.Model(&models.WalletTransaction{}).
		Where("created_at BETWEEN ? AND ?", startDate, endDate).Count(&totalTxns)

	e.db.Model(&models.FraudAlert{}).
		Where("created_at BETWEEN ? AND ?", startDate, endDate).Count(&fraudTxns)

	if totalTxns == 0 {
		return 0.0
	}

	return float64(fraudTxns) / float64(totalTxns) * 100.0
}

func (e *EnhancedAnalyticsService) calculateBlockedTransactions(startDate, endDate time.Time) int64 {
	var blockedCount int64
	e.db.Model(&models.WalletTransaction{}).
		Where("created_at BETWEEN ? AND ? AND status = 'blocked'", startDate, endDate).
		Count(&blockedCount)
	return blockedCount
}

func (e *EnhancedAnalyticsService) calculateSecurityScore(startDate, endDate time.Time) float64 {
	fraudRate := e.calculateFraudRate(startDate, endDate)
	blockedTxns := e.calculateBlockedTransactions(startDate, endDate)

	// Simple security score calculation (100 - fraud_rate - blocked_rate)
	var totalTxns int64
	e.db.Model(&models.WalletTransaction{}).
		Where("created_at BETWEEN ? AND ?", startDate, endDate).Count(&totalTxns)

	blockedRate := 0.0
	if totalTxns > 0 {
		blockedRate = float64(blockedTxns) / float64(totalTxns) * 100.0
	}

	score := 100.0 - fraudRate - blockedRate
	if score < 0 {
		score = 0
	}

	return score
}

func (e *EnhancedAnalyticsService) calculateThreatLevels(fraudAlerts []models.FraudAlert) map[string]int {
	levels := map[string]int{
		"low":      0,
		"medium":   0,
		"high":     0,
		"critical": 0,
	}

	for _, alert := range fraudAlerts {
		if alert.RiskScore >= 80 {
			levels["critical"]++
		} else if alert.RiskScore >= 60 {
			levels["high"]++
		} else if alert.RiskScore >= 40 {
			levels["medium"]++
		} else {
			levels["low"]++
		}
	}

	return levels
}

func (e *EnhancedAnalyticsService) calculateGeographicRisks(startDate, endDate time.Time) map[string]float64 {
	// Simplified geographic risk calculation
	return map[string]float64{
		"high_risk_countries":   5.2,
		"medium_risk_countries": 15.8,
		"low_risk_countries":    79.0,
	}
}

// Performance Analytics Helpers

func (e *EnhancedAnalyticsService) calculateAverageResponseTime(startDate, endDate time.Time) float64 {
	// Simulated response time calculation
	return 125.5 // milliseconds
}

func (e *EnhancedAnalyticsService) calculateThroughput(startDate, endDate time.Time) float64 {
	var totalTxns int64
	e.db.Model(&models.WalletTransaction{}).
		Where("created_at BETWEEN ? AND ?", startDate, endDate).Count(&totalTxns)

	duration := endDate.Sub(startDate).Hours()
	if duration == 0 {
		return 0.0
	}

	return float64(totalTxns) / duration // transactions per hour
}

func (e *EnhancedAnalyticsService) calculateErrorRate(startDate, endDate time.Time) float64 {
	var totalTxns, failedTxns int64

	e.db.Model(&models.WalletTransaction{}).
		Where("created_at BETWEEN ? AND ?", startDate, endDate).Count(&totalTxns)

	e.db.Model(&models.WalletTransaction{}).
		Where("created_at BETWEEN ? AND ? AND status = 'failed'", startDate, endDate).
		Count(&failedTxns)

	if totalTxns == 0 {
		return 0.0
	}

	return float64(failedTxns) / float64(totalTxns) * 100.0
}

func (e *EnhancedAnalyticsService) calculateUptime(startDate, endDate time.Time) float64 {
	// Simulated uptime calculation
	return 99.95 // percentage
}

func (e *EnhancedAnalyticsService) calculatePeakLoadTimes(startDate, endDate time.Time) []string {
	// Simulated peak load times
	return []string{"14:00-15:00", "20:00-21:00", "09:00-10:00"}
}

func (e *EnhancedAnalyticsService) calculateResourceUtilization(startDate, endDate time.Time) map[string]float64 {
	// Simulated resource utilization
	return map[string]float64{
		"cpu":     65.2,
		"memory":  78.5,
		"disk":    45.8,
		"network": 32.1,
	}
}

// Business Analytics Helpers

func (e *EnhancedAnalyticsService) calculateRevenue(startDate, endDate time.Time) float64 {
	var totalVolume float64
	e.db.Model(&models.WalletTransaction{}).
		Where("created_at BETWEEN ? AND ? AND status = 'completed'", startDate, endDate).
		Select("COALESCE(SUM(amount), 0)").Scan(&totalVolume)

	// Assuming 1% transaction fee
	return totalVolume * 0.01
}

func (e *EnhancedAnalyticsService) calculateGrowthRate(startDate, endDate time.Time) float64 {
	// Calculate growth rate compared to previous period
	periodDuration := endDate.Sub(startDate)
	prevStartDate := startDate.Add(-periodDuration)
	prevEndDate := startDate

	currentRevenue := e.calculateRevenue(startDate, endDate)
	previousRevenue := e.calculateRevenue(prevStartDate, prevEndDate)

	if previousRevenue == 0 {
		return 0.0
	}

	return ((currentRevenue - previousRevenue) / previousRevenue) * 100.0
}

func (e *EnhancedAnalyticsService) calculateCustomerAcquisition(startDate, endDate time.Time) int64 {
	var newWallets int64
	e.db.Model(&models.Wallet{}).
		Where("created_at BETWEEN ? AND ?", startDate, endDate).Count(&newWallets)
	return newWallets
}

func (e *EnhancedAnalyticsService) calculateCustomerLifetimeValue(startDate, endDate time.Time) float64 {
	// Simplified CLV calculation
	avgRevenue := e.calculateRevenue(startDate, endDate)
	var activeUsers int64
	e.db.Model(&models.WalletTransaction{}).
		Where("created_at BETWEEN ? AND ?", startDate, endDate).
		Distinct("wallet_id").Count(&activeUsers)

	if activeUsers == 0 {
		return 0.0
	}

	avgRevenuePerUser := avgRevenue / float64(activeUsers)
	avgLifetimeMonths := 24.0 // Assuming 24 months average lifetime

	return avgRevenuePerUser * avgLifetimeMonths
}

func (e *EnhancedAnalyticsService) calculateMarketShare(startDate, endDate time.Time) float64 {
	// Simulated market share calculation
	return 12.5 // percentage
}

func (e *EnhancedAnalyticsService) calculateProfitability(startDate, endDate time.Time) float64 {
	revenue := e.calculateRevenue(startDate, endDate)
	// Assuming 70% profit margin
	return revenue * 0.70
}

// Insight and Recommendation Generators

func (e *EnhancedAnalyticsService) generateBehaviorInsights(metrics map[string]interface{}) []string {
	insights := []string{}

	if retentionRate, ok := metrics["retention_rate"].(float64); ok {
		if retentionRate < 70 {
			insights = append(insights, "User retention rate is below optimal threshold")
		} else if retentionRate > 90 {
			insights = append(insights, "Excellent user retention indicates strong product-market fit")
		}
	}

	return insights
}

func (e *EnhancedAnalyticsService) generateBehaviorRecommendations(metrics map[string]interface{}) []string {
	recommendations := []string{}

	if churnRate, ok := metrics["churn_rate"].(float64); ok {
		if churnRate > 30 {
			recommendations = append(recommendations, "Implement user engagement campaigns to reduce churn")
		}
	}

	return recommendations
}

func (e *EnhancedAnalyticsService) generateSecurityInsights(metrics map[string]interface{}) []string {
	insights := []string{}

	if fraudRate, ok := metrics["fraud_rate"].(float64); ok {
		if fraudRate > 2.0 {
			insights = append(insights, "Fraud rate is above industry average")
		}
	}

	return insights
}

func (e *EnhancedAnalyticsService) generateSecurityRecommendations(metrics map[string]interface{}) []string {
	recommendations := []string{}

	if securityScore, ok := metrics["security_score"].(float64); ok {
		if securityScore < 80 {
			recommendations = append(recommendations, "Enhance fraud detection algorithms")
		}
	}

	return recommendations
}

func (e *EnhancedAnalyticsService) generatePerformanceInsights(metrics map[string]interface{}) []string {
	insights := []string{}

	if errorRate, ok := metrics["error_rate"].(float64); ok {
		if errorRate > 5.0 {
			insights = append(insights, "Error rate exceeds acceptable threshold")
		}
	}

	return insights
}

func (e *EnhancedAnalyticsService) generatePerformanceRecommendations(metrics map[string]interface{}) []string {
	recommendations := []string{}

	if avgResponseTime, ok := metrics["average_response_time"].(float64); ok {
		if avgResponseTime > 200 {
			recommendations = append(recommendations, "Optimize API response times")
		}
	}

	return recommendations
}

func (e *EnhancedAnalyticsService) generateBusinessInsights(metrics map[string]interface{}) []string {
	insights := []string{}

	if growthRate, ok := metrics["growth_rate"].(float64); ok {
		if growthRate > 20 {
			insights = append(insights, "Strong revenue growth indicates market expansion opportunity")
		} else if growthRate < 0 {
			insights = append(insights, "Negative growth requires immediate attention")
		}
	}

	return insights
}

func (e *EnhancedAnalyticsService) generateBusinessRecommendations(metrics map[string]interface{}) []string {
	recommendations := []string{}

	if marketShare, ok := metrics["market_share"].(float64); ok {
		if marketShare < 10 {
			recommendations = append(recommendations, "Focus on market penetration strategies")
		}
	}

	return recommendations
}
