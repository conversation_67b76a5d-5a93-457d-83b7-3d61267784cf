#!/bin/bash

# Wallet Platform Deployment Script
# This script helps deploy the wallet platform to different environments

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT="development"
DEPLOYMENT_TYPE="docker-compose"
BUILD_IMAGE=true
RUN_MIGRATIONS=true
SKIP_SECURITY_CHECK=false

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -e, --environment    Environment (development|staging|production) [default: development]"
    echo "  -t, --type          Deployment type (docker-compose|kubernetes) [default: docker-compose]"
    echo "  -b, --build         Build Docker image [default: true]"
    echo "  -m, --migrate       Run database migrations [default: true]"
    echo "  -s, --skip-security Skip security checks (NOT RECOMMENDED) [default: false]"
    echo "  -h, --help          Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 -e development -t docker-compose"
    echo "  $0 -e production -t kubernetes -b true -m true"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -t|--type)
            DEPLOYMENT_TYPE="$2"
            shift 2
            ;;
        -b|--build)
            BUILD_IMAGE="$2"
            shift 2
            ;;
        -m|--migrate)
            RUN_MIGRATIONS="$2"
            shift 2
            ;;
        -s|--skip-security)
            SKIP_SECURITY_CHECK=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(development|staging|production)$ ]]; then
    print_error "Invalid environment: $ENVIRONMENT"
    print_error "Valid environments: development, staging, production"
    exit 1
fi

# Validate deployment type
if [[ ! "$DEPLOYMENT_TYPE" =~ ^(docker-compose|kubernetes)$ ]]; then
    print_error "Invalid deployment type: $DEPLOYMENT_TYPE"
    print_error "Valid types: docker-compose, kubernetes"
    exit 1
fi

print_status "Starting deployment for environment: $ENVIRONMENT"
print_status "Deployment type: $DEPLOYMENT_TYPE"

# Security check for production
if [[ "$ENVIRONMENT" == "production" && "$SKIP_SECURITY_CHECK" == false ]]; then
    print_warning "PRODUCTION DEPLOYMENT DETECTED"
    print_warning "Performing security checks..."
    
    # Check if security issues document exists
    if [[ -f "docs/CODE_QUALITY_ISSUES.md" ]]; then
        print_error "CRITICAL SECURITY ISSUES IDENTIFIED"
        print_error "Please review docs/CODE_QUALITY_ISSUES.md before production deployment"
        print_error "Current production readiness score: 45/100"
        print_error ""
        print_error "Critical issues that MUST be fixed:"
        print_error "1. Admin authentication is disabled"
        print_error "2. 2FA implementation is incomplete"
        print_error "3. PIN management is not implemented"
        print_error "4. Input validation gaps exist"
        print_error ""
        print_error "Use --skip-security flag to bypass this check (NOT RECOMMENDED)"
        exit 1
    fi
fi

# Check if required files exist
print_status "Checking required files..."

ENV_FILE=".env.${ENVIRONMENT}"
if [[ ! -f "$ENV_FILE" ]]; then
    print_error "Environment file not found: $ENV_FILE"
    print_error "Please copy .env.${ENVIRONMENT}.example to $ENV_FILE and configure it"
    exit 1
fi

# Check Docker
if command -v docker &> /dev/null; then
    print_success "Docker is installed"
else
    print_error "Docker is not installed"
    exit 1
fi

# Build Docker image if requested
if [[ "$BUILD_IMAGE" == "true" ]]; then
    print_status "Building Docker image..."
    
    if [[ "$ENVIRONMENT" == "production" ]]; then
        docker build -t wallet-platform:production -f Dockerfile.production .
    else
        docker build -t wallet-platform:$ENVIRONMENT .
    fi
    
    print_success "Docker image built successfully"
fi

# Deploy based on type
case $DEPLOYMENT_TYPE in
    docker-compose)
        print_status "Deploying with Docker Compose..."
        
        if [[ "$ENVIRONMENT" == "production" ]]; then
            COMPOSE_FILE="docker-compose.production.yml"
        else
            COMPOSE_FILE="docker-compose.yml"
        fi
        
        # Check if Docker Compose is installed
        if command -v docker-compose &> /dev/null; then
            print_success "Docker Compose is installed"
        else
            print_error "Docker Compose is not installed"
            exit 1
        fi
        
        # Stop existing containers
        print_status "Stopping existing containers..."
        docker-compose -f $COMPOSE_FILE down
        
        # Start services
        print_status "Starting services..."
        docker-compose -f $COMPOSE_FILE up -d
        
        # Wait for services to be ready
        print_status "Waiting for services to be ready..."
        sleep 30
        
        ;;
        
    kubernetes)
        print_status "Deploying to Kubernetes..."
        
        # Check if kubectl is installed
        if command -v kubectl &> /dev/null; then
            print_success "kubectl is installed"
        else
            print_error "kubectl is not installed"
            exit 1
        fi
        
        # Apply Kubernetes manifests
        print_status "Applying Kubernetes manifests..."
        kubectl apply -f k8s/namespace.yaml
        kubectl apply -f k8s/mysql.yaml
        kubectl apply -f k8s/redis.yaml
        kubectl apply -f k8s/deployment.yaml
        
        # Wait for deployment to be ready
        print_status "Waiting for deployment to be ready..."
        kubectl wait --for=condition=available --timeout=300s deployment/wallet-platform -n wallet-platform
        
        ;;
esac

# Run migrations if requested
if [[ "$RUN_MIGRATIONS" == "true" ]]; then
    print_status "Running database migrations..."
    
    case $DEPLOYMENT_TYPE in
        docker-compose)
            docker-compose -f $COMPOSE_FILE exec wallet-platform ./wallet-platform migrate
            ;;
        kubernetes)
            kubectl exec -n wallet-platform deployment/wallet-platform -- ./wallet-platform migrate
            ;;
    esac
    
    print_success "Database migrations completed"
fi

# Health check
print_status "Performing health check..."

case $DEPLOYMENT_TYPE in
    docker-compose)
        # Wait a bit more for services to fully start
        sleep 10
        
        if curl -f http://localhost:8086/health &> /dev/null; then
            print_success "Health check passed"
        else
            print_warning "Health check failed - service may still be starting"
        fi
        ;;
    kubernetes)
        # Get service endpoint
        SERVICE_IP=$(kubectl get service wallet-platform-service -n wallet-platform -o jsonpath='{.spec.clusterIP}')
        
        if kubectl exec -n wallet-platform deployment/wallet-platform -- wget --spider -q http://localhost:8086/health; then
            print_success "Health check passed"
        else
            print_warning "Health check failed - service may still be starting"
        fi
        ;;
esac

print_success "Deployment completed successfully!"
print_status "Environment: $ENVIRONMENT"
print_status "Type: $DEPLOYMENT_TYPE"

# Show next steps
echo ""
print_status "Next steps:"
case $DEPLOYMENT_TYPE in
    docker-compose)
        echo "  - Access the application at: http://localhost:8086"
        echo "  - View logs: docker-compose -f $COMPOSE_FILE logs -f"
        echo "  - Stop services: docker-compose -f $COMPOSE_FILE down"
        ;;
    kubernetes)
        echo "  - Check status: kubectl get pods -n wallet-platform"
        echo "  - View logs: kubectl logs -n wallet-platform deployment/wallet-platform"
        echo "  - Port forward: kubectl port-forward -n wallet-platform service/wallet-platform-service 8086:80"
        ;;
esac

if [[ "$ENVIRONMENT" == "production" ]]; then
    echo ""
    print_warning "PRODUCTION DEPLOYMENT REMINDERS:"
    print_warning "1. Ensure all secrets are properly configured"
    print_warning "2. Set up monitoring and alerting"
    print_warning "3. Configure backup procedures"
    print_warning "4. Review security settings"
    print_warning "5. Test all critical functionality"
fi
