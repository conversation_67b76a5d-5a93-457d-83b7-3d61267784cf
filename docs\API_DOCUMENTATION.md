# Wallet Platform API Documentation

## Overview

The Wallet Platform is a production-ready microservice that provides comprehensive digital wallet and PayCard functionality. This platform operates as an independent service with its own database, authentication, and API endpoints, supporting user authentication, admin management, internal service communication, and external webhook integrations.

## Base URL

```
Production: https://wallet-api.yourcompany.com
Development: http://localhost:8086/api/v1
```

## Authentication

The Wallet Platform API supports multiple authentication methods:

### 1. User Authentication (JWT)

For direct user access, use JWT (JSON Web Tokens) for authentication. All protected endpoints require a valid JWT token in the Authorization header.

#### Headers Required
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

### 2. Admin Authentication

For administrative access, use admin-specific JWT tokens with elevated privileges.

#### Headers Required
```
Authorization: Bearer <admin_jwt_token>
X-Admin-Role: <admin_role>
Content-Type: application/json
```

### 3. Service-to-Service Authentication (Internal API Key)

For service-to-service communication, use the internal API key authentication. This allows other services in your ecosystem to access wallet functionality without requiring direct user authentication.

#### Headers Required
```
X-Internal-Key: <internal_api_key>
X-Service-Name: <service_name>
X-User-ID: <user_id> (optional)
X-Wallet-ID: <wallet_id> (optional)
Content-Type: application/json
```

#### Internal API Endpoints

All internal endpoints are available under `/api/v1/internal/` and require the `X-Internal-Key` header. These endpoints provide the same functionality as regular endpoints but are designed for service-to-service communication.

For detailed information about service-to-service authentication, see [SERVICE_TO_SERVICE_AUTH.md](SERVICE_TO_SERVICE_AUTH.md).

## Response Format

All API responses follow a standardized format:

### Success Response
```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": { ... },
  "timestamp": "2024-01-01T00:00:00Z",
  "request_id": "uuid"
}
```

### Error Response
```json
{
  "error": "ERROR_CODE",
  "message": "Human readable error message",
  "code": "400",
  "timestamp": "2024-01-01T00:00:00Z",
  "request_id": "uuid"
}
```

### Paginated Response
```json
{
  "success": true,
  "data": {
    "items": [...],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "total_pages": 5,
      "has_next": true,
      "has_prev": false
    }
  }
}
```

## Health Check

### GET /health
Check API health status.

**Response:**
```json
{
  "status": "healthy",
  "service": "wallet-platform",
  "version": "1.0.0",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## Authentication Endpoints

### POST /auth/login
Authenticate user and receive JWT token.

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "token": "jwt_token_here",
    "user": {
      "id": "user_id",
      "email": "<EMAIL>",
      "name": "User Name"
    }
  }
}
```

### POST /auth/register
Register a new user account.

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "name": "User Name",
  "phone": "+**********"
}
```

### POST /auth/verify
Verify user account with verification code.

**Request:**
```json
{
  "email": "<EMAIL>",
  "verification_code": "123456"
}
```

### POST /auth/forgot-password
Request password reset.

**Request:**
```json
{
  "email": "<EMAIL>"
}
```

### POST /auth/reset-password
Reset password with reset token.

**Request:**
```json
{
  "reset_token": "reset_token_here",
  "new_password": "new_password123"
}
```

## API Endpoints

### Wallet Management

#### Create Wallet
```http
POST /api/v1/wallets
```

**Request Body:**
```json
{
  "phone_number": "+**********",
  "pin": "1234"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Wallet created successfully",
  "data": {
    "wallet_id": "wallet_123",
    "phone_number": "+**********",
    "balance": 0.00,
    "currency": "USD"
  }
}
```

#### Get My Wallet
```http
GET /api/v1/wallets/me
```

**Response:**
```json
{
  "success": true,
  "message": "Wallet retrieved successfully",
  "data": {
    "wallet_id": "wallet_123",
    "phone_number": "+**********",
    "balance": 1000.50,
    "currency": "USD",
    "status": "active"
  }
}
```

#### Get Wallet by ID
```http
GET /api/v1/wallets/{id}
```

#### Update Wallet
```http
PUT /api/v1/wallets/{id}
```

**Request Body:**
```json
{
  "daily_limit": 5000.00,
  "monthly_limit": 50000.00,
  "settings": {
    "notifications": true,
    "auto_topup": false
  }
}
```

#### Get Wallet Balance
```http
GET /api/v1/wallets/{id}/balance
```

**Response:**
```json
{
  "success": true,
  "data": {
    "balance": 1000.50,
    "currency": "USD",
    "available_balance": 950.50,
    "pending_balance": 50.00,
    "last_updated": "2024-01-01T00:00:00Z"
  }
}
```

#### Top Up Wallet
```http
POST /api/v1/wallets/{id}/topup
```

**Request Body:**
```json
{
  "amount": 100.00,
  "payment_method": "card",
  "payment_source_id": "card_123",
  "description": "Wallet top-up"
}
```

#### Transfer Funds
```http
POST /api/v1/wallets/transfer
```

**Request Body:**
```json
{
  "from_wallet_id": "wallet_1",
  "to_wallet_id": "wallet_2",
  "amount": 50.00,
  "description": "Transfer to friend",
  "pin": "1234"
}
```

#### Get Wallet Transactions
```http
GET /api/v1/wallets/{id}/transactions
```

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20)
- `start_date`: Filter from date (YYYY-MM-DD)
- `end_date`: Filter to date (YYYY-MM-DD)
- `type`: Transaction type filter

#### Get Wallet Analytics
```http
GET /api/v1/wallets/{id}/analytics
```

### PayCard Management

#### Create PayCard
```http
POST /api/v1/cards
```

**Request Body:**
```json
{
  "wallet_id": "wallet_123",
  "card_type": "virtual"
}
```

**Response:**
```json
{
  "success": true,
  "message": "PayCard created successfully",
  "data": {
    "card_id": "card_123",
    "card_number": "****-****-****-1234",
    "card_type": "virtual",
    "status": "active",
    "wallet_id": "wallet_123"
  }
}
```

#### Get My Cards
```http
GET /api/v1/cards
```

**Response:**
```json
{
  "success": true,
  "data": {
    "cards": [
      {
        "card_id": "card_123",
        "card_number": "****-****-****-1234",
        "card_type": "virtual",
        "status": "active",
        "wallet_id": "wallet_123"
      }
    ]
  }
}
```

#### Get PayCard by ID
```http
GET /api/v1/cards/{id}
```

#### Update PayCard
```http
PUT /api/v1/cards/{id}
```

**Request Body:**
```json
{
  "spending_limit": 1000.00,
  "daily_limit": 500.00,
  "status": "active"
}
```

#### Update Card PIN
```http
POST /api/v1/cards/{id}/pin
```

**Request Body:**
```json
{
  "current_pin": "1234",
  "new_pin": "5678"
}
```

#### Block PayCard
```http
POST /api/v1/cards/{id}/block
```

**Request Body:**
```json
{
  "reason": "lost_card"
}
```

#### Unblock PayCard
```http
POST /api/v1/cards/{id}/unblock
```

**Request Body:**
```json
{
  "reason": "card_found"
}
```

#### Process Card Transaction
```http
POST /api/v1/cards/{id}/transactions
```

**Request Body:**
```json
{
  "amount": 25.99,
  "merchant_id": "merchant_123",
  "description": "Coffee purchase",
  "currency": "USD"
}
```

#### Get Card Transactions
```http
GET /api/v1/cards/{id}/transactions
```

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20)
- `start_date`: Filter from date (YYYY-MM-DD)
- `end_date`: Filter to date (YYYY-MM-DD)

#### Get Card Analytics
```http
GET /api/v1/cards/{id}/analytics
```

### Enhanced PayCard Features

#### Request Physical Card
```http
POST /api/v1/cards/{id}/request-physical
```

**Request Body:**
```json
{
  "delivery_address": {
    "street": "123 Main St",
    "city": "New York",
    "state": "NY",
    "zip": "10001",
    "country": "US"
  }
}
```

#### Activate Physical Card
```http
POST /api/v1/cards/activate-physical
```

**Request Body:**
```json
{
  "serial_number": "**********",
  "activation_code": "ABC123"
}
```

#### Generate Card Token
```http
POST /api/v1/cards/{id}/generate-token
```

#### Add Merchant to Whitelist
```http
POST /api/v1/cards/{id}/whitelist/add
```

**Request Body:**
```json
{
  "merchant_id": "merchant_123",
  "merchant_name": "Coffee Shop"
}
```

#### Set Spending Controls
```http
POST /api/v1/cards/{id}/controls/set
```

**Request Body:**
```json
{
  "daily_limit": 500.00,
  "monthly_limit": 2000.00,
  "categories": ["grocery", "gas"],
  "blocked_categories": ["gambling"]
}
```

#### Process Contactless Payment
```http
POST /api/v1/cards/contactless/pay
```

**Request Body:**
```json
{
  "card_token": "token_123",
  "amount": 15.99,
  "merchant_id": "merchant_456",
  "description": "Contactless payment"
}
```
PUT /api/v1/cards/{id}/pin
```

**Request Body:**
```json
{
  "current_pin": "1234",
  "new_pin": "5678"
}
```

#### Request Physical Card
```http
POST /api/v1/cards/{id}/request-physical
```

#### Activate Physical Card
```http
POST /api/v1/cards/activate-physical
```

**Request Body:**
```json
{
  "serial_number": "CARD123456789",
  "activation_code": "ACT123"
}
```

#### Generate Card Token
```http
POST /api/v1/cards/{id}/generate-token
```

#### Add Merchant to Whitelist
```http
POST /api/v1/cards/{id}/whitelist/add
```

**Request Body:**
```json
{
  "merchant_id": "MERCH123",
  "merchant_name": "Coffee Shop"
}
```

#### Set Spending Controls
```http
POST /api/v1/cards/{id}/controls/set
```

**Request Body:**
```json
{
  "daily_limit": 500.00,
  "monthly_limit": 5000.00,
  "categories": ["food", "transport"]
}
```

#### Process Contactless Payment
```http
POST /api/v1/cards/contactless/pay
```

**Request Body:**
```json
{
  "card_token": "TOKEN123",
  "amount": 25.50,
  "merchant_id": "MERCH123",
  "description": "Coffee purchase"
}
```

### Security Management

#### Register Device
```http
POST /api/v1/security/devices
```

**Request Body:**
```json
{
  "device_name": "iPhone 12",
  "device_type": "mobile",
  "device_id": "device_unique_id"
}
```

#### Verify Device
```http
POST /api/v1/security/devices/verify
```

#### Setup 2FA
```http
POST /api/v1/security/2fa/setup
```

#### Verify 2FA
```http
POST /api/v1/security/2fa/verify
```

**Request Body:**
```json
{
  "code": "123456"
}
```

#### Get Security Settings
```http
GET /api/v1/security/settings
```

#### Update Security Settings
```http
PUT /api/v1/security/settings
```

#### Get Security Events
```http
GET /api/v1/security/events
```

#### Get Fraud Alerts
```http
GET /api/v1/security/fraud-alerts
```

### Service Management

#### Get Available Services
```http
GET /api/v1/services
```

#### Subscribe to Service
```http
POST /api/v1/services/subscribe
```

**Request Body:**
```json
{
  "service_id": "service_123",
  "plan": "premium"
}
```

#### Get My Subscriptions
```http
GET /api/v1/services/subscriptions
```

#### Update Subscription
```http
PUT /api/v1/services/subscriptions/{id}
```

#### Cancel Subscription
```http
DELETE /api/v1/services/subscriptions/{id}
```

#### Get Service Usage
```http
GET /api/v1/services/subscriptions/{id}/usage
```

#### Record Service Usage
```http
POST /api/v1/services/subscriptions/{id}/usage
```

### Analytics

#### Get Dashboard Data
```http
GET /api/v1/analytics/dashboard
```

#### Get Real-time Metrics
```http
GET /api/v1/analytics/realtime
```

#### Get Business Metrics
```http
GET /api/v1/analytics/business
```

#### Get System Analytics
```http
GET /api/v1/analytics/system
```

#### Get Wallet Analytics
```http
GET /api/v1/analytics/wallets/{wallet_id}
```

#### Get Card Analytics
```http
GET /api/v1/analytics/cards/{card_id}
```

### Monitoring

#### Get System Health
```http
GET /api/v1/monitoring/health
```

#### Get Performance Metrics
```http
GET /api/v1/monitoring/performance
```

#### Create Alert
```http
POST /api/v1/monitoring/alerts
```

## Admin Endpoints

All admin endpoints require admin authentication and are prefixed with `/api/v1/admin/`.

### System Management

#### Get System Status
```http
GET /api/v1/admin/system/status
```

#### Get System Metrics
```http
GET /api/v1/admin/system/metrics
```

#### Update System Settings
```http
PUT /api/v1/admin/system/settings
```

### User Management

#### Get All Users
```http
GET /api/v1/admin/users
```

#### Get User Details
```http
GET /api/v1/admin/users/{id}
```

#### Update User
```http
PUT /api/v1/admin/users/{id}
```

#### Suspend User
```http
POST /api/v1/admin/users/{id}/suspend
```

#### Activate User
```http
POST /api/v1/admin/users/{id}/activate
```

### Wallet Management

#### Get All Wallets
```http
GET /api/v1/admin/wallets
```

#### Get Wallet Details
```http
GET /api/v1/admin/wallets/{id}
```

#### Force Update Wallet
```http
PUT /api/v1/admin/wallets/{id}/force-update
```

#### Freeze Wallet
```http
POST /api/v1/admin/wallets/{id}/freeze
```

#### Unfreeze Wallet
```http
POST /api/v1/admin/wallets/{id}/unfreeze
```

### Card Management

#### Get All Cards
```http
GET /api/v1/admin/cards
```

#### Get Card Details
```http
GET /api/v1/admin/cards/{id}
```

#### Force Block Card
```http
POST /api/v1/admin/cards/{id}/force-block
```

#### Get Card Audit
```http
GET /api/v1/admin/cards/{id}/audit
```

### Security Management

#### Get All Security Events
```http
GET /api/v1/admin/security/events
```

#### Get All Fraud Alerts
```http
GET /api/v1/admin/security/fraud-alerts
```

#### Update Fraud Alert
```http
PUT /api/v1/admin/security/fraud-alerts/{id}
```

#### Get Risk Profiles
```http
GET /api/v1/admin/security/risk-profiles
```

#### Update Risk Profile
```http
PUT /api/v1/admin/security/risk-profiles/{id}
```

### Analytics & Reporting

#### Generate Report
```http
POST /api/v1/admin/reports/generate
```

#### Get Report Status
```http
GET /api/v1/admin/reports/{id}/status
```

#### Download Report
```http
GET /api/v1/admin/reports/{id}/download
```

## Internal API Endpoints

All internal endpoints require internal authentication and are prefixed with `/api/v1/internal/`.

### Wallet Operations

#### Get Wallet by Phone
```http
GET /api/v1/internal/wallets/by-phone/{phone}
```

#### Batch Wallet Lookup
```http
POST /api/v1/internal/wallets/batch-lookup
```

**Request Body:**
```json
{
  "phone_numbers": ["+**********", "+**********"]
}
```

### Card Operations

#### Get Cards by Wallet
```http
GET /api/v1/internal/cards/by-wallet/{wallet_id}
```

### Webhook Operations

#### Send Internal Webhook
```http
POST /api/v1/internal/webhooks/send
```

#### Health Check
```http
GET /api/v1/internal/health
```

### Migration

#### Start Migration
```http
POST /api/v1/migration/start
```

#### Migrate Wallets
```http
POST /api/v1/migration/wallets
```

#### Migrate Cards
```http
POST /api/v1/migration/cards
```

#### Migrate Transactions
```http
POST /api/v1/migration/transactions
```

#### Get Migration Status
```http
GET /api/v1/migration/status
```

#### Get Migration Progress
```http
GET /api/v1/migration/progress/{id}
```

#### Validate Migration
```http
POST /api/v1/migration/validate
```

#### Rollback Migration
```http
POST /api/v1/migration/rollback
```

## Webhook Management

### Register Webhook
```http
POST /api/v1/webhooks/register
```

**Request Body:**
```json
{
  "url": "https://your-app.com/webhooks",
  "secret": "webhook_secret",
  "events": ["wallet.created", "card.transaction", "transaction.completed"],
  "description": "Main application webhook"
}
```

### Get Webhook Events
```http
GET /api/v1/webhooks/events
```

### Retry Failed Webhooks
```http
POST /api/v1/webhooks/retry
```

### Receive External Webhooks
```http
POST /api/v1/webhooks/external/payment-engine
POST /api/v1/webhooks/external/sms-provider
POST /api/v1/webhooks/external/email-provider
POST /api/v1/webhooks/external/fraud-detection
```

**Note:** These endpoints are for receiving webhooks from external services and include signature verification.

## Error Responses

All error responses follow this format:

```json
{
  "error": "ERROR_CODE",
  "message": "Human readable error message",
  "code": "400"
}
```

### Common Error Codes

- `INVALID_REQUEST` - Request format is invalid
- `WALLET_NOT_FOUND` - Wallet does not exist
- `INSUFFICIENT_FUNDS` - Not enough balance for transaction
- `CARD_BLOCKED` - Card is blocked and cannot be used
- `INVALID_PIN` - PIN verification failed
- `RATE_LIMIT_EXCEEDED` - Too many requests

## Rate Limiting

The API implements rate limiting with the following limits:

- **Authentication endpoints**: 10 requests per minute
- **Wallet operations**: 100 requests per minute
- **Card operations**: 100 requests per minute
- **Analytics**: 50 requests per minute
- **Migration**: 5 requests per minute

## Webhook Events

The platform sends webhooks for the following events:

- `wallet.created` - New wallet created
- `wallet.updated` - Wallet information updated
- `wallet.transaction` - New wallet transaction
- `card.created` - New card created
- `card.blocked` - Card blocked
- `card.transaction` - Card transaction processed
- `migration.completed` - Data migration completed
- `security.alert` - Security alert triggered

## Security

### Authentication Methods
- JWT tokens for user authentication
- API keys for service-to-service communication
- HMAC signatures for webhook verification

### Security Features
- Two-factor authentication (2FA)
- Device fingerprinting
- Fraud detection
- IP geolocation
- Rate limiting
- Request encryption

## Response Format

### Success Response
```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": {
    // Response data here
  }
}
```

### Error Response
```json
{
  "error": "ERROR_CODE",
  "message": "Human readable error message",
  "code": "400"
}
```

## Request/Response Examples

### Create Wallet Example

**Request:**
```bash
curl -X POST https://wallet-api.yourcompany.com/api/v1/wallets \
  -H "Authorization: Bearer <jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "phone_number": "+**********",
    "pin": "1234"
  }'
```

**Response:**
```json
{
  "success": true,
  "message": "Wallet created successfully",
  "data": {
    "id": 123,
    "phone_number": "+**********",
    "account_number": "IN**********",
    "wallet_type": "individual",
    "balance": 0.00,
    "status": "active",
    "created_at": "2024-01-15T10:30:00Z"
  }
}
```

### Transfer Funds Example

**Request:**
```bash
curl -X POST https://wallet-api.yourcompany.com/api/v1/wallets/transfer \
  -H "Authorization: Bearer <jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "from_wallet_id": 123,
    "to_wallet_id": 456,
    "amount": 50.00,
    "description": "Payment for services"
  }'
```

**Response:**
```json
{
  "success": true,
  "message": "Transfer completed successfully",
  "data": {
    "transaction_id": "TXN_12345678",
    "from_wallet_id": 123,
    "to_wallet_id": 456,
    "amount": 50.00,
    "fee": 0.50,
    "description": "Payment for services",F
    "status": "completed",
    "created_at": "2024-01-15T10:35:00Z"
  }
}
```

## Pagination

For endpoints that return lists, pagination is supported:

**Query Parameters:**
- `page` - Page number (default: 1)
- `limit` - Items per page (default: 20, max: 100)
- `sort` - Sort field (default: created_at)
- `order` - Sort order: asc/desc (default: desc)

**Example:**
```bash
GET /api/v1/wallets/123/transactions?page=2&limit=50&sort=amount&order=desc
```

**Paginated Response:**
```json
{
  "success": true,
  "message": "Transactions retrieved successfully",
  "data": {
    "transactions": [...],
    "pagination": {
      "current_page": 2,
      "per_page": 50,
      "total_pages": 10,
      "total_items": 500,
      "has_next": true,
      "has_prev": true
    }
  }
}
```

## Filtering and Search

Many endpoints support filtering and search:

**Query Parameters:**
- `search` - Search term
- `status` - Filter by status
- `start_date` - Filter from date (ISO 8601)
- `end_date` - Filter to date (ISO 8601)
- `amount_min` - Minimum amount
- `amount_max` - Maximum amount

**Example:**
```bash
GET /api/v1/wallets/123/transactions?status=completed&start_date=2024-01-01&amount_min=10.00
```

## Webhook Payload Examples

### Wallet Created
```json
{
  "event": "wallet.created",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": {
    "wallet_id": 123,
    "phone_number": "+**********",
    "account_number": "IN**********",
    "wallet_type": "individual"
  }
}
```

### Card Transaction
```json
{
  "event": "card.transaction",
  "timestamp": "2024-01-15T10:35:00Z",
  "data": {
    "transaction_id": "TXN_87654321",
    "card_id": 456,
    "wallet_id": 123,
    "amount": 25.50,
    "merchant_id": "MERCH123",
    "status": "completed",
    "location": {
      "latitude": 40.7128,
      "longitude": -74.0060
    }
  }
}
```

## Testing

### Test Environment
```
Base URL: https://wallet-api-test.yourcompany.com
```

### Test Credentials
```
Test API Key: test_api_key_12345
Test JWT Token: Available through authentication endpoint
```

### Postman Collection
A Postman collection is available for testing all endpoints:
[Download Postman Collection](./postman/wallet-platform.postman_collection.json)

## SDKs and Libraries

Coming soon:
- JavaScript/Node.js SDK
- Python SDK
- PHP SDK
- Go SDK

## Support

For API support and questions:
- Email: <EMAIL>
- Documentation: https://docs.wallet-platform.yourcompany.com
- Status Page: https://status.wallet-platform.yourcompany.com
