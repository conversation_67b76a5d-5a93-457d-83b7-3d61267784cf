# Wallet Platform Test Suite

This directory contains comprehensive tests for the wallet platform, ensuring all functionality works correctly in the standalone environment.

## Test Structure

```
tests/
├── unit/                    # Unit tests for individual components
│   ├── middleware_test.go   # Authentication middleware tests
│   ├── wallet_service_test.go # Wallet service business logic tests
│   └── paycard_service_test.go # PayCard service business logic tests
├── integration/             # Integration tests
│   ├── api_test.go         # End-to-end API workflow tests
│   └── service_auth_test.go # Service-to-service authentication tests
├── load/                   # Performance and load tests
│   └── load_test.go        # Load testing scenarios
├── e2e/                    # End-to-end tests
│   └── e2e_test.go         # Complete user journey tests
├── go.mod                  # Test module dependencies
├── run_tests.sh           # Unix test runner script
├── run_tests.bat          # Windows test runner script
└── README.md              # This file
```

## Test Categories

### 1. Unit Tests (`./unit/`)

Unit tests focus on testing individual components in isolation:

- **Middleware Tests**: Authentication, rate limiting, CORS validation
- **Service Tests**: Business logic for wallets, cards, transactions
- **Model Tests**: Data validation and relationships

**Key Features Tested:**
- Service-to-service authentication with internal API keys
- Wallet creation, balance management, and transactions
- PayCard creation, PIN management, and transaction processing
- Error handling and validation

### 2. Integration Tests (`./integration/`)

Integration tests verify that different components work together correctly:

- **API Tests**: Complete API workflows with database integration
- **Service Auth Tests**: Service-to-service communication patterns

**Key Features Tested:**
- End-to-end API request/response cycles
- Database persistence and retrieval
- Authentication middleware integration
- Error propagation and handling

### 3. Load Tests (`./load/`)

Load tests evaluate system performance under high concurrent usage:

- **Wallet Creation Load**: Concurrent wallet creation scenarios
- **Transaction Load**: High-volume transaction processing
- **Internal API Load**: Service-to-service communication under load

**Performance Metrics:**
- Requests per second
- Average/min/max latency
- Error rates under load
- Concurrent user handling

### 4. End-to-End Tests (`./e2e/`)

E2E tests simulate complete user journeys and real-world scenarios:

- **Complete Wallet Workflow**: Creation → Topup → Transactions → Balance Check
- **Complete PayCard Workflow**: Creation → PIN Setup → Transactions → History
- **Wallet-to-Wallet Transfers**: Multi-wallet transaction scenarios
- **Service Authentication**: Real service-to-service communication

## Running Tests

### Prerequisites

1. **Go 1.21+** installed and configured
2. **Test Environment Variables** (automatically set by test runners):
   - `TEST_DB_PATH=:memory:`
   - `INTERNAL_API_KEY=test-internal-key`
   - `APP_ENVIRONMENT=test`

### Quick Start

#### On Unix/Linux/macOS:
```bash
cd wallet-platform/tests
./run_tests.sh
```

#### On Windows:
```cmd
cd wallet-platform\tests
run_tests.bat
```

### Running Individual Test Suites

#### Unit Tests Only:
```bash
cd tests
go test -v ./unit/...
```

#### Integration Tests Only:
```bash
cd tests
go test -v ./integration/...
```

#### Load Tests (requires running server):
```bash
cd tests
go test -v -timeout=5m ./load/...
```

#### E2E Tests (requires running server):
```bash
cd tests
go test -v -timeout=10m ./e2e/...
```

### Running Specific Tests

```bash
# Run specific test file
go test -v ./unit/middleware_test.go

# Run specific test function
go test -v -run TestInternalAuth_ValidKey ./unit/

# Run tests with coverage
go test -v -cover ./unit/...

# Run tests in short mode (skips long-running tests)
go test -v -short ./...
```

## Test Configuration

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `TEST_DB_PATH` | `:memory:` | SQLite database path for tests |
| `INTERNAL_API_KEY` | `test-internal-key` | API key for service-to-service auth |
| `APP_ENVIRONMENT` | `test` | Application environment |
| `SKIP_LOAD_TESTS` | `false` | Skip load tests in CI |
| `SKIP_E2E_TESTS` | `false` | Skip E2E tests in CI |

### Test Database

Tests use an in-memory SQLite database that is:
- Created fresh for each test suite
- Automatically migrated with all models
- Cleaned between individual tests
- Destroyed after test completion

## Service-to-Service Authentication Testing

The test suite includes comprehensive testing of the internal API authentication:

### Valid Authentication Scenarios:
- Correct internal API key with service name
- User context propagation via headers
- Multiple services accessing same resources

### Invalid Authentication Scenarios:
- Missing internal API key
- Invalid internal API key
- Malformed authentication headers

### Headers Used in Tests:
```
X-Internal-Key: test-internal-key
X-Service-Name: test-service
X-User-ID: user123
X-Wallet-ID: wallet456
```

## Performance Benchmarks

### Expected Performance Metrics:

| Operation | Target Latency | Target RPS | Error Rate |
|-----------|---------------|------------|------------|
| Wallet Creation | < 200ms | > 50 | < 2% |
| Wallet Lookup | < 100ms | > 100 | < 1% |
| Fund Transfer | < 500ms | > 20 | < 5% |
| Card Transaction | < 300ms | > 30 | < 3% |
| Internal API | < 150ms | > 80 | < 1% |

## Continuous Integration

### CI/CD Pipeline Integration:

```yaml
# Example GitHub Actions workflow
- name: Run Unit Tests
  run: |
    cd wallet-platform/tests
    go test -v ./unit/...

- name: Run Integration Tests
  run: |
    cd wallet-platform/tests
    go test -v ./integration/...

- name: Run Load Tests
  env:
    SKIP_LOAD_TESTS: true
  run: |
    cd wallet-platform/tests
    ./run_tests.sh
```

## Test Data Management

### Test Wallets:
- Phone numbers use `+256701234567` pattern
- Wallet types: `individual`, `business`
- Test amounts: Small values (1.0, 10.0, 100.0)

### Test Cards:
- Card types: `virtual`, `physical`
- Test PINs: `1234`, `5678`
- Test merchants: `Test Store`, `E2E Merchant`

## Troubleshooting

### Common Issues:

1. **Import Errors**: Ensure `go.mod` is properly configured
2. **Database Errors**: Check SQLite driver installation
3. **Authentication Errors**: Verify `INTERNAL_API_KEY` environment variable
4. **Load Test Failures**: Ensure server is running on `localhost:8086`
5. **Timeout Errors**: Increase test timeout for slow environments

### Debug Mode:

```bash
# Run tests with verbose output
go test -v -debug ./...

# Run tests with race detection
go test -v -race ./...

# Run tests with memory profiling
go test -v -memprofile=mem.prof ./...
```

## Contributing

When adding new tests:

1. **Follow naming conventions**: `TestFunctionName_Scenario`
2. **Use test suites**: Leverage `testify/suite` for setup/teardown
3. **Mock external dependencies**: Use in-memory database for isolation
4. **Add documentation**: Document test purpose and expected behavior
5. **Update this README**: Add new test categories or important changes

## Test Coverage

Target coverage levels:
- **Unit Tests**: > 80% code coverage
- **Integration Tests**: > 70% API endpoint coverage
- **E2E Tests**: > 90% user journey coverage

Generate coverage report:
```bash
go test -v -coverprofile=coverage.out ./...
go tool cover -html=coverage.out -o coverage.html
```
