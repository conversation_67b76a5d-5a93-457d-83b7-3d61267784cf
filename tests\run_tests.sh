#!/bin/bash

# Wallet Platform Test Runner
# This script runs all tests for the wallet platform

set -e

echo "🧪 Starting Wallet Platform Test Suite"
echo "======================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test configuration
TEST_DB_PATH=":memory:"
INTERNAL_API_KEY="test-internal-key"
APP_ENVIRONMENT="test"

# Export test environment variables
export TEST_DB_PATH
export INTERNAL_API_KEY
export APP_ENVIRONMENT

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to run tests with proper error handling
run_test_suite() {
    local test_name="$1"
    local test_path="$2"
    local test_flags="$3"
    
    print_status "Running $test_name..."
    
    if cd "$test_path" && go test $test_flags ./...; then
        print_success "$test_name completed successfully"
        return 0
    else
        print_error "$test_name failed"
        return 1
    fi
}

# Check if Go is installed
if ! command -v go &> /dev/null; then
    print_error "Go is not installed or not in PATH"
    exit 1
fi

# Check Go version
GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
print_status "Using Go version: $GO_VERSION"

# Navigate to the wallet platform directory
cd "$(dirname "$0")/.."

# Initialize test results
UNIT_TESTS_PASSED=false
INTEGRATION_TESTS_PASSED=false
LOAD_TESTS_PASSED=false
E2E_TESTS_PASSED=false

# Run Unit Tests
print_status "Phase 1: Unit Tests"
echo "-------------------"
if run_test_suite "Unit Tests" "tests" "-v -tags=unit ./unit/..."; then
    UNIT_TESTS_PASSED=true
fi
echo

# Run Integration Tests
print_status "Phase 2: Integration Tests"
echo "-------------------------"
if run_test_suite "Integration Tests" "tests" "-v -tags=integration ./integration/..."; then
    INTEGRATION_TESTS_PASSED=true
fi
echo

# Run Load Tests (optional, skip in CI)
if [[ "${SKIP_LOAD_TESTS:-false}" != "true" ]]; then
    print_status "Phase 3: Load Tests"
    echo "------------------"
    print_warning "Load tests require a running server instance"
    print_warning "Make sure the wallet platform is running on localhost:8086"
    
    read -p "Do you want to run load tests? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        if run_test_suite "Load Tests" "tests" "-v -timeout=5m ./load/..."; then
            LOAD_TESTS_PASSED=true
        fi
    else
        print_warning "Skipping load tests"
        LOAD_TESTS_PASSED=true  # Mark as passed since it was skipped intentionally
    fi
else
    print_warning "Skipping load tests (SKIP_LOAD_TESTS=true)"
    LOAD_TESTS_PASSED=true
fi
echo

# Run E2E Tests (optional, skip in CI)
if [[ "${SKIP_E2E_TESTS:-false}" != "true" ]]; then
    print_status "Phase 4: End-to-End Tests"
    echo "------------------------"
    print_warning "E2E tests require a running server instance"
    print_warning "Make sure the wallet platform is running on localhost:8086"
    
    read -p "Do you want to run E2E tests? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        if run_test_suite "E2E Tests" "tests" "-v -timeout=10m ./e2e/..."; then
            E2E_TESTS_PASSED=true
        fi
    else
        print_warning "Skipping E2E tests"
        E2E_TESTS_PASSED=true  # Mark as passed since it was skipped intentionally
    fi
else
    print_warning "Skipping E2E tests (SKIP_E2E_TESTS=true)"
    E2E_TESTS_PASSED=true
fi
echo

# Generate Test Report
print_status "Test Results Summary"
echo "===================="

if $UNIT_TESTS_PASSED; then
    print_success "✅ Unit Tests: PASSED"
else
    print_error "❌ Unit Tests: FAILED"
fi

if $INTEGRATION_TESTS_PASSED; then
    print_success "✅ Integration Tests: PASSED"
else
    print_error "❌ Integration Tests: FAILED"
fi

if $LOAD_TESTS_PASSED; then
    print_success "✅ Load Tests: PASSED"
else
    print_error "❌ Load Tests: FAILED"
fi

if $E2E_TESTS_PASSED; then
    print_success "✅ E2E Tests: PASSED"
else
    print_error "❌ E2E Tests: FAILED"
fi

echo

# Overall result
if $UNIT_TESTS_PASSED && $INTEGRATION_TESTS_PASSED && $LOAD_TESTS_PASSED && $E2E_TESTS_PASSED; then
    print_success "🎉 All tests passed successfully!"
    echo
    print_status "The wallet platform is ready for deployment"
    exit 0
else
    print_error "❌ Some tests failed"
    echo
    print_status "Please review the failed tests and fix any issues"
    exit 1
fi
