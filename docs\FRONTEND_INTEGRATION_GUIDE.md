# Frontend Integration Guide - Complete Wallet Platform

## Overview

This guide provides comprehensive integration instructions for frontend developers working with the wallet platform. The platform supports phone-based wallet operations, QR code payments, multi-card management, and comprehensive security features.

## Authentication System

### 1. User Registration
**Endpoint**: `POST /api/v1/auth/register`

```javascript
async function registerUser(userData) {
  const response = await fetch('/api/v1/auth/register', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      phone: "+26876543210",
      email: "<EMAIL>",
      password: "securePassword123",
      first_name: "<PERSON>",
      last_name: "<PERSON><PERSON>",
      date_of_birth: "1990-01-01"
    })
  });
  
  return await response.json();
}
```

### 2. User Login
**Endpoint**: `POST /api/v1/auth/login`

```javascript
async function loginUser(credentials) {
  const response = await fetch('/api/v1/auth/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      phone: "+26876543210",
      password: "securePassword123"
    })
  });
  
  const data = await response.json();
  if (data.success) {
    // Store token securely
    localStorage.setItem('authToken', data.data.token);
    localStorage.setItem('walletId', data.data.wallet_id);
  }
  return data;
}
```

### 3. Token Management
```javascript
// Get stored token
function getAuthToken() {
  return localStorage.getItem('authToken');
}

// Create authenticated headers
function getAuthHeaders() {
  return {
    'Authorization': `Bearer ${getAuthToken()}`,
    'Content-Type': 'application/json'
  };
}

// Check if user is authenticated
function isAuthenticated() {
  return !!getAuthToken();
}
```

## Wallet Operations

### 1. Phone-Based Wallet Management

#### Get Wallet by Phone
```javascript
async function getWalletByPhone(phone) {
  const response = await fetch(`/api/v1/wallets/phone/${phone}`, {
    headers: getAuthHeaders()
  });
  return await response.json();
}
```

#### Check Balance
```javascript
async function getBalance(phone) {
  const response = await fetch(`/api/v1/wallets/phone/${phone}/balance`, {
    headers: getAuthHeaders()
  });
  return await response.json();
}
```

#### Topup Wallet
```javascript
async function topupWallet(phone, amount, paymentMethod = 'card') {
  const response = await fetch(`/api/v1/wallets/phone/${phone}/topup`, {
    method: 'POST',
    headers: getAuthHeaders(),
    body: JSON.stringify({
      amount: amount,
      payment_method: paymentMethod,
      description: 'Wallet topup'
    })
  });
  return await response.json();
}
```

#### Transfer Money
```javascript
async function transferMoney(fromPhone, toPhone, amount, description) {
  const response = await fetch(`/api/v1/wallets/phone/${fromPhone}/transfer`, {
    method: 'POST',
    headers: getAuthHeaders(),
    body: JSON.stringify({
      to_phone: toPhone,
      amount: amount,
      description: description
    })
  });
  return await response.json();
}
```

#### Withdraw Funds
```javascript
async function withdrawFunds(phone, amount, bankAccount) {
  const response = await fetch(`/api/v1/wallets/phone/${phone}/withdraw`, {
    method: 'POST',
    headers: getAuthHeaders(),
    body: JSON.stringify({
      amount: amount,
      bank_account: bankAccount,
      description: 'Withdrawal to bank'
    })
  });
  return await response.json();
}
```

### 2. Transaction History
```javascript
async function getTransactionHistory(phone, page = 1, limit = 20) {
  const response = await fetch(
    `/api/v1/wallets/phone/${phone}/transactions?page=${page}&limit=${limit}`,
    { headers: getAuthHeaders() }
  );
  return await response.json();
}
```

## PayCard Management

### 1. Card Operations

#### Get User's Cards
```javascript
async function getUserCards() {
  const response = await fetch('/api/v1/cards/', {
    headers: getAuthHeaders()
  });
  return await response.json();
}
```

#### Create New Card
```javascript
async function createCard(cardData) {
  const response = await fetch('/api/v1/cards/', {
    method: 'POST',
    headers: getAuthHeaders(),
    body: JSON.stringify({
      card_type: 'standard', // standard, premium, business
      holder_name: 'John Doe',
      spending_limit: 5000.00,
      pin: '1234'
    })
  });
  return await response.json();
}
```

#### Update Card Settings
```javascript
async function updateCardSettings(cardId, settings) {
  const response = await fetch(`/api/v1/cards/${cardId}`, {
    method: 'PUT',
    headers: getAuthHeaders(),
    body: JSON.stringify(settings)
  });
  return await response.json();
}
```

#### Block/Unblock Card
```javascript
async function blockCard(cardId, reason) {
  const response = await fetch(`/api/v1/cards/${cardId}/block`, {
    method: 'POST',
    headers: getAuthHeaders(),
    body: JSON.stringify({ reason })
  });
  return await response.json();
}

async function unblockCard(cardId) {
  const response = await fetch(`/api/v1/cards/${cardId}/unblock`, {
    method: 'POST',
    headers: getAuthHeaders()
  });
  return await response.json();
}
```

### 2. Card Security

#### Update PIN
```javascript
async function updateCardPIN(cardId, currentPIN, newPIN) {
  const response = await fetch(`/api/v1/cards/${cardId}/pin`, {
    method: 'POST',
    headers: getAuthHeaders(),
    body: JSON.stringify({
      current_pin: currentPIN,
      new_pin: newPIN
    })
  });
  return await response.json();
}
```

#### Set Spending Limits
```javascript
async function setSpendingLimits(cardId, limits) {
  const response = await fetch(`/api/v1/cards/${cardId}/limits`, {
    method: 'PUT',
    headers: getAuthHeaders(),
    body: JSON.stringify({
      daily_limit: limits.daily,
      monthly_limit: limits.monthly,
      per_transaction_limit: limits.perTransaction
    })
  });
  return await response.json();
}
```

#### Set Merchant Restrictions
```javascript
async function setMerchantRestrictions(cardId, restrictions) {
  const response = await fetch(`/api/v1/cards/${cardId}/merchant-restrictions`, {
    method: 'PUT',
    headers: getAuthHeaders(),
    body: JSON.stringify({
      blacklisted_categories: restrictions.blacklist,
      whitelisted_merchants: restrictions.whitelist,
      international_transactions: restrictions.international
    })
  });
  return await response.json();
}
```

## QR Code Payment System

### 1. Generate QR Code for Payment
```javascript
async function generateQRCode(cardId) {
  const response = await fetch(`/api/v1/cards/${cardId}/qr-code`, {
    headers: getAuthHeaders()
  });
  
  const data = await response.json();
  if (data.success) {
    return data.data.qr_code_data;
  }
  throw new Error(data.message);
}
```

### 2. Display QR Code
```javascript
import QRCode from 'qrcode';

async function displayQRCode(cardId, containerId) {
  try {
    const qrData = await generateQRCode(cardId);
    const canvas = document.getElementById(containerId);
    
    await QRCode.toCanvas(canvas, qrData, {
      width: 300,
      margin: 2,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      },
      errorCorrectionLevel: 'M'
    });
    
    // Auto-refresh QR code every 30 minutes
    setTimeout(() => displayQRCode(cardId, containerId), 30 * 60 * 1000);
    
  } catch (error) {
    console.error('Failed to generate QR code:', error);
  }
}
```

### 3. QR Code Scanner Integration
```javascript
// For merchant/POS applications
async function scanAndValidateQR(scannedData) {
  // First validate the QR code
  const validation = await fetch('/api/v1/cards/qr-code/validate', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ qr_code_data: scannedData })
  });
  
  const validationResult = await validation.json();
  
  if (validationResult.success) {
    return {
      valid: true,
      cardInfo: validationResult.data
    };
  }
  
  return { valid: false, error: validationResult.message };
}

async function processQRPayment(qrData, paymentDetails) {
  const response = await fetch('/api/v1/cards/qr-code/payment', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      qr_code_data: qrData,
      amount: paymentDetails.amount,
      currency: paymentDetails.currency || 'SZL',
      transaction_type: 'purchase',
      pin: paymentDetails.pin, // Optional
      merchant_info: {
        name: paymentDetails.merchantName,
        category: paymentDetails.category,
        location: paymentDetails.location,
        id: paymentDetails.merchantId
      }
    })
  });
  
  return await response.json();
}
```

## Security Features

### 1. Two-Factor Authentication
```javascript
async function setup2FA() {
  const response = await fetch('/api/v1/security/2fa/setup', {
    method: 'POST',
    headers: getAuthHeaders()
  });
  return await response.json();
}

async function verify2FA(code) {
  const response = await fetch('/api/v1/security/2fa/verify', {
    method: 'POST',
    headers: getAuthHeaders(),
    body: JSON.stringify({ code })
  });
  return await response.json();
}
```

### 2. Device Registration
```javascript
async function registerDevice(deviceInfo) {
  const response = await fetch('/api/v1/security/devices/register', {
    method: 'POST',
    headers: getAuthHeaders(),
    body: JSON.stringify({
      device_name: deviceInfo.name,
      device_type: deviceInfo.type,
      device_id: deviceInfo.id,
      os_version: deviceInfo.osVersion,
      app_version: deviceInfo.appVersion
    })
  });
  return await response.json();
}
```

### 3. Security Settings
```javascript
async function updateSecuritySettings(settings) {
  const response = await fetch('/api/v1/security/settings', {
    method: 'PUT',
    headers: getAuthHeaders(),
    body: JSON.stringify(settings)
  });
  return await response.json();
}
```

## Error Handling

### 1. Comprehensive Error Handler
```javascript
class WalletAPIError extends Error {
  constructor(response) {
    super(response.message);
    this.code = response.code;
    this.error = response.error;
  }
}

async function handleAPIResponse(response) {
  const data = await response.json();
  
  if (!data.success) {
    throw new WalletAPIError(data);
  }
  
  return data;
}

// Usage example
try {
  const result = await transferMoney('+26876543210', '+26876543211', 100, 'Payment');
  console.log('Transfer successful:', result);
} catch (error) {
  if (error instanceof WalletAPIError) {
    switch (error.error) {
      case 'INSUFFICIENT_FUNDS':
        showError('Insufficient funds for this transaction');
        break;
      case 'INVALID_PHONE':
        showError('Invalid phone number format');
        break;
      case 'WALLET_NOT_FOUND':
        showError('Recipient wallet not found');
        break;
      default:
        showError('Transaction failed: ' + error.message);
    }
  } else {
    showError('Network error occurred');
  }
}
```

### 2. Common Error Codes
```javascript
const ERROR_CODES = {
  // Authentication
  'INVALID_CREDENTIALS': 'Invalid phone number or password',
  'TOKEN_EXPIRED': 'Session expired, please login again',
  'UNAUTHORIZED': 'Authentication required',
  
  // Wallet Operations
  'INSUFFICIENT_FUNDS': 'Insufficient wallet balance',
  'WALLET_NOT_FOUND': 'Wallet not found',
  'WALLET_FROZEN': 'Wallet is temporarily frozen',
  
  // Card Operations
  'CARD_NOT_FOUND': 'Card not found',
  'CARD_BLOCKED': 'Card is blocked',
  'SPENDING_LIMIT_EXCEEDED': 'Spending limit exceeded',
  'INVALID_PIN': 'Invalid PIN entered',
  
  // QR Code
  'INVALID_QR_CODE': 'Invalid or expired QR code',
  'QR_EXPIRED': 'QR code has expired',
  'FRAUD_DETECTED': 'Transaction blocked by fraud detection'
};
```

## Real-time Updates

### 1. WebSocket Connection
```javascript
class WalletWebSocket {
  constructor(token) {
    this.token = token;
    this.ws = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
  }
  
  connect() {
    this.ws = new WebSocket(`wss://api.wallet.com/ws?token=${this.token}`);
    
    this.ws.onopen = () => {
      console.log('WebSocket connected');
      this.reconnectAttempts = 0;
    };
    
    this.ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      this.handleMessage(data);
    };
    
    this.ws.onclose = () => {
      this.reconnect();
    };
  }
  
  handleMessage(data) {
    switch (data.type) {
      case 'balance_update':
        this.onBalanceUpdate(data.payload);
        break;
      case 'transaction_notification':
        this.onTransactionNotification(data.payload);
        break;
      case 'security_alert':
        this.onSecurityAlert(data.payload);
        break;
    }
  }
  
  onBalanceUpdate(payload) {
    // Update UI with new balance
    document.getElementById('balance').textContent = payload.new_balance;
  }
  
  onTransactionNotification(payload) {
    // Show transaction notification
    showNotification(`Transaction: ${payload.amount} ${payload.currency}`);
  }
  
  onSecurityAlert(payload) {
    // Show security alert
    showSecurityAlert(payload.message);
  }
}
```

## Best Practices

### 1. Security
- Always use HTTPS in production
- Store tokens securely (consider using secure storage libraries)
- Implement token refresh mechanisms
- Validate all user inputs
- Use CSP headers to prevent XSS

### 2. Performance
- Implement request caching where appropriate
- Use pagination for large data sets
- Implement loading states for better UX
- Debounce search inputs
- Use lazy loading for card lists

### 3. User Experience
- Provide clear error messages
- Implement offline support where possible
- Show loading indicators
- Auto-refresh QR codes
- Implement biometric authentication where available

### 4. Testing
```javascript
// Example test for wallet operations
describe('Wallet Operations', () => {
  test('should transfer money successfully', async () => {
    const result = await transferMoney('+26876543210', '+26876543211', 100, 'Test');
    expect(result.success).toBe(true);
    expect(result.data.transaction_id).toBeDefined();
  });
  
  test('should handle insufficient funds', async () => {
    await expect(
      transferMoney('+26876543210', '+26876543211', 999999, 'Test')
    ).rejects.toThrow('INSUFFICIENT_FUNDS');
  });
});
```

This comprehensive guide covers all major aspects of frontend integration with the wallet platform, including authentication, wallet operations, card management, QR code payments, and security features.
