package services

import (
	"encoding/json"
	"fmt"
	"net"
	"net/http"
	"strings"
	"time"

	"wallet-platform/pkg/logger"

	"gorm.io/gorm"
)

// IPGeolocationService handles IP geolocation and geographic security
type IPGeolocationService struct {
	db     *gorm.DB
	logger *logger.Logger
	client *http.Client
	
	// Configuration
	apiKey    string
	apiURL    string
	cacheTime time.Duration
	
	// In-memory cache for performance
	cache map[string]*GeolocationData
}

// GeolocationData represents IP geolocation information
type GeolocationData struct {
	IP          string    `json:"ip"`
	Country     string    `json:"country"`
	CountryCode string    `json:"country_code"`
	Region      string    `json:"region"`
	RegionCode  string    `json:"region_code"`
	City        string    `json:"city"`
	Latitude    float64   `json:"latitude"`
	Longitude   float64   `json:"longitude"`
	Timezone    string    `json:"timezone"`
	ISP         string    `json:"isp"`
	Organization string   `json:"organization"`
	ASN         string    `json:"asn"`
	
	// Security flags
	IsVPN       bool `json:"is_vpn"`
	IsProxy     bool `json:"is_proxy"`
	IsTor       bool `json:"is_tor"`
	IsHosting   bool `json:"is_hosting"`
	IsMalicious bool `json:"is_malicious"`
	
	// Risk assessment
	RiskScore   float64   `json:"risk_score"`
	RiskLevel   string    `json:"risk_level"`
	CachedAt    time.Time `json:"cached_at"`
}

// GeographicRestriction represents geographic access restrictions
type GeographicRestriction struct {
	Type        string   `json:"type"`         // allow, block
	Countries   []string `json:"countries"`    // Country codes
	Regions     []string `json:"regions"`      // Region codes
	Description string   `json:"description"`
}

// LocationAnalysisResult represents the result of location analysis
type LocationAnalysisResult struct {
	IsAllowed       bool     `json:"is_allowed"`
	RiskLevel       string   `json:"risk_level"`
	RiskScore       float64  `json:"risk_score"`
	Reasons         []string `json:"reasons"`
	GeolocationData *GeolocationData `json:"geolocation_data"`
	Restrictions    []GeographicRestriction `json:"restrictions"`
}

// NewIPGeolocationService creates a new IP geolocation service
func NewIPGeolocationService(db *gorm.DB, log *logger.Logger, apiKey string) *IPGeolocationService {
	return &IPGeolocationService{
		db:        db,
		logger:    log,
		client:    &http.Client{Timeout: 10 * time.Second},
		apiKey:    apiKey,
		apiURL:    "http://ip-api.com/json/", // Free IP geolocation API
		cacheTime: 24 * time.Hour,            // Cache for 24 hours
		cache:     make(map[string]*GeolocationData),
	}
}

// AnalyzeIPLocation analyzes an IP address for geographic security
func (g *IPGeolocationService) AnalyzeIPLocation(ipAddress string, walletID uint) (*LocationAnalysisResult, error) {
	// Get geolocation data
	geoData, err := g.getGeolocationData(ipAddress)
	if err != nil {
		g.logger.LogError(err, map[string]interface{}{
			"action":     "analyze_ip_location",
			"ip_address": ipAddress,
			"wallet_id":  walletID,
		})
		return nil, fmt.Errorf("failed to get geolocation data: %w", err)
	}

	// Get geographic restrictions for the wallet
	restrictions, err := g.getGeographicRestrictions(walletID)
	if err != nil {
		g.logger.LogError(err, map[string]interface{}{
			"action":    "get_geographic_restrictions",
			"wallet_id": walletID,
		})
		// Continue without restrictions if error
		restrictions = []GeographicRestriction{}
	}

	// Analyze location against restrictions and security factors
	result := &LocationAnalysisResult{
		IsAllowed:       true,
		RiskLevel:       "LOW",
		RiskScore:       0.0,
		Reasons:         []string{},
		GeolocationData: geoData,
		Restrictions:    restrictions,
	}

	// Check geographic restrictions
	g.checkGeographicRestrictions(result, geoData, restrictions)

	// Check security flags
	g.checkSecurityFlags(result, geoData)

	// Calculate overall risk score
	g.calculateLocationRiskScore(result, geoData)

	// Log location analysis
	g.logger.LogSecurity("ip_location_analyzed", fmt.Sprintf("%d", walletID), ipAddress,
		fmt.Sprintf("Location: %s, %s - Risk: %s (%.2f)", geoData.City, geoData.Country, result.RiskLevel, result.RiskScore))

	return result, nil
}

// getGeolocationData gets geolocation data for an IP address
func (g *IPGeolocationService) getGeolocationData(ipAddress string) (*GeolocationData, error) {
	// Check if IP is private/local
	if g.isPrivateIP(ipAddress) {
		return &GeolocationData{
			IP:          ipAddress,
			Country:     "Local",
			CountryCode: "LOCAL",
			City:        "Local Network",
			RiskScore:   0.0,
			RiskLevel:   "LOW",
			CachedAt:    time.Now(),
		}, nil
	}

	// Check cache first
	if cached, exists := g.cache[ipAddress]; exists {
		if time.Since(cached.CachedAt) < g.cacheTime {
			return cached, nil
		}
		// Remove expired cache entry
		delete(g.cache, ipAddress)
	}

	// Make API request
	url := fmt.Sprintf("%s%s?fields=status,message,country,countryCode,region,regionName,city,lat,lon,timezone,isp,org,as,proxy,hosting", g.apiURL, ipAddress)
	
	resp, err := g.client.Get(url)
	if err != nil {
		return nil, fmt.Errorf("failed to make geolocation request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("geolocation API returned status: %d", resp.StatusCode)
	}

	// Parse response
	var apiResponse struct {
		Status      string  `json:"status"`
		Message     string  `json:"message"`
		Country     string  `json:"country"`
		CountryCode string  `json:"countryCode"`
		Region      string  `json:"region"`
		RegionName  string  `json:"regionName"`
		City        string  `json:"city"`
		Lat         float64 `json:"lat"`
		Lon         float64 `json:"lon"`
		Timezone    string  `json:"timezone"`
		ISP         string  `json:"isp"`
		Org         string  `json:"org"`
		AS          string  `json:"as"`
		Proxy       bool    `json:"proxy"`
		Hosting     bool    `json:"hosting"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&apiResponse); err != nil {
		return nil, fmt.Errorf("failed to decode geolocation response: %w", err)
	}

	if apiResponse.Status != "success" {
		return nil, fmt.Errorf("geolocation API error: %s", apiResponse.Message)
	}

	// Create geolocation data
	geoData := &GeolocationData{
		IP:           ipAddress,
		Country:      apiResponse.Country,
		CountryCode:  apiResponse.CountryCode,
		Region:       apiResponse.RegionName,
		RegionCode:   apiResponse.Region,
		City:         apiResponse.City,
		Latitude:     apiResponse.Lat,
		Longitude:    apiResponse.Lon,
		Timezone:     apiResponse.Timezone,
		ISP:          apiResponse.ISP,
		Organization: apiResponse.Org,
		ASN:          apiResponse.AS,
		IsProxy:      apiResponse.Proxy,
		IsHosting:    apiResponse.Hosting,
		CachedAt:     time.Now(),
	}

	// Detect VPN/Tor (basic detection based on ISP/Org names)
	g.detectVPNTor(geoData)

	// Calculate initial risk score
	g.calculateGeoRiskScore(geoData)

	// Cache the result
	g.cache[ipAddress] = geoData

	return geoData, nil
}

// checkGeographicRestrictions checks if location violates geographic restrictions
func (g *IPGeolocationService) checkGeographicRestrictions(result *LocationAnalysisResult, geoData *GeolocationData, restrictions []GeographicRestriction) {
	for _, restriction := range restrictions {
		switch restriction.Type {
		case "block":
			// Check if country is in block list
			for _, country := range restriction.Countries {
				if strings.EqualFold(geoData.CountryCode, country) {
					result.IsAllowed = false
					result.RiskScore += 10.0
					result.Reasons = append(result.Reasons, fmt.Sprintf("Access blocked from %s", geoData.Country))
					break
				}
			}
		case "allow":
			// Check if country is NOT in allow list
			allowed := false
			for _, country := range restriction.Countries {
				if strings.EqualFold(geoData.CountryCode, country) {
					allowed = true
					break
				}
			}
			if !allowed && len(restriction.Countries) > 0 {
				result.IsAllowed = false
				result.RiskScore += 8.0
				result.Reasons = append(result.Reasons, fmt.Sprintf("Access not allowed from %s", geoData.Country))
			}
		}
	}
}

// checkSecurityFlags checks security-related flags
func (g *IPGeolocationService) checkSecurityFlags(result *LocationAnalysisResult, geoData *GeolocationData) {
	if geoData.IsTor {
		result.RiskScore += 9.0
		result.Reasons = append(result.Reasons, "Tor network detected")
	}

	if geoData.IsVPN {
		result.RiskScore += 6.0
		result.Reasons = append(result.Reasons, "VPN detected")
	}

	if geoData.IsProxy {
		result.RiskScore += 5.0
		result.Reasons = append(result.Reasons, "Proxy detected")
	}

	if geoData.IsHosting {
		result.RiskScore += 4.0
		result.Reasons = append(result.Reasons, "Hosting provider detected")
	}

	if geoData.IsMalicious {
		result.RiskScore += 10.0
		result.Reasons = append(result.Reasons, "Malicious IP detected")
	}
}

// calculateLocationRiskScore calculates overall location risk score
func (g *IPGeolocationService) calculateLocationRiskScore(result *LocationAnalysisResult, geoData *GeolocationData) {
	// Add base geolocation risk
	result.RiskScore += geoData.RiskScore

	// Determine risk level
	if result.RiskScore >= 9.0 {
		result.RiskLevel = "CRITICAL"
	} else if result.RiskScore >= 7.0 {
		result.RiskLevel = "HIGH"
	} else if result.RiskScore >= 4.0 {
		result.RiskLevel = "MEDIUM"
	} else if result.RiskScore >= 2.0 {
		result.RiskLevel = "LOW"
	} else {
		result.RiskLevel = "MINIMAL"
	}
}

// detectVPNTor detects VPN and Tor usage based on ISP/Organization names
func (g *IPGeolocationService) detectVPNTor(geoData *GeolocationData) {
	ispLower := strings.ToLower(geoData.ISP)
	orgLower := strings.ToLower(geoData.Organization)

	// VPN detection keywords
	vpnKeywords := []string{"vpn", "virtual private", "proxy", "tunnel", "anonymizer", "hide", "mask"}
	for _, keyword := range vpnKeywords {
		if strings.Contains(ispLower, keyword) || strings.Contains(orgLower, keyword) {
			geoData.IsVPN = true
			break
		}
	}

	// Tor detection keywords
	torKeywords := []string{"tor", "onion", "relay"}
	for _, keyword := range torKeywords {
		if strings.Contains(ispLower, keyword) || strings.Contains(orgLower, keyword) {
			geoData.IsTor = true
			break
		}
	}
}

// calculateGeoRiskScore calculates risk score based on geographic factors
func (g *IPGeolocationService) calculateGeoRiskScore(geoData *GeolocationData) {
	score := 0.0

	// High-risk countries (example list)
	highRiskCountries := []string{"CN", "RU", "KP", "IR"}
	for _, country := range highRiskCountries {
		if geoData.CountryCode == country {
			score += 3.0
			break
		}
	}

	// Security flags
	if geoData.IsTor {
		score += 5.0
	}
	if geoData.IsVPN {
		score += 3.0
	}
	if geoData.IsProxy {
		score += 2.0
	}
	if geoData.IsHosting {
		score += 1.0
	}

	geoData.RiskScore = score

	// Set risk level
	if score >= 5.0 {
		geoData.RiskLevel = "HIGH"
	} else if score >= 3.0 {
		geoData.RiskLevel = "MEDIUM"
	} else if score >= 1.0 {
		geoData.RiskLevel = "LOW"
	} else {
		geoData.RiskLevel = "MINIMAL"
	}
}

// isPrivateIP checks if an IP address is private/local
func (g *IPGeolocationService) isPrivateIP(ipStr string) bool {
	ip := net.ParseIP(ipStr)
	if ip == nil {
		return false
	}

	// Check for private IP ranges
	privateRanges := []string{
		"10.0.0.0/8",
		"**********/12",
		"***********/16",
		"*********/8",
		"***********/16",
		"::1/128",
		"fc00::/7",
		"fe80::/10",
	}

	for _, rangeStr := range privateRanges {
		_, network, err := net.ParseCIDR(rangeStr)
		if err != nil {
			continue
		}
		if network.Contains(ip) {
			return true
		}
	}

	return false
}

// getGeographicRestrictions gets geographic restrictions for a wallet
func (g *IPGeolocationService) getGeographicRestrictions(walletID uint) ([]GeographicRestriction, error) {
	// This would typically load from database or configuration
	// For now, return empty restrictions
	return []GeographicRestriction{}, nil
}
