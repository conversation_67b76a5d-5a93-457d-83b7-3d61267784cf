package services

import (
	"fmt"
	"time"

	"wallet-platform/internal/models"
	"wallet-platform/pkg/logger"
	"wallet-platform/pkg/redis"

	"github.com/google/uuid"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// SecurityService implements the SecurityServiceInterface
type SecurityService struct {
	db     *gorm.DB
	redis  *redis.Client
	logger *logger.Logger
}

// NewSecurityService creates a new security service
func NewSecurityService(db *gorm.DB, redisClient *redis.Client, log *logger.Logger) *SecurityService {
	return &SecurityService{
		db:     db,
		redis:  redisClient,
		logger: log,
	}
}

// RegisterDevice registers a new device for a wallet
func (s *SecurityService) RegisterDevice(walletID uint, deviceInfo map[string]interface{}) (*DeviceRegistration, error) {
	// Generate device fingerprint if not provided
	deviceFingerprint := deviceInfo["fingerprint"].(string)
	if deviceFingerprint == "" {
		deviceFingerprint = s.generateDeviceFingerprint(deviceInfo)
	}

	// Check if device already exists
	var existingDevice models.DeviceRegistration
	if err := s.db.Where("wallet_id = ? AND device_fingerprint = ?", walletID, deviceFingerprint).First(&existingDevice).Error; err == nil {
		return &DeviceRegistration{
			ID:                existingDevice.ID,
			WalletID:          existingDevice.WalletID,
			DeviceFingerprint: existingDevice.DeviceFingerprint,
			DeviceName:        existingDevice.DeviceName,
			DeviceType:        existingDevice.DeviceType,
			IsTrusted:         existingDevice.IsTrusted,
			IsVerified:        existingDevice.IsVerified,
		}, nil
	}

	// Create new device registration
	verificationCode := s.generateVerificationCode()
	device := models.DeviceRegistration{
		WalletID:          walletID,
		DeviceFingerprint: deviceFingerprint,
		DeviceName:        deviceInfo["name"].(string),
		DeviceType:        deviceInfo["type"].(string),
		OSName:            deviceInfo["os"].(string),
		IPAddress:         deviceInfo["ip"].(string),
		UserAgent:         deviceInfo["user_agent"].(string),
		IsTrusted:         false,
		IsVerified:        false,
		VerificationCode:  verificationCode,
		TrustLevel:        "low",
	}

	if err := s.db.Create(&device).Error; err != nil {
		s.logger.LogError(err, map[string]interface{}{
			"action":    "register_device",
			"wallet_id": walletID,
		})
		return nil, fmt.Errorf("failed to register device: %w", err)
	}

	s.logger.LogSecurity("device_registered", fmt.Sprintf("%d", walletID), deviceInfo["ip"].(string), fmt.Sprintf("Device: %s", device.DeviceName))

	return &DeviceRegistration{
		ID:                device.ID,
		WalletID:          device.WalletID,
		DeviceFingerprint: device.DeviceFingerprint,
		DeviceName:        device.DeviceName,
		DeviceType:        device.DeviceType,
		IsTrusted:         device.IsTrusted,
		IsVerified:        device.IsVerified,
	}, nil
}

// VerifyDevice verifies a device using verification code
func (s *SecurityService) VerifyDevice(deviceFingerprint, verificationCode string) error {
	var device models.DeviceRegistration
	if err := s.db.Where("device_fingerprint = ? AND verification_code = ?", deviceFingerprint, verificationCode).First(&device).Error; err != nil {
		return fmt.Errorf("invalid verification code")
	}

	// Check if verification code is expired (24 hours)
	if time.Since(device.CreatedAt) > 24*time.Hour {
		return fmt.Errorf("verification code expired")
	}

	// Update device as verified
	updates := map[string]interface{}{
		"is_verified":       true,
		"is_trusted":        true,
		"trust_level":       "medium",
		"verification_code": "",
		"verified_at":       time.Now(),
	}

	if err := s.db.Model(&device).Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to verify device: %w", err)
	}

	s.logger.LogSecurity("device_verified", fmt.Sprintf("%d", device.WalletID), device.IPAddress, fmt.Sprintf("Device: %s", device.DeviceName))

	return nil
}

// Setup2FA sets up two-factor authentication for a wallet
func (s *SecurityService) Setup2FA(walletID uint, method string, contact string) error {
	// Check if 2FA already exists
	var existing models.TwoFactorAuth
	if err := s.db.Where("wallet_id = ? AND method = ?", walletID, method).First(&existing).Error; err == nil {
		return fmt.Errorf("2FA already set up for this method")
	}

	// Generate secret for TOTP or store contact for SMS/Email
	secret := s.generate2FASecret()

	twoFA := models.TwoFactorAuth{
		WalletID:    walletID,
		Method:      method,
		Secret:      secret,
		IsEnabled:   true,
		BackupCodes: datatypes.JSON("[]"),
	}

	if err := s.db.Create(&twoFA).Error; err != nil {
		s.logger.LogError(err, map[string]interface{}{
			"action":    "setup_2fa",
			"wallet_id": walletID,
			"method":    method,
		})
		return fmt.Errorf("failed to setup 2FA: %w", err)
	}

	s.logger.LogSecurity("2fa_setup", fmt.Sprintf("%d", walletID), "", fmt.Sprintf("Method: %s", method))

	return nil
}

// Verify2FA verifies a 2FA code
func (s *SecurityService) Verify2FA(walletID uint, code string) error {
	var twoFA models.TwoFactorAuth
	if err := s.db.Where("wallet_id = ? AND is_enabled = true", walletID).First(&twoFA).Error; err != nil {
		return fmt.Errorf("2FA not set up")
	}

	// TODO: Implement actual code verification based on method
	// For now, just check if code is not empty
	if code == "" {
		return fmt.Errorf("invalid 2FA code")
	}

	// Update last verified time
	if err := s.db.Model(&twoFA).Update("last_verified_at", time.Now()).Error; err != nil {
		return fmt.Errorf("failed to update 2FA verification: %w", err)
	}

	s.logger.LogSecurity("2fa_verified", fmt.Sprintf("%d", walletID), "", "2FA code verified")

	return nil
}

// LogSecurityEvent logs a security event
func (s *SecurityService) LogSecurityEvent(walletID uint, eventType, description string, metadata map[string]interface{}) error {
	event := models.SecurityEvent{
		WalletID:    &walletID,
		EventType:   eventType,
		Description: description,
		Severity:    s.getSeverityLevel(eventType),
		IPAddress:   metadata["ip"].(string),
		UserAgent:   metadata["user_agent"].(string),
		Metadata:    datatypes.JSON("{}"),
	}

	if err := s.db.Create(&event).Error; err != nil {
		s.logger.LogError(err, map[string]interface{}{
			"action":     "log_security_event",
			"wallet_id":  walletID,
			"event_type": eventType,
		})
		return fmt.Errorf("failed to log security event: %w", err)
	}

	s.logger.LogSecurity(eventType, fmt.Sprintf("%d", walletID), metadata["ip"].(string), description)

	return nil
}

// CheckFraud performs fraud detection on transaction data
func (s *SecurityService) CheckFraud(walletID uint, transactionData map[string]interface{}) (*FraudAlert, error) {
	// Simple fraud detection logic
	riskScore := s.calculateRiskScore(walletID, transactionData)
	riskLevel := s.getRiskLevel(riskScore)
	reasons := s.generateRiskReasons(transactionData, riskScore)

	if riskScore > 0.7 { // High risk threshold
		alert := models.FraudAlert{
			WalletID:       walletID,
			RiskLevel:      riskLevel,
			RiskScore:      riskScore,
			Reasons:        datatypes.JSON("[]"),
			Recommendation: s.getRecommendation(riskLevel),
			Status:         "pending",
			AlertType:      "transaction_fraud",
		}

		if err := s.db.Create(&alert).Error; err != nil {
			return nil, fmt.Errorf("failed to create fraud alert: %w", err)
		}

		s.logger.LogFraud(fmt.Sprintf("%d", alert.ID), fmt.Sprintf("%d", walletID), riskLevel, riskScore, reasons)

		return &FraudAlert{
			ID:             alert.ID,
			WalletID:       alert.WalletID,
			RiskLevel:      alert.RiskLevel,
			RiskScore:      alert.RiskScore,
			Reasons:        []string{},
			Recommendation: alert.Recommendation,
			Status:         alert.Status,
		}, nil
	}

	return nil, nil // No fraud detected
}

// UpdateSecuritySettings updates security settings for a wallet
func (s *SecurityService) UpdateSecuritySettings(walletID uint, settings map[string]interface{}) error {
	var securitySettings models.SecuritySettings
	if err := s.db.Where("wallet_id = ?", walletID).First(&securitySettings).Error; err != nil {
		// Create new settings if not exists
		securitySettings = models.SecuritySettings{
			WalletID: walletID,
		}
	}

	if err := s.db.Model(&securitySettings).Updates(settings).Error; err != nil {
		return fmt.Errorf("failed to update security settings: %w", err)
	}

	s.logger.LogSecurity("security_settings_updated", fmt.Sprintf("%d", walletID), "", "Security settings updated")

	return nil
}

// GetSecuritySettings retrieves security settings for a wallet
func (s *SecurityService) GetSecuritySettings(walletID uint) (*SecuritySettings, error) {
	var settings models.SecuritySettings
	if err := s.db.Where("wallet_id = ?", walletID).First(&settings).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			// Return default settings
			return &SecuritySettings{
				WalletID:              walletID,
				RequireDeviceAuth:     true,
				Require2FAForTransfer: false,
				Require2FAForCards:    false,
				SessionTimeout:        3600,
			}, nil
		}
		return nil, fmt.Errorf("failed to get security settings: %w", err)
	}

	return &SecuritySettings{
		ID:                    settings.ID,
		WalletID:              settings.WalletID,
		RequireDeviceAuth:     settings.RequireDeviceAuth,
		Require2FAForTransfer: settings.Require2FAForTransfer,
		Require2FAForCards:    settings.Require2FAForCards,
		SessionTimeout:        settings.SessionTimeout,
	}, nil
}

// GetAllSecurityEvents retrieves all security events with pagination and filtering (admin only)
func (s *SecurityService) GetAllSecurityEvents(page, limit int, eventType, severity, startDate, endDate string) ([]models.SecurityEvent, int64, error) {
	var events []models.SecurityEvent
	var total int64

	query := s.db.Model(&models.SecurityEvent{})

	// Apply filters
	if eventType != "" {
		query = query.Where("event_type = ?", eventType)
	}
	if severity != "" {
		query = query.Where("severity = ?", severity)
	}
	if startDate != "" {
		query = query.Where("created_at >= ?", startDate)
	}
	if endDate != "" {
		query = query.Where("created_at <= ?", endDate)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		s.logger.LogError(err, map[string]interface{}{
			"action":     "get_all_security_events_count",
			"event_type": eventType,
			"severity":   severity,
		})
		return nil, 0, fmt.Errorf("failed to count security events: %w", err)
	}

	// Apply pagination and ordering
	offset := (page - 1) * limit
	if err := query.Order("created_at DESC").Offset(offset).Limit(limit).Find(&events).Error; err != nil {
		s.logger.LogError(err, map[string]interface{}{
			"action": "get_all_security_events",
			"page":   page,
			"limit":  limit,
		})
		return nil, 0, fmt.Errorf("failed to get security events: %w", err)
	}

	return events, total, nil
}

// GetAllFraudAlerts retrieves all fraud alerts with pagination and filtering (admin only)
func (s *SecurityService) GetAllFraudAlerts(page, limit int, status, severity, alertType string) ([]models.FraudAlert, int64, error) {
	var alerts []models.FraudAlert
	var total int64

	query := s.db.Model(&models.FraudAlert{})

	// Apply filters
	if status != "" {
		query = query.Where("status = ?", status)
	}
	if severity != "" {
		query = query.Where("risk_level = ?", severity)
	}
	if alertType != "" {
		query = query.Where("alert_type = ?", alertType)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		s.logger.LogError(err, map[string]interface{}{
			"action":     "get_all_fraud_alerts_count",
			"status":     status,
			"severity":   severity,
			"alert_type": alertType,
		})
		return nil, 0, fmt.Errorf("failed to count fraud alerts: %w", err)
	}

	// Apply pagination and ordering
	offset := (page - 1) * limit
	if err := query.Order("created_at DESC").Offset(offset).Limit(limit).Find(&alerts).Error; err != nil {
		s.logger.LogError(err, map[string]interface{}{
			"action": "get_all_fraud_alerts",
			"page":   page,
			"limit":  limit,
		})
		return nil, 0, fmt.Errorf("failed to get fraud alerts: %w", err)
	}

	return alerts, total, nil
}

// UpdateFraudAlert updates a fraud alert (admin only)
func (s *SecurityService) UpdateFraudAlert(alertID uint, updates map[string]interface{}) (*models.FraudAlert, error) {
	var alert models.FraudAlert
	if err := s.db.First(&alert, alertID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("fraud alert not found")
		}
		return nil, fmt.Errorf("failed to find fraud alert: %w", err)
	}

	if err := s.db.Model(&alert).Updates(updates).Error; err != nil {
		s.logger.LogError(err, map[string]interface{}{
			"action":   "update_fraud_alert",
			"alert_id": alertID,
			"updates":  updates,
		})
		return nil, fmt.Errorf("failed to update fraud alert: %w", err)
	}

	return &alert, nil
}

// Helper methods
func (s *SecurityService) generateDeviceFingerprint(deviceInfo map[string]interface{}) string {
	// Simple fingerprint generation - in production, use more sophisticated method
	return fmt.Sprintf("%s_%s_%s", deviceInfo["type"], deviceInfo["os"], deviceInfo["model"])
}

func (s *SecurityService) generateVerificationCode() string {
	// Generate 6-digit verification code
	return fmt.Sprintf("%06d", time.Now().Unix()%1000000)
}

func (s *SecurityService) generate2FASecret() string {
	return uuid.New().String()
}

func (s *SecurityService) generateBackupCodes() []string {
	codes := make([]string, 10)
	for i := 0; i < 10; i++ {
		codes[i] = fmt.Sprintf("%08d", time.Now().UnixNano()%*********)
	}
	return codes
}

func (s *SecurityService) getSeverityLevel(eventType string) string {
	switch eventType {
	case "login_failed", "device_registered":
		return "low"
	case "suspicious_transaction", "multiple_failed_attempts":
		return "medium"
	case "fraud_detected", "account_compromise":
		return "high"
	default:
		return "low"
	}
}

func (s *SecurityService) calculateRiskScore(walletID uint, transactionData map[string]interface{}) float64 {
	// Simple risk calculation - in production, use ML models
	score := 0.0

	// Check transaction amount
	if amount, ok := transactionData["amount"].(float64); ok {
		if amount > 50000 {
			score += 0.3
		}
	}

	// Check time of transaction
	hour := time.Now().Hour()
	if hour < 6 || hour > 22 {
		score += 0.2
	}

	// Check location (placeholder)
	if ip, ok := transactionData["ip"].(string); ok && ip != "" {
		// In production, check against known good IPs
		score += 0.1
	}

	return score
}

func (s *SecurityService) getRiskLevel(score float64) string {
	if score < 0.3 {
		return "low"
	} else if score < 0.7 {
		return "medium"
	}
	return "high"
}

func (s *SecurityService) generateRiskReasons(transactionData map[string]interface{}, score float64) []string {
	var reasons []string

	if amount, ok := transactionData["amount"].(float64); ok && amount > 50000 {
		reasons = append(reasons, "High transaction amount")
	}

	hour := time.Now().Hour()
	if hour < 6 || hour > 22 {
		reasons = append(reasons, "Unusual transaction time")
	}

	if score > 0.5 {
		reasons = append(reasons, "Multiple risk factors detected")
	}

	return reasons
}

func (s *SecurityService) getRecommendation(riskLevel string) string {
	switch riskLevel {
	case "low":
		return "Monitor transaction"
	case "medium":
		return "Require additional verification"
	case "high":
		return "Block transaction and investigate"
	default:
		return "Review manually"
	}
}

func (s *SecurityService) getConfidenceLevel(score float64) string {
	if score > 0.8 {
		return "high"
	} else if score > 0.5 {
		return "medium"
	}
	return "low"
}
