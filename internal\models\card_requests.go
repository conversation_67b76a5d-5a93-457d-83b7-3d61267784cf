package models

// Card-specific request and response models

// PhysicalCardActivationRequest represents physical card activation
type PhysicalCardActivationRequest struct {
	SerialNumber     string `json:"serial_number" binding:"required"`
	ActivationCode   string `json:"activation_code" binding:"required"`
	CardholderName   string `json:"cardholder_name" binding:"required"`
	DeliveryAddress  string `json:"delivery_address"`
	VerificationCode string `json:"verification_code"`
}

// MerchantWhitelistRequest represents merchant whitelist addition
type MerchantW<PERSON>elistRequest struct {
	MerchantID   string `json:"merchant_id" binding:"required"`
	MerchantName string `json:"merchant_name" binding:"required"`
	Category     string `json:"category"`
	Description  string `json:"description"`
	IsActive     bool   `json:"is_active"`
}

// SpendingControlsRequest represents spending controls configuration
type SpendingControlsRequest struct {
	DailyLimit     *float64  `json:"daily_limit" binding:"omitempty,min=0"`
	MonthlyLimit   *float64  `json:"monthly_limit" binding:"omitempty,min=0"`
	PerTxnLimit    *float64  `json:"per_txn_limit" binding:"omitempty,min=0"`
	Categories     []string  `json:"categories"`
	BlockedMerchants []string `json:"blocked_merchants"`
	AllowedMerchants []string `json:"allowed_merchants"`
	AllowOnline    *bool     `json:"allow_online"`
	AllowATM       *bool     `json:"allow_atm"`
	AllowContactless *bool   `json:"allow_contactless"`
	AllowInternational *bool `json:"allow_international"`
	TimeRestrictions *TimeRestrictions `json:"time_restrictions"`
}

// TimeRestrictions represents time-based spending restrictions
type TimeRestrictions struct {
	AllowedDays  []string `json:"allowed_days"` // Monday, Tuesday, etc.
	StartTime    string   `json:"start_time"`   // HH:MM format
	EndTime      string   `json:"end_time"`     // HH:MM format
	Timezone     string   `json:"timezone"`
}

// CardTokenRequest represents card tokenization request
type CardTokenRequest struct {
	Purpose     string                 `json:"purpose" binding:"required,oneof=payment subscription recurring"`
	ExpiresIn   int                    `json:"expires_in" binding:"min=300,max=86400"` // 5 minutes to 24 hours
	Metadata    map[string]interface{} `json:"metadata"`
	Restrictions *TokenRestrictions    `json:"restrictions"`
}

// TokenRestrictions represents token usage restrictions
type TokenRestrictions struct {
	MaxAmount     *float64  `json:"max_amount"`
	MaxUses       *int      `json:"max_uses"`
	AllowedMerchants []string `json:"allowed_merchants"`
	BlockedMerchants []string `json:"blocked_merchants"`
}

// ContactlessPaymentRequest represents contactless payment
type ContactlessPaymentRequest struct {
	CardToken   string                 `json:"card_token" binding:"required"`
	Amount      float64                `json:"amount" binding:"required,gt=0"`
	Currency    string                 `json:"currency" binding:"required,len=3"`
	MerchantID  string                 `json:"merchant_id" binding:"required"`
	MerchantName string                `json:"merchant_name"`
	Description string                 `json:"description"`
	Metadata    map[string]interface{} `json:"metadata"`
	Location    *PaymentLocation       `json:"location"`
}

// PaymentLocation represents payment location data
type PaymentLocation struct {
	Latitude  float64 `json:"latitude" binding:"required"`
	Longitude float64 `json:"longitude" binding:"required"`
	Address   string  `json:"address"`
	City      string  `json:"city"`
	Country   string  `json:"country"`
}

// CardResponse represents card data in responses
type CardResponse struct {
	ID               uint                   `json:"id"`
	WalletID         uint                   `json:"wallet_id"`
	CardType         string                 `json:"card_type"`
	CardNumber       string                 `json:"card_number"` // Masked
	ExpiryMonth      int                    `json:"expiry_month"`
	ExpiryYear       int                    `json:"expiry_year"`
	CardholderName   string                 `json:"cardholder_name"`
	Status           string                 `json:"status"`
	SpendingLimit    float64                `json:"spending_limit"`
	DailySpent       float64                `json:"daily_spent"`
	MonthlySpent     float64                `json:"monthly_spent"`
	IsPhysical       bool                   `json:"is_physical"`
	IsActive         bool                   `json:"is_active"`
	IsBlocked        bool                   `json:"is_blocked"`
	BlockReason      string                 `json:"block_reason,omitempty"`
	Metadata         map[string]interface{} `json:"metadata"`
	CreatedAt        string                 `json:"created_at"`
	UpdatedAt        string                 `json:"updated_at"`
	LastUsed         string                 `json:"last_used,omitempty"`
}

// CardTokenResponse represents card token data
type CardTokenResponse struct {
	Token       string                 `json:"token"`
	CardID      uint                   `json:"card_id"`
	Purpose     string                 `json:"purpose"`
	ExpiresAt   string                 `json:"expires_at"`
	IsActive    bool                   `json:"is_active"`
	UsageCount  int                    `json:"usage_count"`
	MaxUses     int                    `json:"max_uses,omitempty"`
	Metadata    map[string]interface{} `json:"metadata"`
	CreatedAt   string                 `json:"created_at"`
}

// PaymentResponse represents payment transaction response
type PaymentResponse struct {
	TransactionID   string                 `json:"transaction_id"`
	CardID          uint                   `json:"card_id"`
	Amount          float64                `json:"amount"`
	Currency        string                 `json:"currency"`
	MerchantID      string                 `json:"merchant_id"`
	MerchantName    string                 `json:"merchant_name"`
	Status          string                 `json:"status"`
	AuthCode        string                 `json:"auth_code,omitempty"`
	ProcessedAt     string                 `json:"processed_at"`
	Description     string                 `json:"description"`
	Metadata        map[string]interface{} `json:"metadata"`
	Location        *PaymentLocation       `json:"location,omitempty"`
}

// SpendingControlsResponse represents current spending controls
type SpendingControlsResponse struct {
	CardID           uint                   `json:"card_id"`
	DailyLimit       float64                `json:"daily_limit"`
	MonthlyLimit     float64                `json:"monthly_limit"`
	PerTxnLimit      float64                `json:"per_txn_limit"`
	DailySpent       float64                `json:"daily_spent"`
	MonthlySpent     float64                `json:"monthly_spent"`
	Categories       []string               `json:"categories"`
	BlockedMerchants []string               `json:"blocked_merchants"`
	AllowedMerchants []string               `json:"allowed_merchants"`
	AllowOnline      bool                   `json:"allow_online"`
	AllowATM         bool                   `json:"allow_atm"`
	AllowContactless bool                   `json:"allow_contactless"`
	AllowInternational bool                 `json:"allow_international"`
	TimeRestrictions *TimeRestrictions      `json:"time_restrictions,omitempty"`
	UpdatedAt        string                 `json:"updated_at"`
}

// MerchantWhitelistResponse represents merchant whitelist entry
type MerchantWhitelistResponse struct {
	ID           uint   `json:"id"`
	CardID       uint   `json:"card_id"`
	MerchantID   string `json:"merchant_id"`
	MerchantName string `json:"merchant_name"`
	Category     string `json:"category"`
	Description  string `json:"description"`
	IsActive     bool   `json:"is_active"`
	AddedAt      string `json:"added_at"`
	LastUsed     string `json:"last_used,omitempty"`
}

// CardTransactionResponse represents card transaction data
type CardTransactionResponse struct {
	ID              uint                   `json:"id"`
	CardID          uint                   `json:"card_id"`
	TransactionID   string                 `json:"transaction_id"`
	Type            string                 `json:"type"`
	Amount          float64                `json:"amount"`
	Currency        string                 `json:"currency"`
	Status          string                 `json:"status"`
	MerchantID      string                 `json:"merchant_id,omitempty"`
	MerchantName    string                 `json:"merchant_name,omitempty"`
	Description     string                 `json:"description"`
	AuthCode        string                 `json:"auth_code,omitempty"`
	ProcessedAt     string                 `json:"processed_at"`
	Location        *PaymentLocation       `json:"location,omitempty"`
	Metadata        map[string]interface{} `json:"metadata"`
}

// CardStatsResponse represents card usage statistics
type CardStatsResponse struct {
	CardID              uint    `json:"card_id"`
	TotalTransactions   int     `json:"total_transactions"`
	TotalSpent          float64 `json:"total_spent"`
	DailySpent          float64 `json:"daily_spent"`
	WeeklySpent         float64 `json:"weekly_spent"`
	MonthlySpent        float64 `json:"monthly_spent"`
	AverageTransaction  float64 `json:"average_transaction"`
	LargestTransaction  float64 `json:"largest_transaction"`
	MostUsedMerchant    string  `json:"most_used_merchant"`
	LastTransactionDate string  `json:"last_transaction_date"`
	SpendingTrend       string  `json:"spending_trend"` // increasing, decreasing, stable
}
