package controllers

import (
	"crypto/rand"
	"fmt"
	"net/http"
	"time"

	"wallet-platform/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"golang.org/x/crypto/bcrypt"
)

// AuthController handles authentication-related requests
type AuthController struct {
	container *services.Container
}

// NewAuthController creates a new auth controller
func NewAuthController(container *services.Container) *AuthController {
	return &AuthController{
		container: container,
	}
}

// LoginRequest represents login request payload
type LoginRequest struct {
	PhoneNumber string                 `json:"phone_number" binding:"required"`
	Password    string                 `json:"password" binding:"required"`
	DeviceInfo  map[string]interface{} `json:"device_info"`
}

// RegisterRequest represents registration request payload
type RegisterRequest struct {
	PhoneNumber string                 `json:"phone_number" binding:"required"`
	Password    string                 `json:"password" binding:"required,min=8"`
	FirstName   string                 `json:"first_name" binding:"required"`
	LastName    string                 `json:"last_name" binding:"required"`
	Email       string                 `json:"email" binding:"required,email"`
	DeviceInfo  map[string]interface{} `json:"device_info"`
}

// VerifyAccountRequest represents account verification request
type VerifyAccountRequest struct {
	PhoneNumber      string `json:"phone_number" binding:"required"`
	VerificationCode string `json:"verification_code" binding:"required"`
}

// ForgotPasswordRequest represents forgot password request
type ForgotPasswordRequest struct {
	PhoneNumber string `json:"phone_number" binding:"required"`
}

// ResetPasswordRequest represents reset password request
type ResetPasswordRequest struct {
	PhoneNumber string `json:"phone_number" binding:"required"`
	ResetCode   string `json:"reset_code" binding:"required"`
	NewPassword string `json:"new_password" binding:"required,min=8"`
}

// Login authenticates a user and returns a JWT token
func (ac *AuthController) Login(c *gin.Context) {
	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	// Get user by phone number
	user, err := ac.container.UserService.GetUserByPhoneNumber(req.PhoneNumber)
	if err != nil {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "INVALID_CREDENTIALS",
			Message: "Invalid phone number or password",
			Code:    "401",
		})
		return
	}

	// Verify password
	if !ac.verifyPassword(req.Password, user.PasswordHash) {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "INVALID_CREDENTIALS",
			Message: "Invalid phone number or password",
			Code:    "401",
		})
		return
	}

	// Check if account is verified
	if !user.IsVerified {
		c.JSON(http.StatusForbidden, ErrorResponse{
			Error:   "ACCOUNT_NOT_VERIFIED",
			Message: "Please verify your account before logging in",
			Code:    "403",
		})
		return
	}

	// Update login info
	ac.container.UserService.UpdateLoginInfo(user.ID)

	// Generate JWT token
	token := ac.generateJWTToken(user.ID)

	// Log security event
	if req.DeviceInfo != nil {
		req.DeviceInfo["ip"] = c.ClientIP()
		req.DeviceInfo["user_agent"] = c.GetHeader("User-Agent")
		ac.container.SecurityService.LogSecurityEvent(user.WalletID, "login", "User logged in", req.DeviceInfo)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Login successful",
		"data": gin.H{
			"token":      token,
			"user_id":    user.ID,
			"wallet_id":  user.WalletID,
			"expires_in": 3600, // 1 hour
		},
	})
}

// Register creates a new user account
func (ac *AuthController) Register(c *gin.Context) {
	var req RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	// Check if phone number already exists
	existingUser, _ := ac.container.UserService.GetUserByPhoneNumber(req.PhoneNumber)
	if existingUser != nil {
		c.JSON(http.StatusConflict, ErrorResponse{
			Error:   "PHONE_EXISTS",
			Message: "Phone number already registered",
			Code:    "409",
		})
		return
	}

	// Check if email already exists
	existingEmailUser, _ := ac.container.UserService.GetUserByEmail(req.Email)
	if existingEmailUser != nil {
		c.JSON(http.StatusConflict, ErrorResponse{
			Error:   "EMAIL_EXISTS",
			Message: "Email already registered",
			Code:    "409",
		})
		return
	}

	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "REGISTRATION_FAILED",
			Message: "Failed to process registration",
			Code:    "500",
		})
		return
	}

	// Create user with wallet
	user, err := ac.container.UserService.CreateUser(
		req.PhoneNumber,
		"individual",
		req.FirstName,
		req.LastName,
		req.Email,
		string(hashedPassword),
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "REGISTRATION_FAILED",
			Message: err.Error(),
			Code:    "500",
		})
		return
	}

	// Generate verification code
	verificationCode := ac.generateVerificationCode()

	// Store verification code
	err = ac.container.UserService.SetVerificationCode(user.ID, verificationCode)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "VERIFICATION_FAILED",
			Message: "Failed to send verification code",
			Code:    "500",
		})
		return
	}

	// TODO: Send SMS verification code
	// ac.container.NotificationService.SendSMS(req.PhoneNumber, "Your verification code is: " + verificationCode)

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "Registration successful. Please verify your account with the code sent to your phone.",
		"data": gin.H{
			"user_id":      user.ID,
			"wallet_id":    user.WalletID,
			"phone_number": req.PhoneNumber,
		},
	})
}

// VerifyAccount verifies a user's account with verification code
func (ac *AuthController) VerifyAccount(c *gin.Context) {
	var req VerifyAccountRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	// Get user by phone number
	user, err := ac.container.UserService.GetUserByPhoneNumber(req.PhoneNumber)
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{
			Error:   "USER_NOT_FOUND",
			Message: "Phone number not found",
			Code:    "404",
		})
		return
	}

	// Verify code
	if !ac.container.UserService.VerifyCode(user.ID, req.VerificationCode) {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_CODE",
			Message: "Invalid or expired verification code",
			Code:    "400",
		})
		return
	}

	// Mark account as verified
	err = ac.container.UserService.MarkAsVerified(user.ID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "VERIFICATION_FAILED",
			Message: "Failed to verify account",
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Account verified successfully",
	})
}

// ForgotPassword initiates password reset process
func (ac *AuthController) ForgotPassword(c *gin.Context) {
	var req ForgotPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	// Get user by phone number
	user, err := ac.container.UserService.GetUserByPhoneNumber(req.PhoneNumber)
	if err != nil {
		// Don't reveal if phone number exists for security
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "If the phone number exists, a reset code has been sent",
		})
		return
	}

	// Generate reset code
	resetCode := ac.generateVerificationCode()

	// Store reset code
	err = ac.container.UserService.SetPasswordResetCode(user.ID, resetCode)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "RESET_FAILED",
			Message: "Failed to initiate password reset",
			Code:    "500",
		})
		return
	}

	// TODO: Send SMS reset code
	// ac.container.NotificationService.SendSMS(req.PhoneNumber, "Your password reset code is: " + resetCode)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "If the phone number exists, a reset code has been sent",
	})
}

// ResetPassword resets user password with reset code
func (ac *AuthController) ResetPassword(c *gin.Context) {
	var req ResetPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	// Get user by phone number
	user, err := ac.container.UserService.GetUserByPhoneNumber(req.PhoneNumber)
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{
			Error:   "USER_NOT_FOUND",
			Message: "Phone number not found",
			Code:    "404",
		})
		return
	}

	// Verify reset code
	if !ac.container.UserService.VerifyPasswordResetCode(user.ID, req.ResetCode) {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_CODE",
			Message: "Invalid or expired reset code",
			Code:    "400",
		})
		return
	}

	// Hash new password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "RESET_FAILED",
			Message: "Failed to reset password",
			Code:    "500",
		})
		return
	}

	// Update password
	err = ac.container.UserService.UpdatePassword(user.ID, string(hashedPassword))
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "RESET_FAILED",
			Message: "Failed to reset password",
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Password reset successfully",
	})
}

// Helper functions
func (ac *AuthController) verifyPassword(password, hash string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	return err == nil
}

func (ac *AuthController) generateJWTToken(userID uint) string {
	// Get JWT configuration
	jwtConfig := ac.container.Config.JWT

	// Create JWT claims
	claims := jwt.MapClaims{
		"user_id": userID,
		"iss":     jwtConfig.Issuer,
		"aud":     jwtConfig.Audience,
		"exp":     time.Now().Add(time.Duration(jwtConfig.ExpirationTime) * time.Second).Unix(),
		"iat":     time.Now().Unix(),
	}

	// Create token with claims
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// Sign token with secret
	tokenString, err := token.SignedString([]byte(jwtConfig.SecretKey))
	if err != nil {
		ac.container.Logger.LogError(err, map[string]interface{}{
			"action":  "generate_jwt_token",
			"user_id": userID,
		})
		// Return a fallback token format if JWT generation fails
		return fmt.Sprintf("fallback_token_for_user_%d_%d", userID, time.Now().Unix())
	}

	return tokenString
}

func (ac *AuthController) generateVerificationCode() string {
	// Generate 6-digit verification code
	bytes := make([]byte, 3)
	rand.Read(bytes)
	code := ""
	for _, b := range bytes {
		code += fmt.Sprintf("%02d", int(b)%100)
	}
	return code[:6]
}
