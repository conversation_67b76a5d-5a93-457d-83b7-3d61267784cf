package services

import (
	"crypto/hmac"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"math/big"
	"os"
	"strconv"
	"time"

	"wallet-platform/internal/config"
	"wallet-platform/internal/models"
	"wallet-platform/pkg/logger"
	"wallet-platform/pkg/redis"

	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// PayCardService implements the PayCardServiceInterface
type PayCardService struct {
	db            *gorm.DB
	redis         *redis.Client
	logger        *logger.Logger
	walletService WalletServiceInterface
	config        *config.Config
}

// NewPayCardService creates a new paycard service
func NewPayCardService(db *gorm.DB, redisClient *redis.Client, log *logger.Logger, walletService WalletServiceInterface, cfg *config.Config) *PayCardService {
	return &PayCardService{
		db:            db,
		redis:         redisClient,
		logger:        log,
		walletService: walletService,
		config:        cfg,
	}
}

// CreateCard creates a new PayCard with enhanced validation and fraud prevention
func (s *PayCardService) CreateCard(walletID uint, cardType, holderName string) (*models.PayCardResponse, error) {
	// Verify wallet exists and is active
	var wallet models.Wallet
	if err := s.db.First(&wallet, walletID).Error; err != nil {
		return nil, fmt.Errorf("wallet not found")
	}

	if wallet.Status != "active" {
		return nil, fmt.Errorf("wallet is not active")
	}

	// Check card creation limits and fraud prevention
	if err := s.validateCardCreation(walletID, cardType); err != nil {
		return nil, err
	}

	// Generate card number
	cardNumber := s.generateCardNumber()

	// Set card limits based on type and wallet status
	limits := s.getCardLimitsByType(cardType, &wallet)

	// Create card
	card := models.PayCard{
		CardNumber:            cardNumber,
		WalletID:              walletID,
		CardHolderName:        holderName,
		CardType:              cardType,
		Status:                "active",
		SpendingLimit:         limits.SpendingLimit,
		DailySpendingLimit:    limits.DailySpendingLimit,
		MonthlySpendingLimit:  limits.MonthlySpendingLimit,
		IsPinSet:              false,
		ExpiresAt:             time.Now().AddDate(3, 0, 0), // 3 years from now
		ContactlessEnabled:    true,
		OnlinePaymentsEnabled: true,
		ATMWithdrawalsEnabled: false,
		InternationalEnabled:  false,
	}

	if err := s.db.Create(&card).Error; err != nil {
		s.logger.LogError(err, map[string]interface{}{
			"action":    "create_card",
			"wallet_id": walletID,
		})
		return nil, fmt.Errorf("failed to create card: %w", err)
	}

	// Collect card creation fee
	cardCreationFee := s.getCardCreationFee(cardType)
	if cardCreationFee > 0 && s.walletService != nil {
		if err := s.walletService.CollectPlatformFee(walletID, cardCreationFee, "card_creation_fee", fmt.Sprintf("Card creation fee for %s card", cardType)); err != nil {
			// Log fee collection failure but don't fail card creation
			s.logger.LogError(err, map[string]interface{}{
				"action":     "card_creation_fee_failed",
				"card_id":    card.ID,
				"wallet_id":  walletID,
				"fee_amount": cardCreationFee,
				"warning":    true,
			})
		}
	}

	s.logger.LogPayCard(fmt.Sprintf("%d", card.ID), fmt.Sprintf("%d", walletID), "card_created", cardCreationFee, "")

	return s.cardToResponse(&card), nil
}

// getCardCreationFee returns the fee for creating a card based on card type
func (s *PayCardService) getCardCreationFee(cardType string) float64 {
	if s.config == nil {
		// Fallback to default values if config is not available
		switch cardType {
		case "standard":
			return 25.0
		case "premium":
			return 50.0
		case "business":
			return 100.0
		default:
			return 25.0
		}
	}

	switch cardType {
	case "standard":
		return s.config.Fees.CardCreation.Standard
	case "premium":
		return s.config.Fees.CardCreation.Premium
	case "business":
		return s.config.Fees.CardCreation.Business
	default:
		return s.config.Fees.CardCreation.Standard // Default to standard fee
	}
}

// GetCard retrieves a card by ID
func (s *PayCardService) GetCard(id uint) (*models.PayCardResponse, error) {
	var card models.PayCard
	if err := s.db.First(&card, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("card not found")
		}
		return nil, fmt.Errorf("failed to get card: %w", err)
	}

	return s.cardToResponse(&card), nil
}

// GetCardsByWallet retrieves all cards for a wallet
func (s *PayCardService) GetCardsByWallet(walletID uint) ([]models.PayCardResponse, error) {
	var cards []models.PayCard
	if err := s.db.Where("wallet_id = ?", walletID).Find(&cards).Error; err != nil {
		return nil, fmt.Errorf("failed to get cards: %w", err)
	}

	var response []models.PayCardResponse
	for _, card := range cards {
		response = append(response, *s.cardToResponse(&card))
	}

	return response, nil
}

// UpdateCard updates card information
func (s *PayCardService) UpdateCard(id uint, updates map[string]interface{}) (*models.PayCardResponse, error) {
	var card models.PayCard
	if err := s.db.First(&card, id).Error; err != nil {
		return nil, fmt.Errorf("card not found")
	}

	if err := s.db.Model(&card).Updates(updates).Error; err != nil {
		s.logger.LogError(err, map[string]interface{}{
			"action":  "update_card",
			"card_id": id,
			"updates": updates,
		})
		return nil, fmt.Errorf("failed to update card: %w", err)
	}

	return s.cardToResponse(&card), nil
}

// ProcessTransaction processes a card transaction
func (s *PayCardService) ProcessTransaction(cardNumber string, amount float64, merchantInfo map[string]interface{}) (*models.PayCardTransactionResponse, error) {
	// Start transaction
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Get card
	var card models.PayCard
	if err := tx.Where("card_number = ?", cardNumber).First(&card).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("card not found")
	}

	// Check card status
	if card.Status != "active" {
		tx.Rollback()
		return nil, fmt.Errorf("card is not active")
	}

	// Get wallet
	var wallet models.Wallet
	if err := tx.First(&wallet, card.WalletID).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("wallet not found")
	}

	// Check wallet balance
	if wallet.Balance < amount {
		tx.Rollback()
		return nil, fmt.Errorf("insufficient wallet balance")
	}

	// Check spending limits
	if err := s.checkSpendingLimits(tx, card.ID, amount); err != nil {
		tx.Rollback()
		return nil, err
	}

	// Update wallet balance
	wallet.Balance -= amount
	if err := tx.Save(&wallet).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("failed to update wallet balance: %w", err)
	}

	// Create transaction record
	transactionID := s.generateTransactionID()
	transaction := models.PayCardTransaction{
		TransactionID:    transactionID,
		CardID:           card.ID,
		Amount:           amount,
		Currency:         wallet.Currency,
		MerchantName:     merchantInfo["name"].(string),
		MerchantCategory: merchantInfo["category"].(string),
		TransactionType:  "purchase",
		Status:           "completed",
		Reference:        fmt.Sprintf("PAY_%s", uuid.New().String()[:8]),
		Metadata:         datatypes.JSON("{}"),
	}

	if err := tx.Create(&transaction).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("failed to create transaction: %w", err)
	}

	// Create wallet transaction record
	walletTx := models.WalletTransaction{
		WalletID:     wallet.ID,
		Type:         "debit",
		Amount:       amount,
		BalanceAfter: wallet.Balance,
		Reference:    transaction.Reference,
		Description:  fmt.Sprintf("Card payment at %s", transaction.MerchantName),
		Category:     "card_payment",
		Meta:         datatypes.JSON(`{"card_id": ` + fmt.Sprintf("%d", card.ID) + `, "transaction_id": "` + transactionID + `"}`),
	}

	if err := tx.Create(&walletTx).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("failed to create wallet transaction: %w", err)
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	s.logger.LogPayCard(fmt.Sprintf("%d", card.ID), fmt.Sprintf("%d", wallet.ID), "payment", amount, transaction.MerchantName)

	return &models.PayCardTransactionResponse{
		ID:               transaction.ID,
		TransactionID:    transactionID,
		CardID:           card.ID,
		Amount:           amount,
		Currency:         wallet.Currency,
		MerchantName:     transaction.MerchantName,
		MerchantCategory: transaction.MerchantCategory,
		TransactionType:  transaction.TransactionType,
		Status:           transaction.Status,
		Reference:        transaction.Reference,
		CreatedAt:        transaction.CreatedAt,
	}, nil
}

// UpdatePIN updates card PIN with secure hashing and validation
func (s *PayCardService) UpdatePIN(cardID uint, currentPIN, newPIN string) error {
	var card models.PayCard
	if err := s.db.First(&card, cardID).Error; err != nil {
		return fmt.Errorf("card not found")
	}

	// Check if card is locked due to failed PIN attempts
	if card.CardLockedUntil != nil && card.CardLockedUntil.After(time.Now()) {
		return fmt.Errorf("card is temporarily locked due to failed PIN attempts")
	}

	// Validate new PIN format
	if err := s.validatePINFormat(newPIN); err != nil {
		return err
	}

	// If PIN is already set, verify current PIN
	if card.IsPinSet {
		if !s.verifyPIN(currentPIN, card.SecurityPin) {
			// Increment failed attempts
			failedAttempts := card.FailedPinAttempts + 1
			updates := map[string]interface{}{
				"failed_pin_attempts":     failedAttempts,
				"last_failed_pin_attempt": time.Now(),
			}

			// Lock card after 3 failed attempts
			if failedAttempts >= 3 {
				lockUntil := time.Now().Add(30 * time.Minute) // Lock for 30 minutes
				updates["card_locked_until"] = lockUntil
				s.logger.LogSecurity("card_locked_pin_attempts", fmt.Sprintf("%d", card.WalletID), "system",
					fmt.Sprintf("Card %d locked due to %d failed PIN attempts", cardID, failedAttempts))
			}

			s.db.Model(&card).Updates(updates)
			return fmt.Errorf("invalid current PIN")
		}
	}

	// Hash the new PIN
	hashedPIN, err := s.hashPIN(newPIN)
	if err != nil {
		return fmt.Errorf("failed to hash PIN: %w", err)
	}

	// Update PIN and reset failed attempts
	updates := map[string]interface{}{
		"security_pin":            hashedPIN,
		"is_pin_set":              true,
		"failed_pin_attempts":     0,
		"last_failed_pin_attempt": nil,
		"card_locked_until":       nil,
	}

	if err := s.db.Model(&card).Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to update PIN: %w", err)
	}

	s.logger.LogPayCard(fmt.Sprintf("%d", cardID), fmt.Sprintf("%d", card.WalletID), "pin_updated", 0, "")
	s.logger.LogSecurity("pin_updated", fmt.Sprintf("%d", card.WalletID), "user",
		fmt.Sprintf("PIN updated for card %d", cardID))

	return nil
}

// BlockCard blocks a card
func (s *PayCardService) BlockCard(cardID uint, reason string) error {
	var card models.PayCard
	if err := s.db.First(&card, cardID).Error; err != nil {
		return fmt.Errorf("card not found")
	}

	updates := map[string]interface{}{
		"status": "blocked",
		"metadata": map[string]interface{}{
			"block_reason": reason,
			"blocked_at":   time.Now(),
		},
	}

	if err := s.db.Model(&card).Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to block card: %w", err)
	}

	s.logger.LogPayCard(fmt.Sprintf("%d", cardID), fmt.Sprintf("%d", card.WalletID), "card_blocked", 0, reason)

	return nil
}

// UnblockCard unblocks a card
func (s *PayCardService) UnblockCard(cardID uint) error {
	var card models.PayCard
	if err := s.db.First(&card, cardID).Error; err != nil {
		return fmt.Errorf("card not found")
	}

	if err := s.db.Model(&card).Update("status", "active").Error; err != nil {
		return fmt.Errorf("failed to unblock card: %w", err)
	}

	s.logger.LogPayCard(fmt.Sprintf("%d", cardID), fmt.Sprintf("%d", card.WalletID), "card_unblocked", 0, "")

	return nil
}

// GetTransactionHistory retrieves transaction history for a card
func (s *PayCardService) GetTransactionHistory(cardID uint, filters map[string]interface{}) ([]models.PayCardTransactionResponse, error) {
	var transactions []models.PayCardTransaction
	query := s.db.Where("card_id = ?", cardID)

	// Apply filters
	if startDate, ok := filters["start_date"]; ok {
		query = query.Where("created_at >= ?", startDate)
	}
	if endDate, ok := filters["end_date"]; ok {
		query = query.Where("created_at <= ?", endDate)
	}
	if merchant, ok := filters["merchant"]; ok {
		query = query.Where("merchant_name ILIKE ?", "%"+merchant.(string)+"%")
	}
	if category, ok := filters["category"]; ok {
		query = query.Where("merchant_category = ?", category)
	}

	if err := query.Order("created_at DESC").Find(&transactions).Error; err != nil {
		return nil, fmt.Errorf("failed to get transaction history: %w", err)
	}

	var response []models.PayCardTransactionResponse
	for _, tx := range transactions {
		response = append(response, models.PayCardTransactionResponse{
			ID:               tx.ID,
			TransactionID:    tx.TransactionID,
			CardID:           tx.CardID,
			Amount:           tx.Amount,
			Currency:         tx.Currency,
			MerchantName:     tx.MerchantName,
			MerchantCategory: tx.MerchantCategory,
			TransactionType:  tx.TransactionType,
			Status:           tx.Status,
			Reference:        tx.Reference,
			CreatedAt:        tx.CreatedAt,
		})
	}

	return response, nil
}

// GetAllCards retrieves all cards with pagination and filtering (admin only)
func (s *PayCardService) GetAllCards(page, limit int, status, cardType, search string) ([]models.PayCardResponse, int64, error) {
	var cards []models.PayCard
	var total int64

	// Build query
	query := s.db.Model(&models.PayCard{})

	// Apply filters
	if status != "" {
		query = query.Where("status = ?", status)
	}
	if cardType != "" {
		query = query.Where("card_type = ?", cardType)
	}
	if search != "" {
		// Search by card holder name or masked card number
		query = query.Where("card_holder_name LIKE ? OR card_number LIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		s.logger.LogError(err, map[string]interface{}{
			"action":    "get_all_cards_count",
			"status":    status,
			"card_type": cardType,
			"search":    search,
		})
		return nil, 0, fmt.Errorf("failed to count cards: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Offset(offset).Limit(limit).Order("created_at DESC").Find(&cards).Error; err != nil {
		s.logger.LogError(err, map[string]interface{}{
			"action":    "get_all_cards",
			"page":      page,
			"limit":     limit,
			"status":    status,
			"card_type": cardType,
			"search":    search,
		})
		return nil, 0, fmt.Errorf("failed to fetch cards: %w", err)
	}

	// Convert to response format
	var responses []models.PayCardResponse
	for _, card := range cards {
		responses = append(responses, *s.cardToResponse(&card))
	}

	return responses, total, nil
}

// Helper methods
func (s *PayCardService) cardToResponse(card *models.PayCard) *models.PayCardResponse {
	// Mask card number for security
	maskedNumber := s.maskCardNumber(card.CardNumber)

	return &models.PayCardResponse{
		ID:                   card.ID,
		CardNumber:           maskedNumber,
		WalletID:             card.WalletID,
		CardHolderName:       card.CardHolderName,
		CardType:             card.CardType,
		Status:               card.Status,
		SpendingLimit:        card.SpendingLimit,
		DailySpendingLimit:   card.DailySpendingLimit,
		MonthlySpendingLimit: card.MonthlySpendingLimit,
		CurrentDailySpent:    card.CurrentDailySpent,
		CurrentMonthlySpent:  card.CurrentMonthlySpent,
		IsPinSet:             card.IsPinSet,
		CreatedAt:            card.CreatedAt,
		UpdatedAt:            card.UpdatedAt,
		ExpiresAt:            card.ExpiresAt,
	}
}

func (s *PayCardService) generateCardNumber() string {
	// Generate a 16-digit card number (simplified)
	return fmt.Sprintf("5399%012d", time.Now().Unix()%1000000000000)
}

func (s *PayCardService) generateTransactionID() string {
	return fmt.Sprintf("TXN_%d", time.Now().UnixNano())
}

func (s *PayCardService) maskCardNumber(cardNumber string) string {
	if len(cardNumber) < 4 {
		return cardNumber
	}
	return cardNumber[:4] + "********" + cardNumber[len(cardNumber)-4:]
}

func (s *PayCardService) checkSpendingLimits(tx *gorm.DB, cardID uint, amount float64) error {
	// Check daily spending limit
	var dailySpent float64
	today := time.Now().Format("2006-01-02")
	if err := tx.Model(&models.PayCardTransaction{}).
		Where("card_id = ? AND DATE(created_at) = ? AND status = 'completed'", cardID, today).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&dailySpent).Error; err != nil {
		return fmt.Errorf("failed to check daily spending: %w", err)
	}

	var card models.PayCard
	if err := tx.Select("daily_spending_limit, monthly_spending_limit").First(&card, cardID).Error; err != nil {
		return fmt.Errorf("failed to get card limits: %w", err)
	}

	if dailySpent+amount > card.DailySpendingLimit {
		return fmt.Errorf("daily spending limit exceeded")
	}

	// Check monthly spending limit
	var monthlySpent float64
	firstOfMonth := time.Now().Format("2006-01-01")
	if err := tx.Model(&models.PayCardTransaction{}).
		Where("card_id = ? AND created_at >= ? AND status = 'completed'", cardID, firstOfMonth).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&monthlySpent).Error; err != nil {
		return fmt.Errorf("failed to check monthly spending: %w", err)
	}

	if monthlySpent+amount > card.MonthlySpendingLimit {
		return fmt.Errorf("monthly spending limit exceeded")
	}

	return nil
}

// Enhanced PayCard Features - QR Code Payment System

// QRCodePaymentData represents the structure for QR code payment data
type QRCodePaymentData struct {
	CardID     uint      `json:"card_id"`
	CardNumber string    `json:"card_number"`
	WalletID   uint      `json:"wallet_id"`
	CardType   string    `json:"card_type"`
	HolderName string    `json:"holder_name"`
	ExpiresAt  time.Time `json:"expires_at"`
	Timestamp  int64     `json:"timestamp"`
	Nonce      string    `json:"nonce"`
	Signature  string    `json:"signature"`
	Version    string    `json:"version"`
	Platform   string    `json:"platform"`
}

// QRCodeTransactionRequest represents a payment request via QR code
type QRCodeTransactionRequest struct {
	QRCodeData      string                 `json:"qr_code_data"`
	Amount          float64                `json:"amount"`
	Currency        string                 `json:"currency"`
	MerchantInfo    map[string]interface{} `json:"merchant_info"`
	TransactionType string                 `json:"transaction_type"` // payment, withdrawal, transfer
	PIN             string                 `json:"pin,omitempty"`
	DeviceInfo      map[string]interface{} `json:"device_info"`
	Location        map[string]interface{} `json:"location,omitempty"`
}

// GenerateQRCodeData generates secure QR code data for the card
func (s *PayCardService) GenerateQRCodeData(cardID uint) (string, error) {
	var card models.PayCard
	if err := s.db.First(&card, cardID).Error; err != nil {
		return "", fmt.Errorf("card not found")
	}

	// Verify card is active
	if card.Status != "active" {
		return "", fmt.Errorf("card is not active")
	}

	// Generate nonce for security
	nonceBytes := make([]byte, 16)
	if _, err := rand.Read(nonceBytes); err != nil {
		return "", fmt.Errorf("failed to generate nonce: %w", err)
	}
	nonce := hex.EncodeToString(nonceBytes)

	// Create QR code data structure
	qrData := QRCodePaymentData{
		CardID:     card.ID,
		CardNumber: card.CardNumber,
		WalletID:   card.WalletID,
		CardType:   card.CardType,
		HolderName: card.CardHolderName,
		ExpiresAt:  card.ExpiresAt,
		Timestamp:  time.Now().Unix(),
		Nonce:      nonce,
		Version:    "1.0",
		Platform:   "wallet-platform",
	}

	// Generate signature for data integrity
	signature, err := s.generateQRCodeSignature(qrData)
	if err != nil {
		return "", fmt.Errorf("failed to generate signature: %w", err)
	}
	qrData.Signature = signature

	// Convert to JSON
	jsonData, err := json.Marshal(qrData)
	if err != nil {
		return "", fmt.Errorf("failed to marshal QR data: %w", err)
	}

	// Encode as base64 for QR code (more efficient than hex)
	qrCodeString := base64.StdEncoding.EncodeToString(jsonData)

	// Update card with latest QR code data
	if err := s.db.Model(&card).Update("qr_code_data", qrCodeString).Error; err != nil {
		s.logger.LogError(err, map[string]interface{}{
			"action":  "update_qr_code_data",
			"card_id": cardID,
		})
	}

	return qrCodeString, nil
}

// generateQRCodeSignature generates HMAC signature for QR code data integrity
func (s *PayCardService) generateQRCodeSignature(qrData QRCodePaymentData) (string, error) {
	// Get signing key from environment or use default
	signingKey := os.Getenv("QR_CODE_SIGNING_KEY")
	if signingKey == "" {
		signingKey = "default_qr_signing_key_change_in_production"
	}

	// Create data string for signing (exclude signature field)
	dataToSign := fmt.Sprintf("%d:%s:%d:%s:%s:%d:%s:%s:%s",
		qrData.CardID,
		qrData.CardNumber,
		qrData.WalletID,
		qrData.CardType,
		qrData.HolderName,
		qrData.Timestamp,
		qrData.Nonce,
		qrData.Version,
		qrData.Platform,
	)

	// Generate HMAC signature
	h := hmac.New(sha256.New, []byte(signingKey))
	h.Write([]byte(dataToSign))
	signature := hex.EncodeToString(h.Sum(nil))

	return signature, nil
}

// ValidateQRCodeData validates and parses QR code data
func (s *PayCardService) ValidateQRCodeData(qrCodeString string) (*QRCodePaymentData, error) {
	// Decode base64
	jsonData, err := base64.StdEncoding.DecodeString(qrCodeString)
	if err != nil {
		return nil, fmt.Errorf("invalid QR code format: %w", err)
	}

	// Parse JSON
	var qrData QRCodePaymentData
	if err := json.Unmarshal(jsonData, &qrData); err != nil {
		return nil, fmt.Errorf("invalid QR code data: %w", err)
	}

	// Validate version
	if qrData.Version != "1.0" {
		return nil, fmt.Errorf("unsupported QR code version: %s", qrData.Version)
	}

	// Validate platform
	if qrData.Platform != "wallet-platform" {
		return nil, fmt.Errorf("invalid platform: %s", qrData.Platform)
	}

	// Validate timestamp (not older than 24 hours)
	if time.Now().Unix()-qrData.Timestamp > 86400 {
		return nil, fmt.Errorf("QR code has expired")
	}

	// Validate signature
	expectedSignature, err := s.generateQRCodeSignature(qrData)
	if err != nil {
		return nil, fmt.Errorf("failed to validate signature: %w", err)
	}

	if qrData.Signature != expectedSignature {
		return nil, fmt.Errorf("invalid QR code signature")
	}

	// Verify card exists and is active
	var card models.PayCard
	if err := s.db.First(&card, qrData.CardID).Error; err != nil {
		return nil, fmt.Errorf("card not found")
	}

	if card.Status != "active" {
		return nil, fmt.Errorf("card is not active")
	}

	// Verify card hasn't expired
	if time.Now().After(card.ExpiresAt) {
		return nil, fmt.Errorf("card has expired")
	}

	return &qrData, nil
}

// ProcessQRCodePayment processes a payment via QR code scan
func (s *PayCardService) ProcessQRCodePayment(request QRCodeTransactionRequest) (*models.PayCardTransactionResponse, error) {
	// Validate QR code data
	qrData, err := s.ValidateQRCodeData(request.QRCodeData)
	if err != nil {
		return nil, fmt.Errorf("QR code validation failed: %w", err)
	}

	// Get card details
	var card models.PayCard
	if err := s.db.First(&card, qrData.CardID).Error; err != nil {
		return nil, fmt.Errorf("card not found")
	}

	// Validate PIN if provided
	if request.PIN != "" {
		if err := s.VerifyCardPIN(card.ID, request.PIN); err != nil {
			return nil, fmt.Errorf("PIN verification failed: %w", err)
		}
	}

	// Validate amount
	if request.Amount <= 0 {
		return nil, fmt.Errorf("invalid amount")
	}

	// Check spending limits
	if err := s.checkSpendingLimits(s.db, card.ID, request.Amount); err != nil {
		return nil, fmt.Errorf("spending limit exceeded: %w", err)
	}

	// Perform fraud detection
	if err := s.performFraudDetection(&card, request.Amount, request.MerchantInfo); err != nil {
		return nil, fmt.Errorf("fraud detection failed: %w", err)
	}

	// Process the transaction
	transactionID := s.generateTransactionID()
	reference := fmt.Sprintf("QR_%s", transactionID)

	// Convert merchant info to individual fields
	merchantName := ""
	merchantCategory := ""
	merchantLocation := ""
	merchantID := ""

	if request.MerchantInfo != nil {
		if name, ok := request.MerchantInfo["name"].(string); ok {
			merchantName = name
		}
		if category, ok := request.MerchantInfo["category"].(string); ok {
			merchantCategory = category
		}
		if location, ok := request.MerchantInfo["location"].(string); ok {
			merchantLocation = location
		}
		if id, ok := request.MerchantInfo["id"].(string); ok {
			merchantID = id
		}
	}

	// Convert maps to JSON
	deviceInfoJSON, _ := json.Marshal(request.DeviceInfo)
	locationInfoJSON, _ := json.Marshal(request.Location)

	// Create transaction record
	transaction := models.PayCardTransaction{
		CardID:           card.ID,
		Amount:           request.Amount,
		Currency:         request.Currency,
		MerchantName:     merchantName,
		MerchantCategory: merchantCategory,
		MerchantLocation: merchantLocation,
		MerchantID:       merchantID,
		TransactionType:  request.TransactionType,
		PaymentMethod:    "qr_code",
		Status:           "pending",
		Reference:        reference,
		QRCodeScanned:    request.QRCodeData[:20] + "...", // Store truncated QR code for reference
		DeviceInfo:       datatypes.JSON(deviceInfoJSON),
		LocationInfo:     datatypes.JSON(locationInfoJSON),
		ProcessedAt:      &[]time.Time{time.Now()}[0], // Convert to pointer
	}

	if err := s.db.Create(&transaction).Error; err != nil {
		return nil, fmt.Errorf("failed to create transaction: %w", err)
	}

	// Update transaction status to completed (in real implementation, this would be async)
	transaction.Status = "completed"
	if err := s.db.Save(&transaction).Error; err != nil {
		s.logger.LogError(err, map[string]interface{}{
			"action":         "update_transaction_status",
			"transaction_id": transaction.ID,
		})
	}

	// Log successful QR code payment
	s.logger.LogPayCard(
		strconv.FormatUint(uint64(card.ID), 10),
		strconv.FormatUint(uint64(card.WalletID), 10),
		"qr_code_payment_processed",
		request.Amount,
		merchantName,
	)

	// Return transaction response
	return &models.PayCardTransactionResponse{
		ID:               transaction.ID,
		TransactionID:    strconv.FormatUint(uint64(transaction.ID), 10),
		CardID:           card.ID,
		Reference:        reference,
		Status:           transaction.Status,
		Amount:           request.Amount,
		Currency:         request.Currency,
		MerchantName:     merchantName,
		MerchantCategory: merchantCategory,
		ProcessedAt:      transaction.ProcessedAt,
		TransactionType:  request.TransactionType,
		PaymentMethod:    "qr_code",
		CreatedAt:        transaction.CreatedAt,
	}, nil
}

// RequestPhysicalCard requests a physical card to be printed
func (s *PayCardService) RequestPhysicalCard(cardID uint, design string) error {
	var card models.PayCard
	if err := s.db.First(&card, cardID).Error; err != nil {
		return fmt.Errorf("card not found")
	}

	if card.PhysicalCardIssued {
		return fmt.Errorf("physical card already issued")
	}

	// Generate physical card serial
	serial := s.generatePhysicalCardSerial()

	updates := map[string]interface{}{
		"physical_card_issued": true,
		"physical_card_serial": serial,
		"physical_card_status": "pending",
		"physical_card_design": design,
		"physical_card_batch":  fmt.Sprintf("BATCH_%d", time.Now().Unix()),
	}

	if err := s.db.Model(&card).Updates(updates).Error; err != nil {
		return err
	}

	s.logger.LogTransaction(
		fmt.Sprintf("%d", cardID),
		"",
		"physical_card_requested",
		0.0,
		fmt.Sprintf("Physical card requested for card %d with serial %s", cardID, serial),
	)

	return nil
}

// ActivatePhysicalCard activates a physical card with activation code
func (s *PayCardService) ActivatePhysicalCard(serial, activationCode string) error {
	var card models.PayCard
	if err := s.db.Where("physical_card_serial = ?", serial).First(&card).Error; err != nil {
		return fmt.Errorf("card not found")
	}

	if card.PhysicalCardStatus == "activated" {
		return fmt.Errorf("card already activated")
	}

	// Validate activation code (simplified - in production, use secure validation)
	expectedCode := s.generateActivationCode(serial)
	if activationCode != expectedCode {
		return fmt.Errorf("invalid activation code")
	}

	updates := map[string]interface{}{
		"physical_card_status": "activated",
		"status":               "active",
	}

	if err := s.db.Model(&card).Updates(updates).Error; err != nil {
		return err
	}

	s.logger.LogTransaction(
		fmt.Sprintf("%d", card.ID),
		"",
		"physical_card_activated",
		0.0,
		fmt.Sprintf("Physical card activated for card %d with serial %s", card.ID, serial),
	)

	return nil
}

// GenerateCardToken generates a secure token for card operations
func (s *PayCardService) GenerateCardToken(cardID uint, tokenType string, expiresIn time.Duration, maxUsage int) (*models.PayCardToken, error) {
	var card models.PayCard
	if err := s.db.First(&card, cardID).Error; err != nil {
		return nil, fmt.Errorf("card not found")
	}

	// Generate secure token
	tokenBytes := make([]byte, 32)
	if _, err := rand.Read(tokenBytes); err != nil {
		return nil, err
	}
	tokenValue := hex.EncodeToString(tokenBytes)

	token := models.PayCardToken{
		CardID:     cardID,
		TokenValue: tokenValue,
		TokenType:  tokenType,
		ExpiresAt:  time.Now().Add(expiresIn),
		IsActive:   true,
		MaxUsage:   maxUsage,
	}

	if err := s.db.Create(&token).Error; err != nil {
		return nil, err
	}

	return &token, nil
}

// AddMerchantToWhitelist adds a merchant to the card's whitelist
func (s *PayCardService) AddMerchantToWhitelist(cardID uint, merchantName, merchantID, category string) error {
	var card models.PayCard
	if err := s.db.First(&card, cardID).Error; err != nil {
		return fmt.Errorf("card not found")
	}

	// Update card's merchant restrictions (simplified implementation)
	// In production, this would use a separate whitelist table
	restrictions := map[string]interface{}{
		"whitelisted_merchants": []map[string]string{
			{
				"name":     merchantName,
				"id":       merchantID,
				"category": category,
			},
		},
	}

	restrictionsJSON, _ := json.Marshal(restrictions)
	updates := map[string]interface{}{
		"merchant_restrictions": datatypes.JSON(restrictionsJSON),
	}

	if err := s.db.Model(&card).Updates(updates).Error; err != nil {
		return err
	}

	return nil
}

// SetSpendingControl sets advanced spending controls for a card
func (s *PayCardService) SetSpendingControl(cardID uint, controlType string, controlValue float64, maxAmount float64, maxTransactions int, timeRestrictions, locationRestrictions map[string]interface{}) error {
	var card models.PayCard
	if err := s.db.First(&card, cardID).Error; err != nil {
		return fmt.Errorf("card not found")
	}

	// Update card's spending controls (simplified implementation)
	controls := map[string]interface{}{
		"control_type":          controlType,
		"control_value":         controlValue,
		"max_amount":            maxAmount,
		"max_transactions":      maxTransactions,
		"time_restrictions":     timeRestrictions,
		"location_restrictions": locationRestrictions,
	}

	controlsJSON, _ := json.Marshal(controls)
	updates := map[string]interface{}{
		"metadata": datatypes.JSON(controlsJSON),
	}

	if err := s.db.Model(&card).Updates(updates).Error; err != nil {
		return err
	}

	return nil
}

// ProcessContactlessPayment processes a contactless payment
func (s *PayCardService) ProcessContactlessPayment(cardNumber string, amount float64, merchantInfo map[string]interface{}) (*models.PayCardTransactionResponse, error) {
	// Get card
	var card models.PayCard
	if err := s.db.Where("card_number = ?", cardNumber).First(&card).Error; err != nil {
		return nil, fmt.Errorf("card not found")
	}

	// Validate card status
	if card.Status != "active" {
		return nil, fmt.Errorf("card is not active")
	}

	// Check spending limits
	if err := s.checkSpendingLimits(s.db, card.ID, amount); err != nil {
		return nil, err
	}

	// Process transaction
	return s.ProcessTransaction(cardNumber, amount, merchantInfo)
}

// Helper methods

// generatePhysicalCardSerial generates a unique physical card serial number
func (s *PayCardService) generatePhysicalCardSerial() string {
	timestamp := time.Now().Unix()
	randomBytes := make([]byte, 4)
	rand.Read(randomBytes)
	randomNum := big.NewInt(0).SetBytes(randomBytes).Int64() % 9999
	return fmt.Sprintf("PC%d%04d", timestamp, randomNum)
}

// generateActivationCode generates an activation code for physical cards
func (s *PayCardService) generateActivationCode(serial string) string {
	hash := sha256.Sum256([]byte(serial + "ACTIVATION_SALT"))
	return fmt.Sprintf("%06d", hash[0]<<16|hash[1]<<8|hash[2])
}

// PIN management helper methods

// validatePINFormat validates PIN format and security requirements
func (s *PayCardService) validatePINFormat(pin string) error {
	// Check PIN length (must be 4-6 digits)
	if len(pin) < 4 || len(pin) > 6 {
		return fmt.Errorf("PIN must be 4-6 digits long")
	}

	// Check if PIN contains only digits
	for _, char := range pin {
		if char < '0' || char > '9' {
			return fmt.Errorf("PIN must contain only digits")
		}
	}

	// Check for weak PINs
	if s.isWeakPIN(pin) {
		return fmt.Errorf("PIN is too weak. Avoid sequential numbers, repeated digits, or common patterns")
	}

	return nil
}

// isWeakPIN checks for common weak PIN patterns
func (s *PayCardService) isWeakPIN(pin string) bool {
	// Common weak patterns
	weakPINs := []string{
		"0000", "1111", "2222", "3333", "4444", "5555", "6666", "7777", "8888", "9999",
		"1234", "4321", "0123", "3210", "1122", "2211", "1212", "2121",
		"0000", "1234", "4321", "1111", "2580", "0852", "1357", "2468",
	}

	for _, weak := range weakPINs {
		if pin == weak {
			return true
		}
	}

	// Check for sequential numbers
	if len(pin) == 4 {
		isSequential := true
		for i := 1; i < len(pin); i++ {
			if pin[i] != pin[i-1]+1 {
				isSequential = false
				break
			}
		}
		if isSequential {
			return true
		}

		// Check for reverse sequential
		isReverseSequential := true
		for i := 1; i < len(pin); i++ {
			if pin[i] != pin[i-1]-1 {
				isReverseSequential = false
				break
			}
		}
		if isReverseSequential {
			return true
		}
	}

	return false
}

// hashPIN securely hashes a PIN using bcrypt
func (s *PayCardService) hashPIN(pin string) (string, error) {
	// Add salt to PIN before hashing
	saltedPIN := pin + "CARD_PIN_SALT_2024"

	// Use bcrypt for secure hashing
	hashedBytes, err := bcrypt.GenerateFromPassword([]byte(saltedPIN), bcrypt.DefaultCost)
	if err != nil {
		return "", err
	}

	return string(hashedBytes), nil
}

// verifyPIN verifies a PIN against its hash
func (s *PayCardService) verifyPIN(pin, hashedPIN string) bool {
	// Add salt to PIN before verification
	saltedPIN := pin + "CARD_PIN_SALT_2024"

	// Compare with bcrypt
	err := bcrypt.CompareHashAndPassword([]byte(hashedPIN), []byte(saltedPIN))
	return err == nil
}

// VerifyCardPIN verifies a card's PIN for transaction authorization
func (s *PayCardService) VerifyCardPIN(cardID uint, pin string) error {
	var card models.PayCard
	if err := s.db.First(&card, cardID).Error; err != nil {
		return fmt.Errorf("card not found")
	}

	// Check if card is locked due to failed PIN attempts
	if card.CardLockedUntil != nil && card.CardLockedUntil.After(time.Now()) {
		return fmt.Errorf("card is temporarily locked due to failed PIN attempts")
	}

	// Check if PIN is set
	if !card.IsPinSet {
		return fmt.Errorf("PIN not set for this card")
	}

	// Verify PIN
	if !s.verifyPIN(pin, card.SecurityPin) {
		// Increment failed attempts
		failedAttempts := card.FailedPinAttempts + 1
		updates := map[string]interface{}{
			"failed_pin_attempts":     failedAttempts,
			"last_failed_pin_attempt": time.Now(),
		}

		// Lock card after 3 failed attempts
		if failedAttempts >= 3 {
			lockUntil := time.Now().Add(30 * time.Minute) // Lock for 30 minutes
			updates["card_locked_until"] = lockUntil
			s.logger.LogSecurity("card_locked_pin_attempts", fmt.Sprintf("%d", card.WalletID), "system",
				fmt.Sprintf("Card %d locked due to %d failed PIN attempts", cardID, failedAttempts))
		}

		s.db.Model(&card).Updates(updates)
		return fmt.Errorf("invalid PIN")
	}

	// Reset failed attempts on successful verification
	if card.FailedPinAttempts > 0 {
		s.db.Model(&card).Updates(map[string]interface{}{
			"failed_pin_attempts":     0,
			"last_failed_pin_attempt": nil,
		})
	}

	return nil
}

// Enhanced validation methods

// validateCardForTransaction validates a card before processing a transaction
func (s *PayCardService) validateCardForTransaction(card *models.PayCard, amount float64) error {
	if card.Status != "active" {
		return fmt.Errorf("card is not active")
	}

	if card.ExpiresAt.Before(time.Now()) {
		return fmt.Errorf("card has expired")
	}

	if amount <= 0 {
		return fmt.Errorf("invalid transaction amount")
	}

	if amount > card.SpendingLimit {
		return fmt.Errorf("amount exceeds spending limit")
	}

	return nil
}

// Enhanced fraud detection with comprehensive checks
func (s *PayCardService) performFraudDetection(card *models.PayCard, amount float64, merchantInfo map[string]interface{}) error {
	// Check for suspicious patterns
	var recentTransactions []models.PayCardTransaction
	if err := s.db.Where("card_id = ? AND created_at > ?", card.ID, time.Now().Add(-1*time.Hour)).
		Find(&recentTransactions).Error; err != nil {
		return err
	}

	// Check for rapid successive transactions (velocity check)
	if len(recentTransactions) > 5 {
		s.logger.LogSecurity("card_velocity_alert", fmt.Sprintf("%d", card.WalletID), "system",
			fmt.Sprintf("Card %d: %d transactions in 1 hour", card.ID, len(recentTransactions)))
		return fmt.Errorf("too many transactions in short period - potential fraud detected")
	}

	// Check for unusual amounts (amount anomaly detection)
	if err := s.checkAmountAnomaly(card, amount, recentTransactions); err != nil {
		return err
	}

	// Check for duplicate transactions (duplicate detection)
	if err := s.checkDuplicateTransactions(card, amount, merchantInfo, recentTransactions); err != nil {
		return err
	}

	// Check for geographic anomalies if location data is available
	if err := s.checkGeographicAnomaly(card, merchantInfo); err != nil {
		return err
	}

	// Check for merchant category restrictions
	if err := s.checkMerchantRestrictions(card, merchantInfo); err != nil {
		return err
	}

	// Check for time-based restrictions
	if err := s.checkTimeRestrictions(card); err != nil {
		return err
	}

	return nil
}

// CardLimits represents spending limits for different card types
type CardLimits struct {
	SpendingLimit        float64
	DailySpendingLimit   float64
	MonthlySpendingLimit float64
}

// validateCardCreation validates card creation with fraud prevention
func (s *PayCardService) validateCardCreation(walletID uint, cardType string) error {
	// Check maximum cards per wallet
	var cardCount int64
	if err := s.db.Model(&models.PayCard{}).
		Where("wallet_id = ? AND status IN ('active', 'suspended')", walletID).
		Count(&cardCount).Error; err != nil {
		return fmt.Errorf("failed to check existing cards: %w", err)
	}

	// Set maximum cards based on card type
	maxCards := s.getMaxCardsForType(cardType)
	if cardCount >= int64(maxCards) {
		return fmt.Errorf("maximum number of %s cards (%d) reached for this wallet", cardType, maxCards)
	}

	// Check for rapid card creation (fraud prevention)
	var recentCards int64
	cooldownHours := 24 // Default cooldown
	maxCardsPerDay := 3 // Default max cards per day

	if s.config != nil {
		cooldownHours = s.config.CardLimits.Creation.CooldownHours
		maxCardsPerDay = s.config.CardLimits.Creation.MaxCardsPerDay
	}

	if err := s.db.Model(&models.PayCard{}).
		Where("wallet_id = ? AND created_at > ?", walletID, time.Now().Add(-time.Duration(cooldownHours)*time.Hour)).
		Count(&recentCards).Error; err != nil {
		return fmt.Errorf("failed to check recent card creation: %w", err)
	}

	if recentCards >= int64(maxCardsPerDay) {
		return fmt.Errorf("too many cards created recently. Please wait %d hours before creating another card", cooldownHours)
	}

	// Validate card type
	validTypes := []string{"standard", "premium", "business"}
	isValid := false
	for _, validType := range validTypes {
		if cardType == validType {
			isValid = true
			break
		}
	}
	if !isValid {
		return fmt.Errorf("invalid card type: %s", cardType)
	}

	return nil
}

// getMaxCardsForType returns maximum cards allowed for each card type
func (s *PayCardService) getMaxCardsForType(cardType string) int {
	// Use configuration if available
	if s.config != nil {
		switch cardType {
		case "standard":
			return s.config.CardLimits.Standard.MaxCardsPerWallet
		case "premium":
			return s.config.CardLimits.Premium.MaxCardsPerWallet
		case "business":
			return s.config.CardLimits.Business.MaxCardsPerWallet
		default:
			return s.config.CardLimits.Standard.MaxCardsPerWallet // Default to standard
		}
	}

	// Fallback to hard-coded defaults
	switch cardType {
	case "standard":
		return 3 // Maximum 3 standard cards per wallet
	case "premium":
		return 5 // Maximum 5 premium cards per wallet
	case "business":
		return 10 // Maximum 10 business cards per wallet
	default:
		return 1 // Default to 1 for unknown types
	}
}

// getCardLimitsByType returns spending limits based on card type and wallet status
func (s *PayCardService) getCardLimitsByType(cardType string, wallet *models.Wallet) CardLimits {
	// Use configuration if available, otherwise fallback to defaults
	if s.config != nil {
		switch cardType {
		case "standard":
			return CardLimits{
				SpendingLimit:        s.config.CardLimits.Standard.SpendingLimit,
				DailySpendingLimit:   s.config.CardLimits.Standard.DailySpendingLimit,
				MonthlySpendingLimit: s.config.CardLimits.Standard.MonthlySpendingLimit,
			}
		case "premium":
			return CardLimits{
				SpendingLimit:        s.config.CardLimits.Premium.SpendingLimit,
				DailySpendingLimit:   s.config.CardLimits.Premium.DailySpendingLimit,
				MonthlySpendingLimit: s.config.CardLimits.Premium.MonthlySpendingLimit,
			}
		case "business":
			return CardLimits{
				SpendingLimit:        s.config.CardLimits.Business.SpendingLimit,
				DailySpendingLimit:   s.config.CardLimits.Business.DailySpendingLimit,
				MonthlySpendingLimit: s.config.CardLimits.Business.MonthlySpendingLimit,
			}
		default:
			// Default to standard limits for unknown types
			return CardLimits{
				SpendingLimit:        s.config.CardLimits.Standard.SpendingLimit,
				DailySpendingLimit:   s.config.CardLimits.Standard.DailySpendingLimit,
				MonthlySpendingLimit: s.config.CardLimits.Standard.MonthlySpendingLimit,
			}
		}
	}

	// Fallback to hard-coded defaults if config is not available
	switch cardType {
	case "standard":
		return CardLimits{
			SpendingLimit:        5000.0,  // SZL 5,000 per transaction
			DailySpendingLimit:   1000.0,  // SZL 1,000 per day
			MonthlySpendingLimit: 10000.0, // SZL 10,000 per month
		}
	case "premium":
		return CardLimits{
			SpendingLimit:        15000.0, // SZL 15,000 per transaction
			DailySpendingLimit:   3000.0,  // SZL 3,000 per day
			MonthlySpendingLimit: 30000.0, // SZL 30,000 per month
		}
	case "business":
		return CardLimits{
			SpendingLimit:        50000.0,  // SZL 50,000 per transaction
			DailySpendingLimit:   10000.0,  // SZL 10,000 per day
			MonthlySpendingLimit: 100000.0, // SZL 100,000 per month
		}
	default:
		// Default limits for unknown types
		return CardLimits{
			SpendingLimit:        5000.0,  // SZL 5,000 per transaction
			DailySpendingLimit:   1000.0,  // SZL 1,000 per day
			MonthlySpendingLimit: 10000.0, // SZL 10,000 per month
		}
	}
}

// checkAmountAnomaly detects unusual transaction amounts
func (s *PayCardService) checkAmountAnomaly(card *models.PayCard, amount float64, recentTransactions []models.PayCardTransaction) error {
	// Check against card limits first
	if amount > card.SpendingLimit {
		return fmt.Errorf("amount exceeds card spending limit")
	}

	// If we have recent transaction history, check for anomalies
	if len(recentTransactions) >= 3 {
		var total float64
		for _, tx := range recentTransactions {
			total += tx.Amount
		}
		avgAmount := total / float64(len(recentTransactions))

		// Flag if amount is more than 5x the average
		if amount > avgAmount*5 {
			s.logger.LogSecurity("card_amount_anomaly", fmt.Sprintf("%d", card.WalletID), "system",
				fmt.Sprintf("Card %d: Amount %.2f is %.2fx average (%.2f)", card.ID, amount, amount/avgAmount, avgAmount))
			return fmt.Errorf("unusual transaction amount detected - potential fraud")
		}
	}

	// Check for very large amounts that might indicate fraud
	if amount > 100000.0 { // SZL 100,000
		s.logger.LogSecurity("card_large_amount", fmt.Sprintf("%d", card.WalletID), "system",
			fmt.Sprintf("Card %d: Large amount transaction %.2f", card.ID, amount))
		// Don't block, but log for review
	}

	return nil
}

// checkDuplicateTransactions detects potential duplicate transactions
func (s *PayCardService) checkDuplicateTransactions(card *models.PayCard, amount float64, merchantInfo map[string]interface{}, recentTransactions []models.PayCardTransaction) error {
	merchantName := ""
	if name, ok := merchantInfo["name"].(string); ok {
		merchantName = name
	}

	// Check for exact duplicate in last 5 minutes
	for _, tx := range recentTransactions {
		if tx.Amount == amount &&
			tx.MerchantName == merchantName &&
			time.Since(tx.CreatedAt) < 5*time.Minute {
			s.logger.LogSecurity("card_duplicate_transaction", fmt.Sprintf("%d", card.WalletID), "system",
				fmt.Sprintf("Card %d: Potential duplicate transaction %.2f at %s", card.ID, amount, merchantName))
			return fmt.Errorf("potential duplicate transaction detected")
		}
	}

	return nil
}

// checkGeographicAnomaly detects unusual geographic patterns
func (s *PayCardService) checkGeographicAnomaly(card *models.PayCard, merchantInfo map[string]interface{}) error {
	// Extract location information if available
	var currentLocation map[string]interface{}
	if location, ok := merchantInfo["location"].(map[string]interface{}); ok {
		currentLocation = location
	}

	if currentLocation == nil {
		return nil // No location data available
	}

	// Get recent transactions with location data
	var recentTransactionsWithLocation []models.PayCardTransaction
	if err := s.db.Where("card_id = ? AND created_at > ? AND location_info IS NOT NULL",
		card.ID, time.Now().Add(-24*time.Hour)).
		Find(&recentTransactionsWithLocation).Error; err != nil {
		return nil // Don't fail transaction if we can't check
	}

	// Simple geographic anomaly detection
	// In production, this would use proper geolocation services
	if len(recentTransactionsWithLocation) > 0 {
		// Check if current location is significantly different from recent ones
		// This is a simplified implementation
		s.logger.LogSecurity("card_location_check", fmt.Sprintf("%d", card.WalletID), "system",
			fmt.Sprintf("Card %d: Location-based transaction monitoring", card.ID))
	}

	return nil
}

// checkMerchantRestrictions validates merchant category restrictions
func (s *PayCardService) checkMerchantRestrictions(card *models.PayCard, merchantInfo map[string]interface{}) error {
	// Check if card has merchant restrictions
	if card.MerchantRestrictions == nil {
		return nil // No restrictions set
	}

	merchantCategory := ""
	if category, ok := merchantInfo["category"].(string); ok {
		merchantCategory = category
	}

	merchantName := ""
	if name, ok := merchantInfo["name"].(string); ok {
		merchantName = name
	}

	// Parse merchant restrictions from JSON
	var restrictions map[string]interface{}
	if err := json.Unmarshal(card.MerchantRestrictions, &restrictions); err != nil {
		return nil // Don't fail transaction if we can't parse restrictions
	}

	// Check blacklisted categories
	if blacklist, ok := restrictions["blacklisted_categories"].([]interface{}); ok {
		for _, category := range blacklist {
			if categoryStr, ok := category.(string); ok && categoryStr == merchantCategory {
				s.logger.LogSecurity("card_merchant_blocked", fmt.Sprintf("%d", card.WalletID), "system",
					fmt.Sprintf("Card %d: Transaction blocked - blacklisted category %s", card.ID, merchantCategory))
				return fmt.Errorf("transactions not allowed for merchant category: %s", merchantCategory)
			}
		}
	}

	// Check whitelisted merchants (if whitelist exists, only allow those)
	if whitelist, ok := restrictions["whitelisted_merchants"].([]interface{}); ok && len(whitelist) > 0 {
		allowed := false
		for _, merchant := range whitelist {
			if merchantMap, ok := merchant.(map[string]interface{}); ok {
				if whitelistedName, ok := merchantMap["name"].(string); ok && whitelistedName == merchantName {
					allowed = true
					break
				}
			}
		}
		if !allowed {
			s.logger.LogSecurity("card_merchant_not_whitelisted", fmt.Sprintf("%d", card.WalletID), "system",
				fmt.Sprintf("Card %d: Transaction blocked - merchant %s not whitelisted", card.ID, merchantName))
			return fmt.Errorf("merchant not in whitelist: %s", merchantName)
		}
	}

	return nil
}

// checkTimeRestrictions validates time-based transaction restrictions
func (s *PayCardService) checkTimeRestrictions(card *models.PayCard) error {
	now := time.Now()

	// Check if card has time restrictions in settings
	if card.Settings == nil {
		return nil // No time restrictions set
	}

	var settings map[string]interface{}
	if err := json.Unmarshal(card.Settings, &settings); err != nil {
		return nil // Don't fail transaction if we can't parse settings
	}

	// Check business hours restriction
	if businessHours, ok := settings["business_hours_only"].(bool); ok && businessHours {
		hour := now.Hour()
		if hour < 8 || hour > 18 { // 8 AM to 6 PM
			s.logger.LogSecurity("card_time_restricted", fmt.Sprintf("%d", card.WalletID), "system",
				fmt.Sprintf("Card %d: Transaction blocked - outside business hours", card.ID))
			return fmt.Errorf("transactions only allowed during business hours (8 AM - 6 PM)")
		}
	}

	// Check weekend restriction
	if noWeekends, ok := settings["no_weekends"].(bool); ok && noWeekends {
		weekday := now.Weekday()
		if weekday == time.Saturday || weekday == time.Sunday {
			s.logger.LogSecurity("card_weekend_restricted", fmt.Sprintf("%d", card.WalletID), "system",
				fmt.Sprintf("Card %d: Transaction blocked - weekend restriction", card.ID))
			return fmt.Errorf("transactions not allowed on weekends")
		}
	}

	// Check daily time windows
	if timeWindows, ok := settings["allowed_time_windows"].([]interface{}); ok && len(timeWindows) > 0 {
		currentTime := now.Format("15:04")
		allowed := false

		for _, window := range timeWindows {
			if windowMap, ok := window.(map[string]interface{}); ok {
				startTime, startOk := windowMap["start"].(string)
				endTime, endOk := windowMap["end"].(string)

				if startOk && endOk && currentTime >= startTime && currentTime <= endTime {
					allowed = true
					break
				}
			}
		}

		if !allowed {
			s.logger.LogSecurity("card_time_window_restricted", fmt.Sprintf("%d", card.WalletID), "system",
				fmt.Sprintf("Card %d: Transaction blocked - outside allowed time windows", card.ID))
			return fmt.Errorf("transaction not allowed at current time")
		}
	}

	return nil
}

// Enhanced card management methods implementation

// GetCardCount returns the number of cards for a wallet
func (s *PayCardService) GetCardCount(walletID uint) (int64, error) {
	var count int64
	if err := s.db.Model(&models.PayCard{}).
		Where("wallet_id = ? AND status IN ('active', 'suspended')", walletID).
		Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count cards: %w", err)
	}
	return count, nil
}

// GetActiveCardsByType returns active cards of a specific type for a wallet
func (s *PayCardService) GetActiveCardsByType(walletID uint, cardType string) ([]models.PayCardResponse, error) {
	var cards []models.PayCard
	if err := s.db.Where("wallet_id = ? AND card_type = ? AND status = 'active'", walletID, cardType).
		Find(&cards).Error; err != nil {
		return nil, fmt.Errorf("failed to get cards by type: %w", err)
	}

	var responses []models.PayCardResponse
	for _, card := range cards {
		responses = append(responses, *s.cardToResponse(&card))
	}

	return responses, nil
}

// SetSpendingLimits updates spending limits for a card
func (s *PayCardService) SetSpendingLimits(cardID uint, dailyLimit, monthlyLimit, transactionLimit float64) error {
	var card models.PayCard
	if err := s.db.First(&card, cardID).Error; err != nil {
		return fmt.Errorf("card not found")
	}

	// Validate limits
	if dailyLimit < 0 || monthlyLimit < 0 || transactionLimit < 0 {
		return fmt.Errorf("limits cannot be negative")
	}

	if dailyLimit > monthlyLimit && monthlyLimit > 0 {
		return fmt.Errorf("daily limit cannot exceed monthly limit")
	}

	updates := map[string]interface{}{
		"daily_spending_limit":   dailyLimit,
		"monthly_spending_limit": monthlyLimit,
		"spending_limit":         transactionLimit,
	}

	if err := s.db.Model(&card).Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to update spending limits: %w", err)
	}

	s.logger.LogPayCard(fmt.Sprintf("%d", cardID), fmt.Sprintf("%d", card.WalletID), "limits_updated", 0,
		fmt.Sprintf("Daily: %.2f, Monthly: %.2f, Transaction: %.2f", dailyLimit, monthlyLimit, transactionLimit))

	return nil
}

// SetMerchantRestrictions sets merchant category restrictions for a card
func (s *PayCardService) SetMerchantRestrictions(cardID uint, blacklistedCategories []string, whitelistedMerchants []string) error {
	var card models.PayCard
	if err := s.db.First(&card, cardID).Error; err != nil {
		return fmt.Errorf("card not found")
	}

	restrictions := map[string]interface{}{
		"blacklisted_categories": blacklistedCategories,
		"whitelisted_merchants":  whitelistedMerchants,
		"updated_at":             time.Now(),
	}

	restrictionsJSON, err := json.Marshal(restrictions)
	if err != nil {
		return fmt.Errorf("failed to marshal restrictions: %w", err)
	}

	updates := map[string]interface{}{
		"merchant_restrictions": datatypes.JSON(restrictionsJSON),
	}

	if err := s.db.Model(&card).Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to update merchant restrictions: %w", err)
	}

	s.logger.LogPayCard(fmt.Sprintf("%d", cardID), fmt.Sprintf("%d", card.WalletID), "merchant_restrictions_updated", 0,
		fmt.Sprintf("Blacklisted: %v, Whitelisted: %v", blacklistedCategories, whitelistedMerchants))

	return nil
}

// SetTimeRestrictions sets time-based restrictions for a card
func (s *PayCardService) SetTimeRestrictions(cardID uint, businessHoursOnly, noWeekends bool, allowedTimeWindows []map[string]string) error {
	var card models.PayCard
	if err := s.db.First(&card, cardID).Error; err != nil {
		return fmt.Errorf("card not found")
	}

	timeRestrictions := map[string]interface{}{
		"business_hours_only":  businessHoursOnly,
		"no_weekends":          noWeekends,
		"allowed_time_windows": allowedTimeWindows,
		"updated_at":           time.Now(),
	}

	// Get existing settings or create new ones
	var settings map[string]interface{}
	if card.Settings != nil {
		if err := json.Unmarshal(card.Settings, &settings); err != nil {
			settings = make(map[string]interface{})
		}
	} else {
		settings = make(map[string]interface{})
	}

	// Merge time restrictions into settings
	for key, value := range timeRestrictions {
		settings[key] = value
	}

	settingsJSON, err := json.Marshal(settings)
	if err != nil {
		return fmt.Errorf("failed to marshal settings: %w", err)
	}

	updates := map[string]interface{}{
		"settings": datatypes.JSON(settingsJSON),
	}

	if err := s.db.Model(&card).Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to update time restrictions: %w", err)
	}

	s.logger.LogPayCard(fmt.Sprintf("%d", cardID), fmt.Sprintf("%d", card.WalletID), "time_restrictions_updated", 0,
		fmt.Sprintf("Business hours: %t, No weekends: %t", businessHoursOnly, noWeekends))

	return nil
}

// GetCardSecuritySettings retrieves security settings for a card
func (s *PayCardService) GetCardSecuritySettings(cardID uint) (map[string]interface{}, error) {
	var card models.PayCard
	if err := s.db.First(&card, cardID).Error; err != nil {
		return nil, fmt.Errorf("card not found")
	}

	settings := map[string]interface{}{
		"card_id":                 card.ID,
		"status":                  card.Status,
		"contactless_enabled":     card.ContactlessEnabled,
		"online_payments_enabled": card.OnlinePaymentsEnabled,
		"atm_withdrawals_enabled": card.ATMWithdrawalsEnabled,
		"international_enabled":   card.InternationalEnabled,
		"is_pin_set":              card.IsPinSet,
		"failed_pin_attempts":     card.FailedPinAttempts,
		"spending_limit":          card.SpendingLimit,
		"daily_spending_limit":    card.DailySpendingLimit,
		"monthly_spending_limit":  card.MonthlySpendingLimit,
	}

	// Add merchant restrictions if they exist
	if card.MerchantRestrictions != nil {
		var restrictions map[string]interface{}
		if err := json.Unmarshal(card.MerchantRestrictions, &restrictions); err == nil {
			settings["merchant_restrictions"] = restrictions
		}
	}

	// Add time restrictions if they exist
	if card.Settings != nil {
		var cardSettings map[string]interface{}
		if err := json.Unmarshal(card.Settings, &cardSettings); err == nil {
			settings["time_restrictions"] = cardSettings
		}
	}

	return settings, nil
}

// EnableCardFeature enables a specific feature for a card
func (s *PayCardService) EnableCardFeature(cardID uint, feature string) error {
	var card models.PayCard
	if err := s.db.First(&card, cardID).Error; err != nil {
		return fmt.Errorf("card not found")
	}

	updates := make(map[string]interface{})

	switch feature {
	case "contactless":
		updates["contactless_enabled"] = true
	case "online_payments":
		updates["online_payments_enabled"] = true
	case "atm_withdrawals":
		updates["atm_withdrawals_enabled"] = true
	case "international":
		updates["international_enabled"] = true
	default:
		return fmt.Errorf("unknown feature: %s", feature)
	}

	if err := s.db.Model(&card).Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to enable feature: %w", err)
	}

	s.logger.LogPayCard(fmt.Sprintf("%d", cardID), fmt.Sprintf("%d", card.WalletID), "feature_enabled", 0, feature)

	return nil
}

// DisableCardFeature disables a specific feature for a card
func (s *PayCardService) DisableCardFeature(cardID uint, feature string) error {
	var card models.PayCard
	if err := s.db.First(&card, cardID).Error; err != nil {
		return fmt.Errorf("card not found")
	}

	updates := make(map[string]interface{})

	switch feature {
	case "contactless":
		updates["contactless_enabled"] = false
	case "online_payments":
		updates["online_payments_enabled"] = false
	case "atm_withdrawals":
		updates["atm_withdrawals_enabled"] = false
	case "international":
		updates["international_enabled"] = false
	default:
		return fmt.Errorf("unknown feature: %s", feature)
	}

	if err := s.db.Model(&card).Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to disable feature: %w", err)
	}

	s.logger.LogPayCard(fmt.Sprintf("%d", cardID), fmt.Sprintf("%d", card.WalletID), "feature_disabled", 0, feature)

	return nil
}

// GetCardUsageAnalytics provides usage analytics for a card
func (s *PayCardService) GetCardUsageAnalytics(cardID uint, days int) (map[string]interface{}, error) {
	var card models.PayCard
	if err := s.db.First(&card, cardID).Error; err != nil {
		return nil, fmt.Errorf("card not found")
	}

	if days <= 0 {
		days = 30 // Default to 30 days
	}

	startDate := time.Now().AddDate(0, 0, -days)

	// Get transaction statistics
	var totalTransactions int64
	var totalAmount float64
	var avgAmount float64

	if err := s.db.Model(&models.PayCardTransaction{}).
		Where("card_id = ? AND created_at >= ? AND status = 'completed'", cardID, startDate).
		Count(&totalTransactions).Error; err != nil {
		return nil, fmt.Errorf("failed to get transaction count: %w", err)
	}

	if err := s.db.Model(&models.PayCardTransaction{}).
		Where("card_id = ? AND created_at >= ? AND status = 'completed'", cardID, startDate).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&totalAmount).Error; err != nil {
		return nil, fmt.Errorf("failed to get total amount: %w", err)
	}

	if totalTransactions > 0 {
		avgAmount = totalAmount / float64(totalTransactions)
	}

	// Get merchant category breakdown
	var merchantCategories []struct {
		Category string  `json:"category"`
		Count    int64   `json:"count"`
		Amount   float64 `json:"amount"`
	}

	if err := s.db.Model(&models.PayCardTransaction{}).
		Where("card_id = ? AND created_at >= ? AND status = 'completed'", cardID, startDate).
		Select("merchant_category as category, COUNT(*) as count, COALESCE(SUM(amount), 0) as amount").
		Group("merchant_category").
		Scan(&merchantCategories).Error; err != nil {
		return nil, fmt.Errorf("failed to get merchant categories: %w", err)
	}

	// Get daily transaction patterns
	var dailyStats []struct {
		Date   string  `json:"date"`
		Count  int64   `json:"count"`
		Amount float64 `json:"amount"`
	}

	if err := s.db.Model(&models.PayCardTransaction{}).
		Where("card_id = ? AND created_at >= ? AND status = 'completed'", cardID, startDate).
		Select("DATE(created_at) as date, COUNT(*) as count, COALESCE(SUM(amount), 0) as amount").
		Group("DATE(created_at)").
		Order("date").
		Scan(&dailyStats).Error; err != nil {
		return nil, fmt.Errorf("failed to get daily stats: %w", err)
	}

	// Calculate fraud indicators
	var suspiciousTransactions int64
	if err := s.db.Model(&models.PayCardTransaction{}).
		Where("card_id = ? AND created_at >= ? AND (status = 'blocked' OR notes LIKE '%fraud%')", cardID, startDate).
		Count(&suspiciousTransactions).Error; err != nil {
		suspiciousTransactions = 0 // Don't fail if we can't get this data
	}

	analytics := map[string]interface{}{
		"period_days":             days,
		"start_date":              startDate.Format("2006-01-02"),
		"end_date":                time.Now().Format("2006-01-02"),
		"total_transactions":      totalTransactions,
		"total_amount":            totalAmount,
		"average_amount":          avgAmount,
		"suspicious_transactions": suspiciousTransactions,
		"merchant_categories":     merchantCategories,
		"daily_stats":             dailyStats,
		"card_limits": map[string]interface{}{
			"daily_limit":       card.DailySpendingLimit,
			"monthly_limit":     card.MonthlySpendingLimit,
			"transaction_limit": card.SpendingLimit,
		},
		"current_usage": map[string]interface{}{
			"daily_spent":   card.CurrentDailySpent,
			"monthly_spent": card.CurrentMonthlySpent,
		},
	}

	return analytics, nil
}
