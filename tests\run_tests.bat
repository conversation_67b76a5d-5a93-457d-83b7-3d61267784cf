@echo off
REM Wallet Platform Test Runner for Windows
REM This script runs all tests for the wallet platform

echo 🧪 Starting Wallet Platform Test Suite
echo ======================================

REM Test configuration
set TEST_DB_PATH=:memory:
set INTERNAL_API_KEY=test-internal-key
set APP_ENVIRONMENT=test

REM Function to print status messages
echo [INFO] Setting up test environment...

REM Check if Go is installed
go version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Go is not installed or not in PATH
    exit /b 1
)

REM Get Go version
for /f "tokens=3" %%i in ('go version') do set GO_VERSION=%%i
echo [INFO] Using Go version: %GO_VERSION%

REM Navigate to the wallet platform directory
cd /d "%~dp0\.."

echo.
echo [INFO] Phase 1: Unit Tests
echo -------------------
cd tests
go test -v -tags=unit ./unit/...
set UNIT_TESTS_RESULT=%errorlevel%

echo.
echo [INFO] Phase 2: Integration Tests
echo -------------------------
go test -v -tags=integration ./integration/...
set INTEGRATION_TESTS_RESULT=%errorlevel%

echo.
echo [INFO] Phase 3: Load Tests (Optional)
echo ------------------
echo [WARNING] Load tests require a running server instance
echo [WARNING] Make sure the wallet platform is running on localhost:8086
set /p LOAD_TESTS_CHOICE="Do you want to run load tests? (y/N): "
if /i "%LOAD_TESTS_CHOICE%"=="y" (
    go test -v -timeout=5m ./load/...
    set LOAD_TESTS_RESULT=%errorlevel%
) else (
    echo [WARNING] Skipping load tests
    set LOAD_TESTS_RESULT=0
)

echo.
echo [INFO] Phase 4: End-to-End Tests (Optional)
echo ------------------------
echo [WARNING] E2E tests require a running server instance
echo [WARNING] Make sure the wallet platform is running on localhost:8086
set /p E2E_TESTS_CHOICE="Do you want to run E2E tests? (y/N): "
if /i "%E2E_TESTS_CHOICE%"=="y" (
    go test -v -timeout=10m ./e2e/...
    set E2E_TESTS_RESULT=%errorlevel%
) else (
    echo [WARNING] Skipping E2E tests
    set E2E_TESTS_RESULT=0
)

echo.
echo [INFO] Test Results Summary
echo ====================

if %UNIT_TESTS_RESULT%==0 (
    echo [SUCCESS] ✅ Unit Tests: PASSED
) else (
    echo [ERROR] ❌ Unit Tests: FAILED
)

if %INTEGRATION_TESTS_RESULT%==0 (
    echo [SUCCESS] ✅ Integration Tests: PASSED
) else (
    echo [ERROR] ❌ Integration Tests: FAILED
)

if %LOAD_TESTS_RESULT%==0 (
    echo [SUCCESS] ✅ Load Tests: PASSED
) else (
    echo [ERROR] ❌ Load Tests: FAILED
)

if %E2E_TESTS_RESULT%==0 (
    echo [SUCCESS] ✅ E2E Tests: PASSED
) else (
    echo [ERROR] ❌ E2E Tests: FAILED
)

echo.

REM Calculate overall result
set /a TOTAL_RESULT=%UNIT_TESTS_RESULT%+%INTEGRATION_TESTS_RESULT%+%LOAD_TESTS_RESULT%+%E2E_TESTS_RESULT%

if %TOTAL_RESULT%==0 (
    echo [SUCCESS] 🎉 All tests passed successfully!
    echo.
    echo [INFO] The wallet platform is ready for deployment
    exit /b 0
) else (
    echo [ERROR] ❌ Some tests failed
    echo.
    echo [INFO] Please review the failed tests and fix any issues
    exit /b 1
)
