package services

import (
	"fmt"
	"time"

	"wallet-platform/internal/models"
	"wallet-platform/pkg/logger"
	"wallet-platform/pkg/redis"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// AnalyticsService implements the AnalyticsServiceInterface
type AnalyticsService struct {
	db     *gorm.DB
	redis  *redis.Client
	logger *logger.Logger
}

// NewAnalyticsService creates a new analytics service
func NewAnalyticsService(db *gorm.DB, redisClient *redis.Client, log *logger.Logger) *AnalyticsService {
	return &AnalyticsService{
		db:     db,
		redis:  redisClient,
		logger: log,
	}
}

// GenerateWalletAnalytics generates analytics for a specific wallet
func (s *AnalyticsService) GenerateWalletAnalytics(walletID uint, period string, startDate, endDate string) (*AnalyticsResponse, error) {
	start, err := time.Parse("2006-01-02", startDate)
	if err != nil {
		return nil, fmt.Errorf("invalid start date format")
	}

	end, err := time.Parse("2006-01-02", endDate)
	if err != nil {
		return nil, fmt.Errorf("invalid end date format")
	}

	// Get transaction data
	var transactions []models.WalletTransaction
	if err := s.db.Where("wallet_id = ? AND created_at BETWEEN ? AND ?", walletID, start, end).
		Find(&transactions).Error; err != nil {
		return nil, fmt.Errorf("failed to get transaction data: %w", err)
	}

	// Calculate metrics
	totalTransactions := len(transactions)
	var totalCredits, totalDebits float64
	var creditCount, debitCount int
	categoryBreakdown := make(map[string]float64)
	dailyData := make(map[string]map[string]interface{})

	for _, tx := range transactions {
		if tx.Type == "credit" {
			totalCredits += tx.Amount
			creditCount++
		} else {
			totalDebits += tx.Amount
			debitCount++
		}

		categoryBreakdown[tx.Category] += tx.Amount

		// Daily breakdown
		day := tx.CreatedAt.Format("2006-01-02")
		if dailyData[day] == nil {
			dailyData[day] = map[string]interface{}{
				"date":         day,
				"transactions": 0,
				"credits":      0.0,
				"debits":       0.0,
			}
		}
		dailyData[day]["transactions"] = dailyData[day]["transactions"].(int) + 1
		if tx.Type == "credit" {
			dailyData[day]["credits"] = dailyData[day]["credits"].(float64) + tx.Amount
		} else {
			dailyData[day]["debits"] = dailyData[day]["debits"].(float64) + tx.Amount
		}
	}

	// Convert daily data to slice
	var data []map[string]interface{}
	for _, dayData := range dailyData {
		data = append(data, dayData)
	}

	// Create summary
	summary := map[string]interface{}{
		"total_transactions": totalTransactions,
		"total_credits":      totalCredits,
		"total_debits":       totalDebits,
		"credit_count":       creditCount,
		"debit_count":        debitCount,
		"net_flow":           totalCredits - totalDebits,
		"category_breakdown": categoryBreakdown,
	}

	// Create charts data
	charts := []map[string]interface{}{
		{
			"type":  "line",
			"title": "Daily Transaction Volume",
			"data":  data,
		},
		{
			"type":  "pie",
			"title": "Transaction Categories",
			"data":  categoryBreakdown,
		},
	}

	s.logger.LogAnalytics("wallet_analytics", period, totalTransactions, time.Since(start).Milliseconds())

	return &AnalyticsResponse{
		ReportType: "wallet_analytics",
		PeriodType: period,
		Data:       data,
		Summary:    summary,
		Charts:     charts,
	}, nil
}

// GenerateCardAnalytics generates analytics for a specific card
func (s *AnalyticsService) GenerateCardAnalytics(cardID uint, period string, startDate, endDate string) (*AnalyticsResponse, error) {
	start, err := time.Parse("2006-01-02", startDate)
	if err != nil {
		return nil, fmt.Errorf("invalid start date format")
	}

	end, err := time.Parse("2006-01-02", endDate)
	if err != nil {
		return nil, fmt.Errorf("invalid end date format")
	}

	// Get card transaction data
	var transactions []models.PayCardTransaction
	if err := s.db.Where("card_id = ? AND created_at BETWEEN ? AND ?", cardID, start, end).
		Find(&transactions).Error; err != nil {
		return nil, fmt.Errorf("failed to get card transaction data: %w", err)
	}

	// Calculate metrics
	totalTransactions := len(transactions)
	var totalAmount float64
	merchantBreakdown := make(map[string]float64)
	categoryBreakdown := make(map[string]float64)
	dailyData := make(map[string]map[string]interface{})

	for _, tx := range transactions {
		totalAmount += tx.Amount
		merchantBreakdown[tx.MerchantName] += tx.Amount
		categoryBreakdown[tx.MerchantCategory] += tx.Amount

		// Daily breakdown
		day := tx.CreatedAt.Format("2006-01-02")
		if dailyData[day] == nil {
			dailyData[day] = map[string]interface{}{
				"date":         day,
				"transactions": 0,
				"amount":       0.0,
			}
		}
		dailyData[day]["transactions"] = dailyData[day]["transactions"].(int) + 1
		dailyData[day]["amount"] = dailyData[day]["amount"].(float64) + tx.Amount
	}

	// Convert daily data to slice
	var data []map[string]interface{}
	for _, dayData := range dailyData {
		data = append(data, dayData)
	}

	// Create summary
	summary := map[string]interface{}{
		"total_transactions":  totalTransactions,
		"total_amount":        totalAmount,
		"average_transaction": totalAmount / float64(totalTransactions),
		"merchant_breakdown":  merchantBreakdown,
		"category_breakdown":  categoryBreakdown,
	}

	// Create charts data
	charts := []map[string]interface{}{
		{
			"type":  "line",
			"title": "Daily Spending",
			"data":  data,
		},
		{
			"type":  "pie",
			"title": "Spending by Category",
			"data":  categoryBreakdown,
		},
	}

	s.logger.LogAnalytics("card_analytics", period, totalTransactions, time.Since(start).Milliseconds())

	return &AnalyticsResponse{
		ReportType: "card_analytics",
		PeriodType: period,
		Data:       data,
		Summary:    summary,
		Charts:     charts,
	}, nil
}

// GenerateSystemAnalytics generates system-wide analytics
func (s *AnalyticsService) GenerateSystemAnalytics(period string, startDate, endDate string) (*AnalyticsResponse, error) {
	start, err := time.Parse("2006-01-02", startDate)
	if err != nil {
		return nil, fmt.Errorf("invalid start date format")
	}

	end, err := time.Parse("2006-01-02", endDate)
	if err != nil {
		return nil, fmt.Errorf("invalid end date format")
	}

	// Get system metrics
	var totalWallets int64
	s.db.Model(&models.Wallet{}).Count(&totalWallets)

	var activeWallets int64
	s.db.Model(&models.Wallet{}).Where("status = 'active'").Count(&activeWallets)

	var totalCards int64
	s.db.Model(&models.PayCard{}).Count(&totalCards)

	var activeCards int64
	s.db.Model(&models.PayCard{}).Where("status = 'active'").Count(&activeCards)

	// Get transaction metrics
	var walletTransactions []models.WalletTransaction
	s.db.Where("created_at BETWEEN ? AND ?", start, end).Find(&walletTransactions)

	var cardTransactions []models.PayCardTransaction
	s.db.Where("created_at BETWEEN ? AND ?", start, end).Find(&cardTransactions)

	// Calculate daily metrics
	dailyData := make(map[string]map[string]interface{})

	for _, tx := range walletTransactions {
		day := tx.CreatedAt.Format("2006-01-02")
		if dailyData[day] == nil {
			dailyData[day] = map[string]interface{}{
				"date":                day,
				"wallet_transactions": 0,
				"wallet_volume":       0.0,
				"card_transactions":   0,
				"card_volume":         0.0,
			}
		}
		dailyData[day]["wallet_transactions"] = dailyData[day]["wallet_transactions"].(int) + 1
		dailyData[day]["wallet_volume"] = dailyData[day]["wallet_volume"].(float64) + tx.Amount
	}

	for _, tx := range cardTransactions {
		day := tx.CreatedAt.Format("2006-01-02")
		if dailyData[day] == nil {
			dailyData[day] = map[string]interface{}{
				"date":                day,
				"wallet_transactions": 0,
				"wallet_volume":       0.0,
				"card_transactions":   0,
				"card_volume":         0.0,
			}
		}
		dailyData[day]["card_transactions"] = dailyData[day]["card_transactions"].(int) + 1
		dailyData[day]["card_volume"] = dailyData[day]["card_volume"].(float64) + tx.Amount
	}

	// Convert to slice
	var data []map[string]interface{}
	for _, dayData := range dailyData {
		data = append(data, dayData)
	}

	// Create summary
	summary := map[string]interface{}{
		"total_wallets":       totalWallets,
		"active_wallets":      activeWallets,
		"total_cards":         totalCards,
		"active_cards":        activeCards,
		"wallet_transactions": len(walletTransactions),
		"card_transactions":   len(cardTransactions),
	}

	// Create charts
	charts := []map[string]interface{}{
		{
			"type":  "line",
			"title": "Daily Transaction Volume",
			"data":  data,
		},
	}

	s.logger.LogAnalytics("system_analytics", period, len(walletTransactions)+len(cardTransactions), time.Since(start).Milliseconds())

	return &AnalyticsResponse{
		ReportType: "system_analytics",
		PeriodType: period,
		Data:       data,
		Summary:    summary,
		Charts:     charts,
	}, nil
}

// GetDashboardMetrics returns real-time dashboard metrics
func (s *AnalyticsService) GetDashboardMetrics() (*DashboardMetrics, error) {
	var metrics DashboardMetrics

	// Get wallet metrics
	var totalWallets, activeWallets, totalCards, activeCards, transactionsToday int64
	s.db.Model(&models.Wallet{}).Count(&totalWallets)
	s.db.Model(&models.Wallet{}).Where("status = 'active'").Count(&activeWallets)

	// Get total balance
	var totalBalance float64
	s.db.Model(&models.Wallet{}).Select("COALESCE(SUM(balance), 0)").Scan(&totalBalance)

	// Get card metrics
	s.db.Model(&models.PayCard{}).Count(&totalCards)
	s.db.Model(&models.PayCard{}).Where("status = 'active'").Count(&activeCards)

	// Get today's transactions
	today := time.Now().Format("2006-01-02")
	s.db.Model(&models.WalletTransaction{}).Where("DATE(created_at) = ?", today).Count(&transactionsToday)

	// Convert to int and assign to metrics
	metrics.TotalWallets = int(totalWallets)
	metrics.ActiveWallets = int(activeWallets)
	metrics.TotalBalance = totalBalance
	metrics.TotalCards = int(totalCards)
	metrics.ActiveCards = int(activeCards)
	metrics.TransactionsToday = int(transactionsToday)

	// Get today's volume
	var volumeToday float64
	s.db.Model(&models.WalletTransaction{}).Where("DATE(created_at) = ?", today).Select("COALESCE(SUM(amount), 0)").Scan(&volumeToday)
	metrics.VolumeToday = volumeToday

	return &metrics, nil
}

// CreateReport creates a new analytics report
func (s *AnalyticsService) CreateReport(reportType, period string, filters map[string]interface{}) (*AnalyticsReport, error) {
	report := models.AnalyticsReport{
		ReportType: reportType,
		ReportName: fmt.Sprintf("%s_%s_%d", reportType, period, time.Now().Unix()),
		Status:     "generating",
		Filters:    datatypes.JSON("{}"),
		Data:       datatypes.JSON("{}"),
	}

	if err := s.db.Create(&report).Error; err != nil {
		return nil, fmt.Errorf("failed to create report: %w", err)
	}

	// TODO: Implement async report generation
	// For now, just mark as completed
	report.Status = "completed"
	report.FileURL = fmt.Sprintf("/reports/%d.pdf", report.ID)
	s.db.Save(&report)

	return &AnalyticsReport{
		ID:         report.ID,
		ReportType: report.ReportType,
		ReportName: report.ReportName,
		Status:     report.Status,
		Data:       make(map[string]interface{}),
		FileURL:    report.FileURL,
	}, nil
}

// ScheduleReport schedules a recurring report
func (s *AnalyticsService) ScheduleReport(reportType, schedule string, parameters map[string]interface{}) (*ReportSchedule, error) {
	reportSchedule := models.ReportSchedule{
		ReportType:      reportType,
		ReportName:      fmt.Sprintf("Scheduled_%s_%d", reportType, time.Now().Unix()),
		SchedulePattern: schedule,
		IsActive:        true,
		Parameters:      datatypes.JSON("{}"),
	}

	if err := s.db.Create(&reportSchedule).Error; err != nil {
		return nil, fmt.Errorf("failed to schedule report: %w", err)
	}

	return &ReportSchedule{
		ID:              reportSchedule.ID,
		ReportType:      reportSchedule.ReportType,
		ReportName:      reportSchedule.ReportName,
		SchedulePattern: reportSchedule.SchedulePattern,
		IsActive:        reportSchedule.IsActive,
		Parameters:      make(map[string]interface{}),
	}, nil
}
