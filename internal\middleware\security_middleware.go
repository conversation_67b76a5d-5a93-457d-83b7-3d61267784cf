package middleware

import (
	"net/http"
	"strings"
	"time"

	"wallet-platform/internal/services"
	"wallet-platform/pkg/logger"

	"github.com/gin-gonic/gin"
)

// SecurityMiddleware provides advanced security middleware
type SecurityMiddleware struct {
	fraudDetectionService   *services.FraudDetectionService
	deviceManagementService *services.DeviceManagementService
	ipGeolocationService    *services.IPGeolocationService
	enhanced2FAService      *services.Enhanced2FAService
	logger                  *logger.Logger
}

// NewSecurityMiddleware creates a new security middleware
func NewSecurityMiddleware(container *services.Container) *SecurityMiddleware {
	return &SecurityMiddleware{
		fraudDetectionService:   container.FraudDetectionService,
		deviceManagementService: container.DeviceManagementService,
		ipGeolocationService:    container.IPGeolocationService,
		enhanced2FAService:      container.Enhanced2FAService,
		logger:                  container.Logger,
	}
}

// DeviceFingerprinting middleware extracts and validates device fingerprints
func (s *SecurityMiddleware) DeviceFingerprinting() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Extract device information from headers
		deviceInfo := &services.DeviceInfo{
			DeviceType: s.extractHeader(c, "X-Device-Type", "unknown"),
			OS:         s.extractHeader(c, "X-Device-OS", "unknown"),
			OSVersion:  s.extractHeader(c, "X-Device-OS-Version", "unknown"),
			AppVersion: s.extractHeader(c, "X-App-Version", "unknown"),
			Model:      s.extractHeader(c, "X-Device-Model", "unknown"),
			Brand:      s.extractHeader(c, "X-Device-Brand", "unknown"),
			UserAgent:  c.GetHeader("User-Agent"),
			ScreenSize: s.extractHeader(c, "X-Screen-Size", "unknown"),
			Timezone:   s.extractHeader(c, "X-Timezone", "unknown"),
			Language:   s.extractHeader(c, "X-Language", "unknown"),
			IPAddress:  c.ClientIP(),
			Location:   s.extractHeader(c, "X-Location", "unknown"),
			Metadata:   make(map[string]interface{}),
		}

		// Store device info in context for later use
		c.Set("device_info", deviceInfo)

		// Generate device fingerprint
		fingerprint := s.generateDeviceFingerprint(deviceInfo)
		c.Set("device_fingerprint", fingerprint)

		// Log device access
		s.logger.LogSecurity("device_access", "", deviceInfo.IPAddress,
			"Device fingerprint: "+fingerprint)

		c.Next()
	}
}

// FraudDetection middleware performs real-time fraud detection
func (s *SecurityMiddleware) FraudDetection() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Skip fraud detection for non-transaction endpoints
		if !s.isTransactionEndpoint(c.Request.URL.Path) {
			c.Next()
			return
		}

		// Get wallet ID from context (set by auth middleware)
		walletIDInterface, exists := c.Get("wallet_id")
		if !exists {
			c.Next()
			return
		}

		walletID, ok := walletIDInterface.(uint)
		if !ok {
			c.Next()
			return
		}

		// Extract transaction data
		transactionData := s.extractTransactionData(c)

		// Perform fraud analysis
		result, err := s.fraudDetectionService.AnalyzeTransaction(walletID, 0.0, transactionData)
		if err != nil {
			s.logger.LogError(err, map[string]interface{}{
				"action":    "fraud_detection_middleware",
				"wallet_id": walletID,
			})
			c.Next()
			return
		}

		// Store fraud analysis result in context
		c.Set("fraud_analysis", result)

		// Block high-risk transactions
		if result.RiskLevel == "CRITICAL" || (result.RiskLevel == "HIGH" && result.RequireAction) {
			s.logger.LogSecurity("transaction_blocked", string(rune(walletID)), c.ClientIP(),
				"Transaction blocked due to high fraud risk")

			c.JSON(http.StatusForbidden, gin.H{
				"error":   "Transaction blocked",
				"message": "This transaction has been blocked due to security concerns",
				"code":    "FRAUD_DETECTED",
			})
			c.Abort()
			return
		}

		// Require 2FA for medium-high risk transactions
		if result.RiskLevel == "HIGH" || result.RiskLevel == "MEDIUM" {
			c.Set("require_2fa", true)
		}

		c.Next()
	}
}

// IPGeolocation middleware analyzes IP geolocation for security
func (s *SecurityMiddleware) IPGeolocation() gin.HandlerFunc {
	return func(c *gin.Context) {
		ipAddress := c.ClientIP()

		// Get wallet ID from context
		walletIDInterface, exists := c.Get("wallet_id")
		if !exists {
			c.Next()
			return
		}

		walletID, ok := walletIDInterface.(uint)
		if !ok {
			c.Next()
			return
		}

		// Analyze IP location
		result, err := s.ipGeolocationService.AnalyzeIPLocation(ipAddress, walletID)
		if err != nil {
			s.logger.LogError(err, map[string]interface{}{
				"action":     "ip_geolocation_middleware",
				"ip_address": ipAddress,
				"wallet_id":  walletID,
			})
			c.Next()
			return
		}

		// Store geolocation result in context
		c.Set("geolocation_analysis", result)

		// Block access from restricted locations
		if !result.IsAllowed {
			s.logger.LogSecurity("access_blocked_location", string(rune(walletID)), ipAddress,
				"Access blocked due to geographic restrictions")

			c.JSON(http.StatusForbidden, gin.H{
				"error":   "Access denied",
				"message": "Access from your location is not permitted",
				"code":    "GEOGRAPHIC_RESTRICTION",
			})
			c.Abort()
			return
		}

		// Add security headers based on risk level
		if result.RiskLevel == "HIGH" || result.RiskLevel == "CRITICAL" {
			c.Set("require_additional_verification", true)
		}

		c.Next()
	}
}

// DeviceVerification middleware ensures device is registered and trusted
func (s *SecurityMiddleware) DeviceVerification() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get wallet ID and device fingerprint from context
		walletIDInterface, exists := c.Get("wallet_id")
		if !exists {
			c.Next()
			return
		}

		walletID, ok := walletIDInterface.(uint)
		if !ok {
			c.Next()
			return
		}

		deviceFingerprint, exists := c.Get("device_fingerprint")
		if !exists {
			c.Next()
			return
		}

		fingerprintStr, ok := deviceFingerprint.(string)
		if !ok {
			c.Next()
			return
		}

		// Check if device is trusted
		isTrusted, err := s.deviceManagementService.IsDeviceTrusted(walletID, fingerprintStr)
		if err != nil {
			s.logger.LogError(err, map[string]interface{}{
				"action":             "device_verification_middleware",
				"wallet_id":          walletID,
				"device_fingerprint": fingerprintStr,
			})
			c.Next()
			return
		}

		// Store device trust status in context
		c.Set("device_trusted", isTrusted)

		// For sensitive operations, require device verification
		if s.isSensitiveEndpoint(c.Request.URL.Path) && !isTrusted {
			s.logger.LogSecurity("untrusted_device_access", string(rune(walletID)), c.ClientIP(),
				"Untrusted device attempting sensitive operation")

			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "Device verification required",
				"message": "This device is not verified for sensitive operations",
				"code":    "DEVICE_NOT_VERIFIED",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// TwoFactorAuthRequired middleware enforces 2FA when required
func (s *SecurityMiddleware) TwoFactorAuthRequired() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Check if 2FA is required (set by fraud detection or other middleware)
		require2FA, exists := c.Get("require_2fa")
		if !exists || !require2FA.(bool) {
			c.Next()
			return
		}

		// Get wallet ID from context
		walletIDInterface, exists := c.Get("wallet_id")
		if !exists {
			c.Next()
			return
		}

		walletID, ok := walletIDInterface.(uint)
		if !ok {
			c.Next()
			return
		}

		// Check if 2FA code is provided
		twoFACode := c.GetHeader("X-2FA-Code")
		if twoFACode == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "Two-factor authentication required",
				"message": "Please provide a valid 2FA code",
				"code":    "2FA_REQUIRED",
			})
			c.Abort()
			return
		}

		// Verify 2FA code
		result, err := s.enhanced2FAService.VerifyTOTP(walletID, twoFACode)
		if err != nil {
			s.logger.LogError(err, map[string]interface{}{
				"action":    "2fa_verification_middleware",
				"wallet_id": walletID,
			})
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "2FA verification failed",
				"message": "Unable to verify 2FA code",
				"code":    "2FA_VERIFICATION_ERROR",
			})
			c.Abort()
			return
		}

		if !result.Success {
			s.logger.LogSecurity("2fa_verification_failed", string(rune(walletID)), c.ClientIP(),
				"2FA verification failed: "+result.Message)

			c.JSON(http.StatusUnauthorized, gin.H{
				"error":             "2FA verification failed",
				"message":           result.Message,
				"code":              "2FA_INVALID",
				"remaining_attempts": result.RemainingAttempts,
				"locked_until":      result.LockedUntil,
			})
			c.Abort()
			return
		}

		// 2FA successful, continue
		s.logger.LogSecurity("2fa_verification_success", string(rune(walletID)), c.ClientIP(),
			"2FA verification successful")

		c.Next()
	}
}

// Helper methods

func (s *SecurityMiddleware) extractHeader(c *gin.Context, header, defaultValue string) string {
	value := c.GetHeader(header)
	if value == "" {
		return defaultValue
	}
	return value
}

func (s *SecurityMiddleware) generateDeviceFingerprint(deviceInfo *services.DeviceInfo) string {
	// This would use the same logic as DeviceManagementService
	// For now, return a simple concatenation
	return deviceInfo.DeviceType + "|" + deviceInfo.OS + "|" + deviceInfo.Model
}

func (s *SecurityMiddleware) isTransactionEndpoint(path string) bool {
	transactionPaths := []string{
		"/api/v1/wallets/transfer",
		"/api/v1/wallets/topup",
		"/api/v1/paycards/transaction",
		"/api/v1/paycards/payment",
	}

	for _, txPath := range transactionPaths {
		if strings.Contains(path, txPath) {
			return true
		}
	}
	return false
}

func (s *SecurityMiddleware) isSensitiveEndpoint(path string) bool {
	sensitivePaths := []string{
		"/api/v1/wallets/transfer",
		"/api/v1/paycards/pin",
		"/api/v1/security/2fa",
		"/api/v1/security/disable",
	}

	for _, sensPath := range sensitivePaths {
		if strings.Contains(path, sensPath) {
			return true
		}
	}
	return false
}

func (s *SecurityMiddleware) extractTransactionData(c *gin.Context) map[string]interface{} {
	data := make(map[string]interface{})

	// Extract device info
	if deviceInfo, exists := c.Get("device_info"); exists {
		if di, ok := deviceInfo.(*services.DeviceInfo); ok {
			data["device_fingerprint"] = s.generateDeviceFingerprint(di)
			data["ip_address"] = di.IPAddress
			data["location"] = di.Location
			data["user_agent"] = di.UserAgent
		}
	}

	// Extract additional context
	data["endpoint"] = c.Request.URL.Path
	data["method"] = c.Request.Method
	data["timestamp"] = time.Now()

	return data
}
