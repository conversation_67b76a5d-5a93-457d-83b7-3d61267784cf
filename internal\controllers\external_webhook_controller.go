package controllers

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"wallet-platform/internal/services"

	"github.com/gin-gonic/gin"
)

// ExternalWebhookController handles external webhook requests from third-party services
type ExternalWebhookController struct {
	container *services.Container
}

// NewExternalWebhookController creates a new external webhook controller
func NewExternalWebhookController(container *services.Container) *ExternalWebhookController {
	return &ExternalWebhookController{
		container: container,
	}
}

// PaymentEngineWebhook handles webhooks from payment engine
func (ewc *ExternalWebhookController) PaymentEngineWebhook(c *gin.Context) {
	// Read the request body
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_PAYLOAD",
			Message: "Failed to read request body",
			Code:    "400",
		})
		return
	}

	// Verify signature
	signature := c.GetHeader("X-Payment-Signature")
	if !ewc.verifyPaymentEngineSignature(body, signature) {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "INVALID_SIGNATURE",
			Message: "Payment engine signature verification failed",
			Code:    "401",
		})
		return
	}

	// Parse webhook payload
	var payload PaymentEngineWebhookPayload
	if err := json.Unmarshal(body, &payload); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_JSON",
			Message: "Invalid JSON payload",
			Code:    "400",
		})
		return
	}

	// Process the payment engine webhook
	err = ewc.processPaymentEngineWebhook(&payload)
	if err != nil {
		ewc.container.Logger.LogError(err, map[string]interface{}{
			"webhook_type": "payment_engine",
			"event_type":   payload.EventType,
		})
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "PROCESSING_FAILED",
			Message: "Failed to process webhook",
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Payment engine webhook processed successfully",
	})
}

// SMSProviderWebhook handles webhooks from SMS provider
func (ewc *ExternalWebhookController) SMSProviderWebhook(c *gin.Context) {
	// Read the request body
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_PAYLOAD",
			Message: "Failed to read request body",
			Code:    "400",
		})
		return
	}

	// Parse webhook payload
	var payload SMSProviderWebhookPayload
	if err := json.Unmarshal(body, &payload); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_JSON",
			Message: "Invalid JSON payload",
			Code:    "400",
		})
		return
	}

	// Process the SMS provider webhook
	err = ewc.processSMSProviderWebhook(&payload)
	if err != nil {
		ewc.container.Logger.LogError(err, map[string]interface{}{
			"webhook_type": "sms_provider",
			"event_type":   payload.EventType,
		})
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "PROCESSING_FAILED",
			Message: "Failed to process webhook",
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "SMS provider webhook processed successfully",
	})
}

// EmailProviderWebhook handles webhooks from email provider
func (ewc *ExternalWebhookController) EmailProviderWebhook(c *gin.Context) {
	// Read the request body
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_PAYLOAD",
			Message: "Failed to read request body",
			Code:    "400",
		})
		return
	}

	// Parse webhook payload
	var payload EmailProviderWebhookPayload
	if err := json.Unmarshal(body, &payload); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_JSON",
			Message: "Invalid JSON payload",
			Code:    "400",
		})
		return
	}

	// Process the email provider webhook
	err = ewc.processEmailProviderWebhook(&payload)
	if err != nil {
		ewc.container.Logger.LogError(err, map[string]interface{}{
			"webhook_type": "email_provider",
			"event_type":   payload.EventType,
		})
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "PROCESSING_FAILED",
			Message: "Failed to process webhook",
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Email provider webhook processed successfully",
	})
}

// FraudDetectionWebhook handles webhooks from fraud detection service
func (ewc *ExternalWebhookController) FraudDetectionWebhook(c *gin.Context) {
	// Read the request body
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_PAYLOAD",
			Message: "Failed to read request body",
			Code:    "400",
		})
		return
	}

	// Verify signature
	signature := c.GetHeader("X-Fraud-Signature")
	if !ewc.verifyFraudDetectionSignature(body, signature) {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "INVALID_SIGNATURE",
			Message: "Fraud detection signature verification failed",
			Code:    "401",
		})
		return
	}

	// Parse webhook payload
	var payload FraudDetectionWebhookPayload
	if err := json.Unmarshal(body, &payload); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_JSON",
			Message: "Invalid JSON payload",
			Code:    "400",
		})
		return
	}

	// Process the fraud detection webhook
	err = ewc.processFraudDetectionWebhook(&payload)
	if err != nil {
		ewc.container.Logger.LogError(err, map[string]interface{}{
			"webhook_type": "fraud_detection",
			"event_type":   payload.EventType,
		})
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "PROCESSING_FAILED",
			Message: "Failed to process webhook",
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Fraud detection webhook processed successfully",
	})
}

// Webhook Payload Structures

type PaymentEngineWebhookPayload struct {
	EventType     string                 `json:"event_type"`
	TransactionID string                 `json:"transaction_id"`
	WalletID      uint                   `json:"wallet_id"`
	Amount        float64                `json:"amount"`
	TopupPhone    string                 `json:"topup_phone"`
	Currency      string                 `json:"currency"`
	Status        string                 `json:"status"`
	Reference     string                 `json:"reference"`
	Metadata      map[string]interface{} `json:"metadata"`
	Timestamp     time.Time              `json:"timestamp"`
}

type SMSProviderWebhookPayload struct {
	EventType   string                 `json:"event_type"`
	MessageID   string                 `json:"message_id"`
	PhoneNumber string                 `json:"phone_number"`
	Status      string                 `json:"status"`
	DeliveredAt *time.Time             `json:"delivered_at,omitempty"`
	FailedAt    *time.Time             `json:"failed_at,omitempty"`
	ErrorCode   string                 `json:"error_code,omitempty"`
	ErrorMsg    string                 `json:"error_message,omitempty"`
	Metadata    map[string]interface{} `json:"metadata"`
	Timestamp   time.Time              `json:"timestamp"`
}

type EmailProviderWebhookPayload struct {
	EventType   string                 `json:"event_type"`
	MessageID   string                 `json:"message_id"`
	Email       string                 `json:"email"`
	Status      string                 `json:"status"`
	DeliveredAt *time.Time             `json:"delivered_at,omitempty"`
	OpenedAt    *time.Time             `json:"opened_at,omitempty"`
	ClickedAt   *time.Time             `json:"clicked_at,omitempty"`
	BouncedAt   *time.Time             `json:"bounced_at,omitempty"`
	ErrorCode   string                 `json:"error_code,omitempty"`
	ErrorMsg    string                 `json:"error_message,omitempty"`
	Metadata    map[string]interface{} `json:"metadata"`
	Timestamp   time.Time              `json:"timestamp"`
}

type FraudDetectionWebhookPayload struct {
	EventType     string                 `json:"event_type"`
	AlertID       string                 `json:"alert_id"`
	TransactionID string                 `json:"transaction_id"`
	WalletID      uint                   `json:"wallet_id"`
	CardID        uint                   `json:"card_id,omitempty"`
	RiskScore     float64                `json:"risk_score"`
	RiskLevel     string                 `json:"risk_level"`
	Reasons       []string               `json:"reasons"`
	Action        string                 `json:"action"`
	Metadata      map[string]interface{} `json:"metadata"`
	Timestamp     time.Time              `json:"timestamp"`
}

// Webhook Processing Methods

func (ewc *ExternalWebhookController) processPaymentEngineWebhook(payload *PaymentEngineWebhookPayload) error {
	ewc.container.Logger.LogTransaction(
		payload.Reference,
		payload.TransactionID,
		"payment_engine_webhook",
		payload.Amount,
		"Received payment engine webhook: "+payload.EventType,
	)

	switch payload.EventType {
	case "payment.completed":
		return ewc.handlePaymentCompleted(payload)
	case "payment.failed":
		return ewc.handlePaymentFailed(payload)
	case "payment.pending":
		return ewc.handlePaymentPending(payload)
	case "refund.completed":
		return ewc.handleRefundCompleted(payload)
	case "chargeback.created":
		return ewc.handleChargebackCreated(payload)
	default:
		ewc.container.Logger.LogSystem("unknown_payment_webhook", "payment_engine", "",
			"Unknown payment webhook event: "+payload.EventType)
		return nil
	}
}

func (ewc *ExternalWebhookController) processSMSProviderWebhook(payload *SMSProviderWebhookPayload) error {
	ewc.container.Logger.LogSystem("sms_webhook_received", "sms_provider", "",
		"Received SMS webhook: "+payload.EventType)

	switch payload.EventType {
	case "message.delivered":
		return ewc.handleSMSDelivered(payload)
	case "message.failed":
		return ewc.handleSMSFailed(payload)
	case "message.bounced":
		return ewc.handleSMSBounced(payload)
	default:
		ewc.container.Logger.LogSystem("unknown_sms_webhook", "sms_provider", "",
			"Unknown SMS webhook event: "+payload.EventType)
		return nil
	}
}

func (ewc *ExternalWebhookController) processEmailProviderWebhook(payload *EmailProviderWebhookPayload) error {
	ewc.container.Logger.LogSystem("email_webhook_received", "email_provider", "",
		"Received email webhook: "+payload.EventType)

	switch payload.EventType {
	case "email.delivered":
		return ewc.handleEmailDelivered(payload)
	case "email.opened":
		return ewc.handleEmailOpened(payload)
	case "email.clicked":
		return ewc.handleEmailClicked(payload)
	case "email.bounced":
		return ewc.handleEmailBounced(payload)
	case "email.failed":
		return ewc.handleEmailFailed(payload)
	default:
		ewc.container.Logger.LogSystem("unknown_email_webhook", "email_provider", "",
			"Unknown email webhook event: "+payload.EventType)
		return nil
	}
}

func (ewc *ExternalWebhookController) processFraudDetectionWebhook(payload *FraudDetectionWebhookPayload) error {
	ewc.container.Logger.LogSystem("fraud_webhook_received", "fraud_detection", "",
		"Received fraud detection webhook: "+payload.EventType)

	switch payload.EventType {
	case "fraud.detected":
		return ewc.handleFraudDetected(payload)
	case "fraud.cleared":
		return ewc.handleFraudCleared(payload)
	case "risk.assessment":
		return ewc.handleRiskAssessment(payload)
	default:
		ewc.container.Logger.LogSystem("unknown_fraud_webhook", "fraud_detection", "",
			"Unknown fraud webhook event: "+payload.EventType)
		return nil
	}
}

// Payment Engine Event Handlers

func (ewc *ExternalWebhookController) handlePaymentCompleted(payload *PaymentEngineWebhookPayload) error {
	// Update transaction status and wallet balance
	if payload.WalletID > 0 {
		// Top up wallet with the payment amount
		_, err := ewc.container.WalletService.TopupWallet(
			payload.WalletID,
			payload.Amount,
			payload.TopupPhone,
			"external_payment",
			payload.Reference,
		)
		if err != nil {
			ewc.container.Logger.LogError(err, map[string]interface{}{
				"action":         "payment_completed_topup",
				"wallet_id":      payload.WalletID,
				"amount":         payload.Amount,
				"transaction_id": payload.TransactionID,
			})
			return err
		}
	}

	ewc.container.Logger.LogTransaction(
		payload.Reference,
		payload.TransactionID,
		"payment_completed",
		payload.Amount,
		"Payment completed successfully",
	)
	return nil
}

func (ewc *ExternalWebhookController) handlePaymentFailed(payload *PaymentEngineWebhookPayload) error {
	// TODO: Update transaction status, send failure notifications
	ewc.container.Logger.LogTransaction(
		payload.Reference,
		payload.TransactionID,
		"payment_failed",
		payload.Amount,
		"Payment failed",
	)
	return nil
}

func (ewc *ExternalWebhookController) handlePaymentPending(payload *PaymentEngineWebhookPayload) error {
	// TODO: Update transaction status to pending
	ewc.container.Logger.LogTransaction(
		payload.Reference,
		payload.TransactionID,
		"payment_pending",
		payload.Amount,
		"Payment is pending",
	)
	return nil
}

func (ewc *ExternalWebhookController) handleRefundCompleted(payload *PaymentEngineWebhookPayload) error {
	// TODO: Process refund, update wallet balance
	ewc.container.Logger.LogTransaction(
		payload.Reference,
		payload.TransactionID,
		"refund_completed",
		payload.Amount,
		"Refund completed successfully",
	)
	return nil
}

func (ewc *ExternalWebhookController) handleChargebackCreated(payload *PaymentEngineWebhookPayload) error {
	// Handle chargeback - freeze wallet and notify admin
	if payload.WalletID > 0 {
		// Freeze the wallet to prevent further transactions
		err := ewc.container.WalletService.FreezeWallet(payload.WalletID, "Chargeback detected - automatic freeze")
		if err != nil {
			ewc.container.Logger.LogError(err, map[string]interface{}{
				"action":         "chargeback_freeze_wallet",
				"wallet_id":      payload.WalletID,
				"transaction_id": payload.TransactionID,
			})
		}

		// Create fraud alert using CheckFraud
		_, err = ewc.container.SecurityService.CheckFraud(payload.WalletID, map[string]interface{}{
			"transaction_id": payload.TransactionID,
			"amount":         payload.Amount,
			"reference":      payload.Reference,
			"event_type":     "chargeback",
			"ip":             "external",
			"user_agent":     "payment_engine",
		})
		if err != nil {
			ewc.container.Logger.LogError(err, map[string]interface{}{
				"action":         "chargeback_create_fraud_alert",
				"wallet_id":      payload.WalletID,
				"transaction_id": payload.TransactionID,
			})
		}
	}

	ewc.container.Logger.LogTransaction(
		payload.Reference,
		payload.TransactionID,
		"chargeback_created",
		payload.Amount,
		"Chargeback created",
	)
	return nil
}

// SMS Provider Event Handlers

func (ewc *ExternalWebhookController) handleSMSDelivered(payload *SMSProviderWebhookPayload) error {
	// Update SMS delivery status and track analytics

	// Log successful delivery for analytics
	ewc.container.Logger.LogSystem("sms_delivered", "sms_provider", "",
		"SMS delivered to "+payload.PhoneNumber)

	// Track delivery metrics for analytics
	ewc.container.Logger.LogService(payload.MessageID, "sms_delivered", payload.PhoneNumber, true,
		"SMS successfully delivered to "+payload.PhoneNumber)

	return nil
}

func (ewc *ExternalWebhookController) handleSMSFailed(payload *SMSProviderWebhookPayload) error {
	// TODO: Update SMS delivery status, retry if needed
	ewc.container.Logger.LogSystem("sms_failed", "sms_provider", "",
		"SMS failed to "+payload.PhoneNumber+": "+payload.ErrorMsg)
	return nil
}

func (ewc *ExternalWebhookController) handleSMSBounced(payload *SMSProviderWebhookPayload) error {
	// TODO: Mark phone number as invalid, update user profile
	ewc.container.Logger.LogSystem("sms_bounced", "sms_provider", "",
		"SMS bounced for "+payload.PhoneNumber)
	return nil
}

// Email Provider Event Handlers

func (ewc *ExternalWebhookController) handleEmailDelivered(payload *EmailProviderWebhookPayload) error {
	// TODO: Update email delivery status, track analytics
	ewc.container.Logger.LogSystem("email_delivered", "email_provider", "",
		"Email delivered to "+payload.Email)
	return nil
}

func (ewc *ExternalWebhookController) handleEmailOpened(payload *EmailProviderWebhookPayload) error {
	// TODO: Track email open analytics
	ewc.container.Logger.LogSystem("email_opened", "email_provider", "",
		"Email opened by "+payload.Email)
	return nil
}

func (ewc *ExternalWebhookController) handleEmailClicked(payload *EmailProviderWebhookPayload) error {
	// TODO: Track email click analytics
	ewc.container.Logger.LogSystem("email_clicked", "email_provider", "",
		"Email clicked by "+payload.Email)
	return nil
}

func (ewc *ExternalWebhookController) handleEmailBounced(payload *EmailProviderWebhookPayload) error {
	// TODO: Mark email as invalid, update user profile
	ewc.container.Logger.LogSystem("email_bounced", "email_provider", "",
		"Email bounced for "+payload.Email)
	return nil
}

func (ewc *ExternalWebhookController) handleEmailFailed(payload *EmailProviderWebhookPayload) error {
	// TODO: Update email delivery status, retry if needed
	ewc.container.Logger.LogSystem("email_failed", "email_provider", "",
		"Email failed to "+payload.Email+": "+payload.ErrorMsg)
	return nil
}

// Fraud Detection Event Handlers

func (ewc *ExternalWebhookController) handleFraudDetected(payload *FraudDetectionWebhookPayload) error {
	// TODO: Block card/wallet, send alerts, notify admin
	ewc.container.Logger.LogSystem("fraud_detected", "fraud_detection", "",
		"Fraud detected for wallet "+string(rune(payload.WalletID))+", risk score: "+string(rune(int(payload.RiskScore))))

	// If high risk, take immediate action
	if payload.RiskLevel == "high" || payload.RiskScore >= 0.8 {
		// Block all cards for this wallet as a protective measure
		cards, err := ewc.container.PayCardService.GetCardsByWallet(payload.WalletID)
		if err != nil {
			ewc.container.Logger.LogError(err, map[string]interface{}{
				"action":    "fraud_auto_block_cards",
				"wallet_id": payload.WalletID,
			})
		} else {
			for _, card := range cards {
				if card.Status == "active" {
					err := ewc.container.PayCardService.BlockCard(card.ID, "Automatic fraud protection")
					if err != nil {
						ewc.container.Logger.LogError(err, map[string]interface{}{
							"action":    "fraud_auto_block_card",
							"card_id":   card.ID,
							"wallet_id": payload.WalletID,
						})
					} else {
						// Get IP address from metadata if available
						ipAddress := "unknown"
						if ip, ok := payload.Metadata["ip_address"].(string); ok {
							ipAddress = ip
						}

						ewc.container.Logger.LogSecurity("card_auto_blocked", fmt.Sprintf("%d", payload.WalletID),
							ipAddress, fmt.Sprintf("Card %d automatically blocked due to fraud detection", card.ID))
					}
				}
			}
		}

		// Create fraud alert in the system using available metadata
		alertData := map[string]interface{}{
			"transaction_id": payload.TransactionID,
			"alert_id":       payload.AlertID,
			"risk_score":     payload.RiskScore,
			"risk_level":     payload.RiskLevel,
			"reasons":        payload.Reasons,
		}

		// Add metadata fields if available
		for key, value := range payload.Metadata {
			alertData[key] = value
		}

		_, err = ewc.container.SecurityService.CheckFraud(payload.WalletID, alertData)
		if err != nil {
			ewc.container.Logger.LogError(err, map[string]interface{}{
				"action":    "create_fraud_alert",
				"wallet_id": payload.WalletID,
			})
		}

		ewc.container.Logger.LogSystem("high_risk_fraud", "fraud_detection", "",
			"High risk fraud detected, protective actions taken")
	}

	return nil
}

func (ewc *ExternalWebhookController) handleFraudCleared(payload *FraudDetectionWebhookPayload) error {
	// Unblock cards that were automatically blocked due to fraud
	if payload.CardID > 0 {
		// Unblock specific card
		err := ewc.container.PayCardService.UnblockCard(payload.CardID)
		if err != nil {
			ewc.container.Logger.LogError(err, map[string]interface{}{
				"action":    "fraud_unblock_card",
				"card_id":   payload.CardID,
				"wallet_id": payload.WalletID,
			})
		} else {
			ewc.container.Logger.LogSecurity("card_auto_unblocked", fmt.Sprintf("%d", payload.WalletID),
				"system", fmt.Sprintf("Card %d automatically unblocked - fraud cleared", payload.CardID))
		}
	} else {
		// If no specific card ID, check all cards for this wallet
		// Note: We can't easily check block reason from PayCardResponse, so we'll unblock all blocked cards
		// In a production system, you might want to add a service method to check block metadata
		cards, err := ewc.container.PayCardService.GetCardsByWallet(payload.WalletID)
		if err != nil {
			ewc.container.Logger.LogError(err, map[string]interface{}{
				"action":    "fraud_get_cards_for_unblock",
				"wallet_id": payload.WalletID,
			})
		} else {
			for _, card := range cards {
				if card.Status == "blocked" {
					err := ewc.container.PayCardService.UnblockCard(card.ID)
					if err != nil {
						ewc.container.Logger.LogError(err, map[string]interface{}{
							"action":    "fraud_unblock_card",
							"card_id":   card.ID,
							"wallet_id": payload.WalletID,
						})
					} else {
						ewc.container.Logger.LogSecurity("card_auto_unblocked", fmt.Sprintf("%d", payload.WalletID),
							"system", fmt.Sprintf("Card %d automatically unblocked - fraud cleared", card.ID))
					}
				}
			}
		}
	}

	ewc.container.Logger.LogSystem("fraud_cleared", "fraud_detection", "",
		"Fraud alert cleared for wallet "+fmt.Sprintf("%d", payload.WalletID))
	return nil
}

func (ewc *ExternalWebhookController) handleRiskAssessment(payload *FraudDetectionWebhookPayload) error {
	// Update wallet risk profile based on assessment
	riskData := map[string]interface{}{
		"risk_score":    payload.RiskScore,
		"risk_level":    payload.RiskLevel,
		"assessment_id": payload.AlertID,
		"reasons":       payload.Reasons,
		"assessed_at":   payload.Timestamp,
	}

	// Add metadata from the assessment
	for key, value := range payload.Metadata {
		riskData[key] = value
	}

	// Log the risk assessment for audit purposes
	err := ewc.container.SecurityService.LogSecurityEvent(
		payload.WalletID,
		"risk_assessment",
		fmt.Sprintf("Risk assessment completed - Level: %s, Score: %.2f", payload.RiskLevel, payload.RiskScore),
		riskData,
	)
	if err != nil {
		ewc.container.Logger.LogError(err, map[string]interface{}{
			"action":    "log_risk_assessment",
			"wallet_id": payload.WalletID,
		})
	}

	// Adjust spending limits based on risk level
	if payload.RiskLevel == "high" && payload.RiskScore >= 0.8 {
		// Reduce spending limits for high-risk wallets
		cards, err := ewc.container.PayCardService.GetCardsByWallet(payload.WalletID)
		if err != nil {
			ewc.container.Logger.LogError(err, map[string]interface{}{
				"action":    "get_cards_for_risk_adjustment",
				"wallet_id": payload.WalletID,
			})
		} else {
			for _, card := range cards {
				// Reduce daily limit by 50% for high-risk assessments
				newDailyLimit := card.DailySpendingLimit * 0.5
				updates := map[string]interface{}{
					"daily_spending_limit": newDailyLimit,
				}

				_, err := ewc.container.PayCardService.UpdateCard(card.ID, updates)
				if err != nil {
					ewc.container.Logger.LogError(err, map[string]interface{}{
						"action":    "adjust_card_limits_risk",
						"card_id":   card.ID,
						"wallet_id": payload.WalletID,
					})
				} else {
					ewc.container.Logger.LogSecurity("card_limits_adjusted", fmt.Sprintf("%d", payload.WalletID),
						"system", fmt.Sprintf("Card %d limits reduced due to high risk assessment", card.ID))
				}
			}
		}
	}

	ewc.container.Logger.LogSystem("risk_assessment", "fraud_detection", "",
		"Risk assessment completed for wallet "+fmt.Sprintf("%d", payload.WalletID))
	return nil
}

// Signature Verification Methods

func (ewc *ExternalWebhookController) verifyPaymentEngineSignature(body []byte, signature string) bool {
	secret := ewc.container.Config.External.Webhook.Secret
	if secret == "" {
		ewc.container.Logger.LogError(fmt.Errorf("webhook secret not configured"), map[string]interface{}{
			"action": "verify_payment_engine_signature",
		})
		return false
	}

	return ewc.verifyHMACSignature(body, signature, secret)
}

func (ewc *ExternalWebhookController) verifyFraudDetectionSignature(body []byte, signature string) bool {
	secret := ewc.container.Config.External.Webhook.Secret
	if secret == "" {
		ewc.container.Logger.LogError(fmt.Errorf("webhook secret not configured"), map[string]interface{}{
			"action": "verify_fraud_detection_signature",
		})
		return false
	}

	return ewc.verifyHMACSignature(body, signature, secret)
}

// Generic signature verification helper
func (ewc *ExternalWebhookController) verifyHMACSignature(body []byte, signature, secret string) bool {
	if signature == "" || secret == "" {
		return false
	}

	// Remove "sha256=" prefix if present
	if strings.HasPrefix(signature, "sha256=") {
		signature = signature[7:]
	}

	// Calculate expected signature
	mac := hmac.New(sha256.New, []byte(secret))
	mac.Write(body)
	expectedSignature := hex.EncodeToString(mac.Sum(nil))

	// Compare signatures
	return hmac.Equal([]byte(signature), []byte(expectedSignature))
}
