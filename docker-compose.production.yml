version: '3.8'

services:
  wallet-platform:
    build:
      context: .
      dockerfile: Dockerfile.production
    ports:
      - "8086:8086"
    environment:
      - APP_ENVIRONMENT=production
    env_file:
      - .env.production
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./logs:/app/logs:rw
    restart: unless-stopped
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:8086/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - wallet-network

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_DATABASE: wallet_platform
      MYSQL_USER: ${DATABASE_USERNAME}
      MYSQL_PASSWORD: ${DATABASE_PASSWORD}
      MYSQL_ROOT_PASSWORD: ${DATABASE_ROOT_PASSWORD}
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/mysql/production.cnf:/etc/mysql/conf.d/production.cnf:ro
      - ./scripts/mysql/init-production.sql:/docker-entrypoint-initdb.d/init.sql:ro
    command: --default-authentication-plugin=mysql_native_password --ssl-mode=REQUIRED
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${DATABASE_ROOT_PASSWORD}"]
      timeout: 20s
      retries: 10
      start_period: 30s
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G
    networks:
      - wallet-network

  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./scripts/redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server /usr/local/etc/redis/redis.conf --requirepass ${REDIS_PASSWORD}
    healthcheck:
      test: ["CMD", "redis-cli", "--no-auth-warning", "-a", "${REDIS_PASSWORD}", "ping"]
      timeout: 3s
      retries: 5
      start_period: 10s
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M
    networks:
      - wallet-network

  # Nginx reverse proxy for production
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./scripts/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./scripts/nginx/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - wallet-platform
    restart: unless-stopped
    networks:
      - wallet-network

  # Log aggregation (optional)
  fluentd:
    image: fluent/fluentd:v1.14-debian-1
    volumes:
      - ./logs:/fluentd/log
      - ./scripts/fluentd/fluent.conf:/fluentd/etc/fluent.conf:ro
    depends_on:
      - wallet-platform
    restart: unless-stopped
    networks:
      - wallet-network

volumes:
  mysql_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /var/lib/wallet-platform/mysql
  redis_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /var/lib/wallet-platform/redis

networks:
  wallet-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
