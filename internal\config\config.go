package config

import (
	"fmt"
	"os"
	"strings"

	"github.com/joho/godotenv"
	"github.com/spf13/viper"
)

// Config represents the application configuration
type Config struct {
	App         AppConfig         `mapstructure:"app"`
	Server      ServerConfig      `mapstructure:"server"`
	Database    DatabaseConfig    `mapstructure:"database"`
	Redis       RedisConfig       `mapstructure:"redis"`
	JWT         JWTConfig         `mapstructure:"jwt"`
	Log         LogConfig         `mapstructure:"log"`
	RateLimit   RateLimitConfig   `mapstructure:"rate_limit"`
	Security    SecurityConfig    `mapstructure:"security"`
	InternalAPI InternalAPIConfig `mapstructure:"internal_api"`
	External    ExternalConfig    `mapstructure:"external"`
	Fees        FeesConfig        `mapstructure:"fees"`
	CardLimits  CardLimitsConfig  `mapstructure:"card_limits"`
}

// AppConfig represents application-specific configuration
type AppConfig struct {
	Name        string `mapstructure:"name"`
	Version     string `mapstructure:"version"`
	Environment string `mapstructure:"environment"`
	Debug       bool   `mapstructure:"debug"`
}

// ServerConfig represents HTTP server configuration
type ServerConfig struct {
	Port         int        `mapstructure:"port"`
	Host         string     `mapstructure:"host"`
	Environment  string     `mapstructure:"environment"`
	ReadTimeout  int        `mapstructure:"read_timeout"`
	WriteTimeout int        `mapstructure:"write_timeout"`
	IdleTimeout  int        `mapstructure:"idle_timeout"`
	CORS         CORSConfig `mapstructure:"cors"`
}

// CORSConfig represents CORS configuration
type CORSConfig struct {
	AllowOrigins     []string `mapstructure:"allow_origins"`
	AllowMethods     []string `mapstructure:"allow_methods"`
	AllowHeaders     []string `mapstructure:"allow_headers"`
	ExposeHeaders    []string `mapstructure:"expose_headers"`
	AllowCredentials bool     `mapstructure:"allow_credentials"`
	MaxAge           int      `mapstructure:"max_age"`
}

// DatabaseConfig represents database configuration
type DatabaseConfig struct {
	Enabled         bool   `mapstructure:"enabled"`
	Driver          string `mapstructure:"driver"`
	Host            string `mapstructure:"host"`
	Port            int    `mapstructure:"port"`
	Username        string `mapstructure:"username"`
	Password        string `mapstructure:"password"`
	Database        string `mapstructure:"database"`
	SSLMode         string `mapstructure:"ssl_mode"` // For MySQL: "true", "false", "skip-verify", "preferred"
	MaxOpenConns    int    `mapstructure:"max_open_conns"`
	MaxIdleConns    int    `mapstructure:"max_idle_conns"`
	ConnMaxLifetime int    `mapstructure:"conn_max_lifetime"`
	ConnMaxIdleTime int    `mapstructure:"conn_max_idle_time"`
}

// RedisConfig represents Redis configuration
type RedisConfig struct {
	Enabled      bool   `mapstructure:"enabled"`
	Host         string `mapstructure:"host"`
	Port         int    `mapstructure:"port"`
	Password     string `mapstructure:"password"`
	Database     int    `mapstructure:"database"`
	PoolSize     int    `mapstructure:"pool_size"`
	MinIdleConns int    `mapstructure:"min_idle_conns"`
	MaxRetries   int    `mapstructure:"max_retries"`
}

// JWTConfig represents JWT configuration
type JWTConfig struct {
	SecretKey      string `mapstructure:"secret_key"`
	ExpirationTime int    `mapstructure:"expiration_time"`
	RefreshTime    int    `mapstructure:"refresh_time"`
	Issuer         string `mapstructure:"issuer"`
	Audience       string `mapstructure:"audience"`
}

// LogConfig represents logging configuration
type LogConfig struct {
	Level  string `mapstructure:"level"`
	Format string `mapstructure:"format"`
	Output string `mapstructure:"output"`
}

// RateLimitConfig represents rate limiting configuration
type RateLimitConfig struct {
	Enabled        bool                     `mapstructure:"enabled"`
	DefaultLimit   int                      `mapstructure:"default_limit"`
	DefaultWindow  int                      `mapstructure:"default_window"`
	EndpointLimits map[string]EndpointLimit `mapstructure:"endpoint_limits"`
}

// EndpointLimit represents rate limit for specific endpoints
type EndpointLimit struct {
	Limit  int `mapstructure:"limit"`
	Window int `mapstructure:"window"`
}

// SecurityConfig represents security configuration
type SecurityConfig struct {
	EncryptionKey      string `mapstructure:"encryption_key"`
	HashSalt           string `mapstructure:"hash_salt"`
	MaxLoginAttempts   int    `mapstructure:"max_login_attempts"`
	LockoutDuration    int    `mapstructure:"lockout_duration"`
	SessionTimeout     int    `mapstructure:"session_timeout"`
	RequireHTTPS       bool   `mapstructure:"require_https"`
	CSRFProtection     bool   `mapstructure:"csrf_protection"`
	ContentTypeNoSniff bool   `mapstructure:"content_type_no_sniff"`
}

// InternalAPIConfig represents internal API configuration
type InternalAPIConfig struct {
	Enabled         bool                 `mapstructure:"enabled"`
	Key             string               `mapstructure:"key"`
	AllowedServices []string             `mapstructure:"allowed_services"`
	RateLimit       InternalAPIRateLimit `mapstructure:"rate_limit"`
}

// InternalAPIRateLimit represents rate limiting for internal API
type InternalAPIRateLimit struct {
	Enabled bool `mapstructure:"enabled"`
	Limit   int  `mapstructure:"limit"`
	Window  int  `mapstructure:"window"`
}

// ExternalConfig represents external service configuration
type ExternalConfig struct {
	PaymentEngine PaymentEngineConfig `mapstructure:"payment_engine"`
	SMS           SMSConfig           `mapstructure:"sms"`
	Email         EmailConfig         `mapstructure:"email"`
	Webhook       WebhookConfig       `mapstructure:"webhook"`
}

// PaymentEngineConfig represents payment engine API configuration
type PaymentEngineConfig struct {
	BaseURL    string `mapstructure:"base_url"`
	APIKey     string `mapstructure:"api_key"`
	Timeout    int    `mapstructure:"timeout"`
	RetryCount int    `mapstructure:"retry_count"`
}

// SMSConfig represents SMS service configuration
type SMSConfig struct {
	Provider string `mapstructure:"provider"`
	APIKey   string `mapstructure:"api_key"`
	APIURL   string `mapstructure:"api_url"`
	From     string `mapstructure:"from"`
}

// EmailConfig represents email service configuration
type EmailConfig struct {
	Provider string `mapstructure:"provider"`
	APIKey   string `mapstructure:"api_key"`
	APIURL   string `mapstructure:"api_url"`
	From     string `mapstructure:"from"`
	FromName string `mapstructure:"from_name"`
}

// WebhookConfig represents webhook configuration
type WebhookConfig struct {
	Secret     string `mapstructure:"secret"`
	Timeout    int    `mapstructure:"timeout"`
	MaxRetries int    `mapstructure:"max_retries"`
}

// FeesConfig represents platform fees configuration
type FeesConfig struct {
	CardCreation struct {
		Standard float64 `mapstructure:"standard"`
		Premium  float64 `mapstructure:"premium"`
		Business float64 `mapstructure:"business"`
	} `mapstructure:"card_creation"`
	WithdrawalPercentage               float64 `mapstructure:"withdrawal_percentage"`
	TransactionPercentage              float64 `mapstructure:"transaction_percentage"`
	MonthlyMaintenance                 float64 `mapstructure:"monthly_maintenance"`
	CardReplacement                    float64 `mapstructure:"card_replacement"`
	InternationalTransactionPercentage float64 `mapstructure:"international_transaction_percentage"`
	ATMWithdrawal                      float64 `mapstructure:"atm_withdrawal"`
}

// CardLimitsConfig represents card limits configuration
type CardLimitsConfig struct {
	Standard struct {
		SpendingLimit        float64 `mapstructure:"spending_limit"`
		DailySpendingLimit   float64 `mapstructure:"daily_spending_limit"`
		MonthlySpendingLimit float64 `mapstructure:"monthly_spending_limit"`
		MaxCardsPerWallet    int     `mapstructure:"max_cards_per_wallet"`
	} `mapstructure:"standard"`
	Premium struct {
		SpendingLimit        float64 `mapstructure:"spending_limit"`
		DailySpendingLimit   float64 `mapstructure:"daily_spending_limit"`
		MonthlySpendingLimit float64 `mapstructure:"monthly_spending_limit"`
		MaxCardsPerWallet    int     `mapstructure:"max_cards_per_wallet"`
	} `mapstructure:"premium"`
	Business struct {
		SpendingLimit        float64 `mapstructure:"spending_limit"`
		DailySpendingLimit   float64 `mapstructure:"daily_spending_limit"`
		MonthlySpendingLimit float64 `mapstructure:"monthly_spending_limit"`
		MaxCardsPerWallet    int     `mapstructure:"max_cards_per_wallet"`
	} `mapstructure:"business"`
	Creation struct {
		MaxCardsPerDay int `mapstructure:"max_cards_per_day"`
		CooldownHours  int `mapstructure:"cooldown_hours"`
	} `mapstructure:"creation"`
	Transaction struct {
		MinAmount         float64 `mapstructure:"min_amount"`
		MaxAmountStandard float64 `mapstructure:"max_amount_standard"`
		MaxAmountPremium  float64 `mapstructure:"max_amount_premium"`
		MaxAmountBusiness float64 `mapstructure:"max_amount_business"`
	} `mapstructure:"transaction"`
	ATM struct {
		DailyLimitStandard   float64 `mapstructure:"daily_limit_standard"`
		DailyLimitPremium    float64 `mapstructure:"daily_limit_premium"`
		DailyLimitBusiness   float64 `mapstructure:"daily_limit_business"`
		MonthlyLimitStandard float64 `mapstructure:"monthly_limit_standard"`
		MonthlyLimitPremium  float64 `mapstructure:"monthly_limit_premium"`
		MonthlyLimitBusiness float64 `mapstructure:"monthly_limit_business"`
	} `mapstructure:"atm"`
}

// Load loads configuration from .env file and environment variables
func Load() (*Config, error) {
	// Load .env file first (primary source)
	// Try to load .env file, but don't fail if it doesn't exist
	if err := godotenv.Load(".env"); err != nil {
		// If .env doesn't exist, try environment-specific .env files
		environment := os.Getenv("APP_ENVIRONMENT")
		if environment == "" {
			environment = "development"
		}

		envFile := ".env." + environment
		if err := godotenv.Load(envFile); err != nil {
			// If no .env files exist, continue with system environment variables only
			fmt.Printf("No .env file found, using system environment variables only\n")
		}
	}

	// Enable environment variable support with Viper
	viper.AutomaticEnv()
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	// Set default values
	setDefaults()

	// Manually map critical environment variables to Viper
	// This ensures they override defaults properly
	envMappings := map[string]string{
		"JWT_SECRET_KEY":                   "jwt.secret_key",
		"SECURITY_ENCRYPTION_KEY":          "security.encryption_key",
		"SECURITY_HASH_SALT":               "security.hash_salt",
		"INTERNAL_API_KEY":                 "internal_api.key",
		"DATABASE_USERNAME":                "database.username",
		"DATABASE_PASSWORD":                "database.password",
		"EXTERNAL_PAYMENT_ENGINE_BASE_URL": "external.payment_engine.base_url",
		"EXTERNAL_PAYMENT_ENGINE_API_KEY":  "external.payment_engine.api_key",
		"EXTERNAL_SMS_API_KEY":             "external.sms.api_key",
		"EXTERNAL_EMAIL_API_KEY":           "external.email.api_key",
		"EXTERNAL_WEBHOOK_SECRET":          "external.webhook.secret",
	}

	for envVar, viperKey := range envMappings {
		if value := os.Getenv(envVar); value != "" {
			viper.Set(viperKey, value)
		}
	}

	// Unmarshal configuration
	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// Validate required security configurations
	if err := validateSecurityConfig(&config); err != nil {
		return nil, fmt.Errorf("security configuration validation failed: %w", err)
	}

	return &config, nil
}

// validateSecurityConfig validates that required security configurations are set
func validateSecurityConfig(config *Config) error {
	// Check JWT secret key
	if config.JWT.SecretKey == "" {
		return fmt.Errorf("JWT_SECRET_KEY environment variable is required")
	}

	// Check encryption key
	if config.Security.EncryptionKey == "" {
		return fmt.Errorf("SECURITY_ENCRYPTION_KEY environment variable is required")
	}

	// Check hash salt
	if config.Security.HashSalt == "" {
		return fmt.Errorf("SECURITY_HASH_SALT environment variable is required")
	}

	// Check internal API key
	if config.InternalAPI.Key == "" {
		return fmt.Errorf("INTERNAL_API_KEY environment variable is required")
	}

	// Check database credentials
	if config.Database.Username == "" {
		return fmt.Errorf("DATABASE_USERNAME environment variable is required")
	}

	if config.Database.Password == "" {
		return fmt.Errorf("DATABASE_PASSWORD environment variable is required")
	}

	// Validate key lengths for security
	if len(config.Security.EncryptionKey) < 32 {
		return fmt.Errorf("SECURITY_ENCRYPTION_KEY must be at least 32 characters")
	}

	if len(config.Security.HashSalt) < 16 {
		return fmt.Errorf("SECURITY_HASH_SALT must be at least 16 characters")
	}

	// In production, require HTTPS
	if config.App.Environment == "production" && !config.Security.RequireHTTPS {
		return fmt.Errorf("HTTPS is required in production environment")
	}

	return nil
}

// setDefaults sets default configuration values
func setDefaults() {
	// App defaults
	viper.SetDefault("app.name", "Wallet Platform")
	viper.SetDefault("app.version", "1.0.0")
	viper.SetDefault("app.environment", "development")
	viper.SetDefault("app.debug", true)

	// Server defaults
	viper.SetDefault("server.port", 8086)
	viper.SetDefault("server.host", "0.0.0.0")
	viper.SetDefault("server.environment", "development")
	viper.SetDefault("server.read_timeout", 30)
	viper.SetDefault("server.write_timeout", 30)
	viper.SetDefault("server.idle_timeout", 120)

	// CORS defaults (configurable via environment variables)
	viper.SetDefault("server.cors.allow_origins", []string{"http://localhost:3000"})
	viper.SetDefault("server.cors.allow_methods", []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"})
	viper.SetDefault("server.cors.allow_headers", []string{"*"})
	viper.SetDefault("server.cors.allow_credentials", true)
	viper.SetDefault("server.cors.max_age", 86400)

	// Database defaults (MySQL) - DO NOT SET CREDENTIALS AS DEFAULTS
	viper.SetDefault("database.enabled", true)
	viper.SetDefault("database.driver", "mysql")
	viper.SetDefault("database.host", "localhost")
	viper.SetDefault("database.port", 3306)
	viper.SetDefault("database.database", "wallet_platform")
	viper.SetDefault("database.ssl_mode", "true") // Default to secure
	viper.SetDefault("database.max_open_conns", 25)
	viper.SetDefault("database.max_idle_conns", 5)
	viper.SetDefault("database.conn_max_lifetime", 300)
	viper.SetDefault("database.conn_max_idle_time", 60)

	// Redis defaults
	viper.SetDefault("redis.enabled", false)
	viper.SetDefault("redis.host", "localhost")
	viper.SetDefault("redis.port", 6379)
	viper.SetDefault("redis.password", "")
	viper.SetDefault("redis.database", 0)
	viper.SetDefault("redis.pool_size", 10)
	viper.SetDefault("redis.min_idle_conns", 2)
	viper.SetDefault("redis.max_retries", 3)

	// JWT defaults - DO NOT SET DEFAULT FOR SECRET_KEY IN PRODUCTION
	viper.SetDefault("jwt.expiration_time", 3600)
	viper.SetDefault("jwt.refresh_time", 86400)
	viper.SetDefault("jwt.issuer", "wallet-platform")
	viper.SetDefault("jwt.audience", "wallet-platform-users")

	// Log defaults
	viper.SetDefault("log.level", "info")
	viper.SetDefault("log.format", "json")
	viper.SetDefault("log.output", "stdout")

	// Rate limit defaults
	viper.SetDefault("rate_limit.enabled", true)
	viper.SetDefault("rate_limit.default_limit", 100)
	viper.SetDefault("rate_limit.default_window", 60)

	// Security defaults
	viper.SetDefault("security.max_login_attempts", 5)
	viper.SetDefault("security.lockout_duration", 900)
	viper.SetDefault("security.session_timeout", 3600)
	viper.SetDefault("security.require_https", false)
	viper.SetDefault("security.csrf_protection", true)
	viper.SetDefault("security.content_type_no_sniff", true)

	// Internal API defaults
	viper.SetDefault("internal_api.enabled", true)
	viper.SetDefault("internal_api.rate_limit.enabled", true)
	viper.SetDefault("internal_api.rate_limit.limit", 1000)
	viper.SetDefault("internal_api.rate_limit.window", 60)

	// External service defaults
	viper.SetDefault("external.payment_engine.timeout", 30)
	viper.SetDefault("external.payment_engine.retry_count", 3)
	viper.SetDefault("external.webhook.timeout", 30)
	viper.SetDefault("external.webhook.max_retries", 3)

	// SMS service defaults
	viper.SetDefault("external.sms.provider", "centurion")
	viper.SetDefault("external.sms.api_url", "https://auth.centurionbd.com/api/v1/sms/send")

	// Email service defaults
	viper.SetDefault("external.email.provider", "centurion")
	viper.SetDefault("external.email.api_url", "https://auth.centurionbd.com/api/v1/email/send")
	viper.SetDefault("external.email.from", "<EMAIL>")
	viper.SetDefault("external.email.from_name", "Wallet Platform")

	// Platform fees defaults (in SZL)
	viper.SetDefault("fees.card_creation.standard", 25.00)
	viper.SetDefault("fees.card_creation.premium", 50.00)
	viper.SetDefault("fees.card_creation.business", 100.00)
	viper.SetDefault("fees.withdrawal_percentage", 1.0)
	viper.SetDefault("fees.transaction_percentage", 0.5)
	viper.SetDefault("fees.monthly_maintenance", 10.00)
	viper.SetDefault("fees.card_replacement", 15.00)
	viper.SetDefault("fees.international_transaction_percentage", 2.5)
	viper.SetDefault("fees.atm_withdrawal", 5.00)

	// Card limits defaults
	// Standard card limits
	viper.SetDefault("card_limits.standard.spending_limit", 5000.00)
	viper.SetDefault("card_limits.standard.daily_spending_limit", 1000.00)
	viper.SetDefault("card_limits.standard.monthly_spending_limit", 10000.00)
	viper.SetDefault("card_limits.standard.max_cards_per_wallet", 3)

	// Premium card limits
	viper.SetDefault("card_limits.premium.spending_limit", 15000.00)
	viper.SetDefault("card_limits.premium.daily_spending_limit", 3000.00)
	viper.SetDefault("card_limits.premium.monthly_spending_limit", 30000.00)
	viper.SetDefault("card_limits.premium.max_cards_per_wallet", 5)

	// Business card limits
	viper.SetDefault("card_limits.business.spending_limit", 50000.00)
	viper.SetDefault("card_limits.business.daily_spending_limit", 10000.00)
	viper.SetDefault("card_limits.business.monthly_spending_limit", 100000.00)
	viper.SetDefault("card_limits.business.max_cards_per_wallet", 10)

	// Card creation limits
	viper.SetDefault("card_limits.creation.max_cards_per_day", 3)
	viper.SetDefault("card_limits.creation.cooldown_hours", 24)

	// Transaction limits
	viper.SetDefault("card_limits.transaction.min_amount", 1.00)
	viper.SetDefault("card_limits.transaction.max_amount_standard", 5000.00)
	viper.SetDefault("card_limits.transaction.max_amount_premium", 15000.00)
	viper.SetDefault("card_limits.transaction.max_amount_business", 50000.00)

	// ATM withdrawal limits
	viper.SetDefault("card_limits.atm.daily_limit_standard", 2000.00)
	viper.SetDefault("card_limits.atm.daily_limit_premium", 5000.00)
	viper.SetDefault("card_limits.atm.daily_limit_business", 10000.00)
	viper.SetDefault("card_limits.atm.monthly_limit_standard", 20000.00)
	viper.SetDefault("card_limits.atm.monthly_limit_premium", 50000.00)
	viper.SetDefault("card_limits.atm.monthly_limit_business", 100000.00)
}
