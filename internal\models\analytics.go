package models

import (
	"time"

	"gorm.io/datatypes"
)

// WalletAnalytics represents aggregated analytics data for wallets
type WalletAnalytics struct {
	ID       uint   `json:"id" gorm:"primaryKey"`
	WalletID uint   `json:"wallet_id" gorm:"index;not null"`
	Wallet   Wallet `json:"wallet" gorm:"constraint:OnDelete:CASCADE"`

	// Time period
	PeriodType  string    `json:"period_type" gorm:"type:varchar(20);not null"` // daily, weekly, monthly, yearly
	PeriodStart time.Time `json:"period_start"`
	PeriodEnd   time.Time `json:"period_end"`

	// Transaction metrics
	TransactionCount int `json:"transaction_count" gorm:"default:0"`
	CreditCount      int `json:"credit_count" gorm:"default:0"`
	DebitCount       int `json:"debit_count" gorm:"default:0"`
	TransferInCount  int `json:"transfer_in_count" gorm:"default:0"`
	TransferOutCount int `json:"transfer_out_count" gorm:"default:0"`

	// Amount metrics
	TotalAmount       float64 `json:"total_amount" gorm:"type:decimal(15,2);default:0"`
	CreditAmount      float64 `json:"credit_amount" gorm:"type:decimal(15,2);default:0"`
	DebitAmount       float64 `json:"debit_amount" gorm:"type:decimal(15,2);default:0"`
	TransferInAmount  float64 `json:"transfer_in_amount" gorm:"type:decimal(15,2);default:0"`
	TransferOutAmount float64 `json:"transfer_out_amount" gorm:"type:decimal(15,2);default:0"`

	// Balance metrics
	StartingBalance float64 `json:"starting_balance" gorm:"type:decimal(15,2);default:0"`
	EndingBalance   float64 `json:"ending_balance" gorm:"type:decimal(15,2);default:0"`
	AverageBalance  float64 `json:"average_balance" gorm:"type:decimal(15,2);default:0"`
	MinBalance      float64 `json:"min_balance" gorm:"type:decimal(15,2);default:0"`
	MaxBalance      float64 `json:"max_balance" gorm:"type:decimal(15,2);default:0"`

	// Usage patterns
	ActiveDays int `json:"active_days" gorm:"default:0"`
	PeakHour   int `json:"peak_hour" gorm:"default:0"` // Hour of day with most activity
	PeakDay    int `json:"peak_day" gorm:"default:0"`  // Day of week with most activity

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// PayCardAnalytics represents aggregated analytics data for pay cards
type PayCardAnalytics struct {
	ID     uint    `json:"id" gorm:"primaryKey"`
	CardID uint    `json:"card_id" gorm:"index;not null"`
	Card   PayCard `json:"card" gorm:"constraint:OnDelete:CASCADE"`

	// Time period
	PeriodType  string    `json:"period_type" gorm:"type:varchar(20);not null"` // daily, weekly, monthly, yearly
	PeriodStart time.Time `json:"period_start"`
	PeriodEnd   time.Time `json:"period_end"`

	// Transaction metrics
	TransactionCount int `json:"transaction_count" gorm:"default:0"`
	PurchaseCount    int `json:"purchase_count" gorm:"default:0"`
	WithdrawalCount  int `json:"withdrawal_count" gorm:"default:0"`
	RefundCount      int `json:"refund_count" gorm:"default:0"`

	// Amount metrics
	TotalAmount      float64 `json:"total_amount" gorm:"type:decimal(15,2);default:0"`
	PurchaseAmount   float64 `json:"purchase_amount" gorm:"type:decimal(15,2);default:0"`
	WithdrawalAmount float64 `json:"withdrawal_amount" gorm:"type:decimal(15,2);default:0"`
	RefundAmount     float64 `json:"refund_amount" gorm:"type:decimal(15,2);default:0"`

	// Merchant analytics
	UniqueMerchants int    `json:"unique_merchants" gorm:"default:0"`
	TopMerchant     string `json:"top_merchant" gorm:"type:varchar(100)"`
	TopCategory     string `json:"top_category" gorm:"type:varchar(50)"`

	// Usage patterns
	ActiveDays         int     `json:"active_days" gorm:"default:0"`
	AverageTransaction float64 `json:"average_transaction" gorm:"type:decimal(15,2);default:0"`
	PeakHour           int     `json:"peak_hour" gorm:"default:0"`

	// Security metrics
	FailedAttempts int `json:"failed_attempts" gorm:"default:0"`
	SecurityEvents int `json:"security_events" gorm:"default:0"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// SystemAnalytics represents platform-wide analytics
type SystemAnalytics struct {
	ID uint `json:"id" gorm:"primaryKey"`

	// Time period
	PeriodType  string    `json:"period_type" gorm:"type:varchar(20);not null"` // daily, weekly, monthly, yearly
	PeriodStart time.Time `json:"period_start"`
	PeriodEnd   time.Time `json:"period_end"`

	// User metrics
	TotalWallets    int `json:"total_wallets" gorm:"default:0"`
	ActiveWallets   int `json:"active_wallets" gorm:"default:0"`
	NewWallets      int `json:"new_wallets" gorm:"default:0"`
	VerifiedWallets int `json:"verified_wallets" gorm:"default:0"`

	// Card metrics
	TotalCards    int `json:"total_cards" gorm:"default:0"`
	ActiveCards   int `json:"active_cards" gorm:"default:0"`
	NewCards      int `json:"new_cards" gorm:"default:0"`
	PhysicalCards int `json:"physical_cards" gorm:"default:0"`

	// Transaction metrics
	TotalTransactions  int `json:"total_transactions" gorm:"default:0"`
	WalletTransactions int `json:"wallet_transactions" gorm:"default:0"`
	CardTransactions   int `json:"card_transactions" gorm:"default:0"`
	FailedTransactions int `json:"failed_transactions" gorm:"default:0"`

	// Volume metrics
	TotalVolume        float64 `json:"total_volume" gorm:"type:decimal(15,2);default:0"`
	WalletVolume       float64 `json:"wallet_volume" gorm:"type:decimal(15,2);default:0"`
	CardVolume         float64 `json:"card_volume" gorm:"type:decimal(15,2);default:0"`
	AverageTransaction float64 `json:"average_transaction" gorm:"type:decimal(15,2);default:0"`

	// Security metrics
	SecurityEvents      int `json:"security_events" gorm:"default:0"`
	FraudAlerts         int `json:"fraud_alerts" gorm:"default:0"`
	BlockedTransactions int `json:"blocked_transactions" gorm:"default:0"`

	// Service metrics
	ActiveSubscriptions int     `json:"active_subscriptions" gorm:"default:0"`
	ServiceRevenue      float64 `json:"service_revenue" gorm:"type:decimal(15,2);default:0"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// MerchantAnalytics represents analytics for merchant transactions
type MerchantAnalytics struct {
	ID               uint   `json:"id" gorm:"primaryKey"`
	MerchantName     string `json:"merchant_name" gorm:"type:varchar(100);not null"`
	MerchantCategory string `json:"merchant_category" gorm:"type:varchar(50);not null"`

	// Time period
	PeriodType  string    `json:"period_type" gorm:"type:varchar(20);not null"` // daily, weekly, monthly, yearly
	PeriodStart time.Time `json:"period_start"`
	PeriodEnd   time.Time `json:"period_end"`

	// Transaction metrics
	TransactionCount       int `json:"transaction_count" gorm:"default:0"`
	UniqueCards            int `json:"unique_cards" gorm:"default:0"`
	SuccessfulTransactions int `json:"successful_transactions" gorm:"default:0"`
	FailedTransactions     int `json:"failed_transactions" gorm:"default:0"`

	// Amount metrics
	TotalAmount        float64 `json:"total_amount" gorm:"type:decimal(15,2);default:0"`
	AverageTransaction float64 `json:"average_transaction" gorm:"type:decimal(15,2);default:0"`
	MinTransaction     float64 `json:"min_transaction" gorm:"type:decimal(15,2);default:0"`
	MaxTransaction     float64 `json:"max_transaction" gorm:"type:decimal(15,2);default:0"`

	// Performance metrics
	SuccessRate           float64 `json:"success_rate" gorm:"type:decimal(5,2);default:0"`
	AverageProcessingTime float64 `json:"average_processing_time" gorm:"type:decimal(10,3);default:0"` // seconds

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// RevenueAnalytics represents revenue analytics for the platform
type RevenueAnalytics struct {
	ID uint `json:"id" gorm:"primaryKey"`

	// Time period
	PeriodType  string    `json:"period_type" gorm:"type:varchar(20);not null"` // daily, weekly, monthly, yearly
	PeriodStart time.Time `json:"period_start"`
	PeriodEnd   time.Time `json:"period_end"`

	// Revenue sources
	TransactionFees  float64 `json:"transaction_fees" gorm:"type:decimal(15,2);default:0"`
	CardFees         float64 `json:"card_fees" gorm:"type:decimal(15,2);default:0"`
	SubscriptionFees float64 `json:"subscription_fees" gorm:"type:decimal(15,2);default:0"`
	ServiceFees      float64 `json:"service_fees" gorm:"type:decimal(15,2);default:0"`
	InterchangeFees  float64 `json:"interchange_fees" gorm:"type:decimal(15,2);default:0"`

	// Total revenue
	TotalRevenue float64 `json:"total_revenue" gorm:"type:decimal(15,2);default:0"`
	NetRevenue   float64 `json:"net_revenue" gorm:"type:decimal(15,2);default:0"`

	// Costs
	ProcessingCosts  float64 `json:"processing_costs" gorm:"type:decimal(15,2);default:0"`
	OperationalCosts float64 `json:"operational_costs" gorm:"type:decimal(15,2);default:0"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// AnalyticsReport represents a generated analytics report
type AnalyticsReport struct {
	ID          uint   `json:"id" gorm:"primaryKey"`
	ReportType  string `json:"report_type" gorm:"type:varchar(50);not null"` // wallet, card, system, merchant, revenue
	ReportName  string `json:"report_name" gorm:"type:varchar(100);not null"`
	Description string `json:"description" gorm:"type:text"`

	// Report parameters
	PeriodType  string         `json:"period_type" gorm:"type:varchar(20);not null"`
	PeriodStart time.Time      `json:"period_start"`
	PeriodEnd   time.Time      `json:"period_end"`
	Filters     datatypes.JSON `json:"filters"` // JSON object of applied filters

	// Report data
	Data    datatypes.JSON `json:"data" gorm:"type:json;not null"`
	Summary datatypes.JSON `json:"summary"` // Summary statistics
	Charts  datatypes.JSON `json:"charts"`  // Chart configurations

	// Generation details
	GeneratedBy *uint  `json:"generated_by"`                                       // User who generated the report
	Status      string `json:"status" gorm:"type:varchar(20);default:'completed'"` // pending, processing, completed, failed
	FileURL     string `json:"file_url" gorm:"type:varchar(255)"`                  // URL to downloadable report file
	FileFormat  string `json:"file_format" gorm:"type:varchar(20)"`                // pdf, excel, csv, json

	// Scheduling
	IsScheduled     bool       `json:"is_scheduled" gorm:"default:false"`
	SchedulePattern string     `json:"schedule_pattern" gorm:"type:varchar(100)"` // Cron pattern
	NextGeneration  *time.Time `json:"next_generation"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// AnalyticsRequest represents a request for analytics data
type AnalyticsRequest struct {
	ReportType string                 `json:"report_type" binding:"required,oneof=wallet card system merchant revenue"`
	PeriodType string                 `json:"period_type" binding:"required,oneof=daily weekly monthly yearly"`
	StartDate  string                 `json:"start_date" binding:"required"` // ISO 8601 format
	EndDate    string                 `json:"end_date" binding:"required"`   // ISO 8601 format
	WalletID   *uint                  `json:"wallet_id"`
	CardID     *uint                  `json:"card_id"`
	Filters    map[string]interface{} `json:"filters"`
	GroupBy    []string               `json:"group_by"`
	Metrics    []string               `json:"metrics"`
	Format     string                 `json:"format" binding:"omitempty,oneof=json csv excel pdf"`
}

// AnalyticsResponse represents the response format for analytics data
type AnalyticsResponse struct {
	ReportType  string                   `json:"report_type"`
	PeriodType  string                   `json:"period_type"`
	PeriodStart time.Time                `json:"period_start"`
	PeriodEnd   time.Time                `json:"period_end"`
	Data        []map[string]interface{} `json:"data"`
	Summary     map[string]interface{}   `json:"summary"`
	Charts      []map[string]interface{} `json:"charts"`
	GeneratedAt time.Time                `json:"generated_at"`
}

// DashboardMetrics represents key metrics for dashboard display
type DashboardMetrics struct {
	// Wallet metrics
	TotalWallets  int     `json:"total_wallets"`
	ActiveWallets int     `json:"active_wallets"`
	TotalBalance  float64 `json:"total_balance"`

	// Card metrics
	TotalCards  int `json:"total_cards"`
	ActiveCards int `json:"active_cards"`

	// Transaction metrics (last 24 hours)
	TransactionsToday int     `json:"transactions_today"`
	VolumeToday       float64 `json:"volume_today"`

	// Growth metrics (compared to previous period)
	WalletGrowth      float64 `json:"wallet_growth"`      // percentage
	TransactionGrowth float64 `json:"transaction_growth"` // percentage
	VolumeGrowth      float64 `json:"volume_growth"`      // percentage

	// Security metrics
	SecurityEvents int `json:"security_events"`
	FraudAlerts    int `json:"fraud_alerts"`

	// Revenue metrics
	RevenueToday  float64 `json:"revenue_today"`
	RevenueGrowth float64 `json:"revenue_growth"` // percentage

	LastUpdated time.Time `json:"last_updated"`
}

// ReportSchedule represents scheduled report generation
type ReportSchedule struct {
	ID          uint   `json:"id" gorm:"primaryKey"`
	ReportType  string `json:"report_type" gorm:"type:varchar(50);not null"`
	ReportName  string `json:"report_name" gorm:"type:varchar(100);not null"`
	Description string `json:"description" gorm:"type:text"`

	// Schedule configuration
	SchedulePattern string `json:"schedule_pattern" gorm:"type:varchar(100);not null"` // Cron pattern
	IsActive        bool   `json:"is_active" gorm:"default:true"`

	// Report parameters
	Parameters datatypes.JSON `json:"parameters" gorm:"type:json;not null"`

	// Recipients
	Recipients     datatypes.JSON `json:"recipients"`                                              // Email addresses or webhook URLs
	DeliveryMethod string         `json:"delivery_method" gorm:"type:varchar(20);default:'email'"` // email, webhook, storage

	// Execution tracking
	LastRun      *time.Time `json:"last_run"`
	NextRun      *time.Time `json:"next_run"`
	RunCount     int        `json:"run_count" gorm:"default:0"`
	FailureCount int        `json:"failure_count" gorm:"default:0"`
	LastError    string     `json:"last_error" gorm:"type:text"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// Alert represents system alerts and notifications
type Alert struct {
	ID        uint           `gorm:"primaryKey" json:"id"`
	Type      string         `gorm:"type:varchar(50);not null" json:"type"`  // system, security, performance, business
	Level     string         `gorm:"type:varchar(20);not null" json:"level"` // info, warning, error, critical
	Title     string         `gorm:"type:varchar(255);not null" json:"title"`
	Message   string         `gorm:"type:text" json:"message"`
	Component string         `gorm:"type:varchar(100)" json:"component"`
	Metadata  datatypes.JSON `json:"metadata"`
	IsRead    bool           `gorm:"default:false" json:"is_read"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
}

// ScheduledJob represents scheduled analytics jobs
type ScheduledJob struct {
	ID           uint           `gorm:"primaryKey" json:"id"`
	Name         string         `gorm:"type:varchar(100);not null" json:"name"`
	Type         string         `gorm:"type:varchar(50);not null" json:"type"`      // system_health, metrics_aggregation, report_generation
	Schedule     string         `gorm:"type:varchar(100);not null" json:"schedule"` // cron expression
	IsEnabled    bool           `gorm:"default:true" json:"is_enabled"`
	LastRun      *time.Time     `json:"last_run"`
	NextRun      *time.Time     `json:"next_run"`
	LastResult   string         `gorm:"type:varchar(20)" json:"last_result"` // success, failed, running
	LastError    string         `gorm:"type:text" json:"last_error"`
	RunCount     int            `gorm:"default:0" json:"run_count"`
	FailureCount int            `gorm:"default:0" json:"failure_count"`
	Config       datatypes.JSON `json:"config"` // job-specific configuration
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
}

// JobExecution represents the execution history of scheduled jobs
type JobExecution struct {
	ID               uint           `gorm:"primaryKey" json:"id"`
	JobID            uint           `gorm:"index;not null" json:"job_id"`
	Job              ScheduledJob   `gorm:"constraint:OnDelete:CASCADE" json:"job"`
	Status           string         `gorm:"type:varchar(20);not null" json:"status"` // running, success, failed
	StartTime        time.Time      `json:"start_time"`
	EndTime          *time.Time     `json:"end_time"`
	Duration         int64          `json:"duration"` // milliseconds
	Result           datatypes.JSON `json:"result"`   // execution result data
	Error            string         `gorm:"type:text" json:"error"`
	RecordsProcessed int            `gorm:"default:0" json:"records_processed"`
	CreatedAt        time.Time      `json:"created_at"`
}
