package services

import (
	"fmt"
	"wallet-platform/internal/clients"
	"wallet-platform/internal/config"
	"wallet-platform/internal/models"
	"wallet-platform/pkg/logger"
	"wallet-platform/pkg/redis"

	"gorm.io/gorm"
)

// Container holds all service dependencies
type Container struct {
	DB     *gorm.DB
	Redis  *redis.Client
	Config *config.Config
	Logger *logger.Logger

	// Services
	WalletService       *WalletService
	PayCardService      *PayCardService
	UserService         *UserService
	SecurityService     *SecurityService
	AnalyticsService    *AnalyticsService
	ServiceManager      *ServiceManager
	WebhookService      *WebhookService
	EventService        *EventService
	PaymentEngineClient *clients.PaymentEngineClient

	// Enhanced Security services
	FraudDetectionService   *FraudDetectionService
	DeviceManagementService *DeviceManagementService
	IPGeolocationService    *IPGeolocationService
	Enhanced2FAService      *Enhanced2FAService

	// Enhanced Analytics and Monitoring services
	EnhancedAnalyticsService *EnhancedAnalyticsService
	MonitoringService        *MonitoringService
	DashboardService         *DashboardService
	AnalyticsJobsService     *AnalyticsJobsService

	// Migration service
	MigrationService *MigrationService
}

// NewContainer creates a new service container
func NewContainer(db *gorm.DB, redisClient *redis.Client, cfg *config.Config, log *logger.Logger) *Container {
	container := &Container{
		DB:     db,
		Redis:  redisClient,
		Config: cfg,
		Logger: log,
	}

	// Initialize services (handle nil database for local development)
	fmt.Printf("CONTAINER DEBUG: Initializing services, db is nil: %v\n", db == nil)

	if db != nil {
		fmt.Printf("CONTAINER DEBUG: Database available, creating real services\n")

		fmt.Printf("CONTAINER DEBUG: Creating WalletService\n")
		container.WalletService = NewWalletService(db, redisClient, log, cfg)
		if container.WalletService == nil {
			fmt.Printf("CONTAINER ERROR: WalletService creation returned nil\n")
		} else {
			fmt.Printf("CONTAINER DEBUG: WalletService created successfully\n")
		}

		fmt.Printf("CONTAINER DEBUG: Creating PayCardService\n")
		container.PayCardService = NewPayCardService(db, redisClient, log, container.WalletService, cfg)

		fmt.Printf("CONTAINER DEBUG: Creating UserService\n")
		container.UserService = NewUserService(db, log)

		fmt.Printf("CONTAINER DEBUG: Creating SecurityService\n")
		container.SecurityService = NewSecurityService(db, redisClient, log)

		fmt.Printf("CONTAINER DEBUG: Creating AnalyticsService\n")
		container.AnalyticsService = NewAnalyticsService(db, redisClient, log)

		fmt.Printf("CONTAINER DEBUG: Creating ServiceManager\n")
		container.ServiceManager = NewServiceManager(db, redisClient, log)

		fmt.Printf("CONTAINER DEBUG: All services created\n")
	} else {
		// For local development without database - services will be nil
		fmt.Printf("CONTAINER DEBUG: Database is disabled - setting services to nil\n")
		log.Info("Database is disabled - services will return mock responses")
		container.WalletService = nil
		container.PayCardService = nil
		container.UserService = nil
		container.SecurityService = nil
		container.AnalyticsService = nil
		container.ServiceManager = nil
	}

	// Initialize communication services
	container.PaymentEngineClient = clients.NewPaymentEngineClient(cfg, log)
	container.WebhookService = NewWebhookService(db, log)
	container.EventService = NewEventService(db, log, container.WebhookService, container.PaymentEngineClient)

	// Initialize enhanced security services
	container.FraudDetectionService = NewFraudDetectionService(db, log)
	container.DeviceManagementService = NewDeviceManagementService(db, log)
	container.IPGeolocationService = NewIPGeolocationService(db, log, "") // API key from config
	container.Enhanced2FAService = NewEnhanced2FAService(db, log, "Wallet Platform")

	// Initialize enhanced analytics and monitoring services
	container.EnhancedAnalyticsService = NewEnhancedAnalyticsService(db, log)
	container.MonitoringService = NewMonitoringService(db, redisClient, log)
	container.DashboardService = NewDashboardService(db, redisClient, log, container.MonitoringService, container.AnalyticsService)
	container.AnalyticsJobsService = NewAnalyticsJobsService(db, redisClient, log, container.MonitoringService, container.EnhancedAnalyticsService)

	// Initialize migration service (for data migration from main system)
	// Note: In production, you would configure a separate source database connection
	container.MigrationService = NewMigrationService(db, db, log) // Using same DB for now

	return container
}

// WalletServiceInterface defines the wallet service interface
type WalletServiceInterface interface {
	CreateWallet(phoneNumber, walletType string) (*models.WalletResponse, error)
	GetWallet(id uint) (*models.WalletResponse, error)
	GetWalletByPhone(phoneNumber string) (*models.WalletResponse, error)
	UpdateWallet(id uint, updates map[string]interface{}) (*models.WalletResponse, error)
	GetBalance(walletID uint) (float64, error)
	Transfer(fromWalletID, toWalletID uint, amount float64, description string) (*models.TransferResponse, error)
	GetTransactionHistory(walletID uint, filters map[string]interface{}) ([]models.TransactionResponse, error)
	TopupWallet(walletID uint, amount float64, topupPhone, paymentMethod, reference string) (*models.TransactionResponse, error)
	WithdrawFunds(walletID uint, amount float64, destination, reference string) (*models.TransactionResponse, error)
	FundOrUnfundWallet(phone string, amount float64, direction string) error

	// Phone-based convenience methods
	TransferByPhone(fromPhoneNumber, toPhoneNumber string, amount float64, description string) (*models.TransferResponse, error)
	TopupWalletByPhone(phoneNumber string, amount float64, paymentMethod, reference string) (*models.TransactionResponse, error)
	GetBalanceByPhone(phoneNumber string) (float64, error)
	GetTransactionHistoryByPhone(phoneNumber string, filters map[string]interface{}) ([]models.TransactionResponse, error)
	FreezeWalletByPhone(phoneNumber string, reason string) error
	UnfreezeWalletByPhone(phoneNumber string) error
	GetAllWallets(page, limit int, status, walletType string) ([]models.WalletResponse, int64, error)
	FreezeWallet(walletID uint, reason string) error
	UnfreezeWallet(walletID uint) error

	// Platform wallet methods
	EnsurePlatformWallet() (*models.Wallet, error)
	GetPlatformWallet() (*models.Wallet, error)
	CollectPlatformFee(fromWalletID uint, amount float64, feeType, description string) error
}

// PayCardServiceInterface defines the paycard service interface
type PayCardServiceInterface interface {
	CreateCard(walletID uint, cardType, holderName string) (*PayCardResponse, error)
	GetCard(id uint) (*PayCardResponse, error)
	GetCardsByWallet(walletID uint) ([]PayCardResponse, error)
	UpdateCard(id uint, updates map[string]interface{}) (*PayCardResponse, error)
	ProcessTransaction(cardNumber string, amount float64, merchantInfo map[string]interface{}) (*PayCardTransactionResponse, error)
	UpdatePIN(cardID uint, currentPIN, newPIN string) error
	VerifyCardPIN(cardID uint, pin string) error
	BlockCard(cardID uint, reason string) error
	UnblockCard(cardID uint) error
	GetTransactionHistory(cardID uint, filters map[string]interface{}) ([]PayCardTransactionResponse, error)
	GetAllCards(page, limit int, status, cardType, search string) ([]models.PayCardResponse, int64, error)

	// Enhanced card management methods
	GetCardCount(walletID uint) (int64, error)
	GetActiveCardsByType(walletID uint, cardType string) ([]PayCardResponse, error)
	SetSpendingLimits(cardID uint, dailyLimit, monthlyLimit, transactionLimit float64) error
	SetMerchantRestrictions(cardID uint, blacklistedCategories []string, whitelistedMerchants []string) error
	SetTimeRestrictions(cardID uint, businessHoursOnly, noWeekends bool, allowedTimeWindows []map[string]string) error
	GetCardSecuritySettings(cardID uint) (map[string]interface{}, error)
	EnableCardFeature(cardID uint, feature string) error
	DisableCardFeature(cardID uint, feature string) error
	GetCardUsageAnalytics(cardID uint, days int) (map[string]interface{}, error)

	// QR Code payment methods
	GenerateQRCodeData(cardID uint) (string, error)
	ValidateQRCodeData(qrCodeString string) (*QRCodePaymentData, error)
	ProcessQRCodePayment(request QRCodeTransactionRequest) (*PayCardTransactionResponse, error)
}

// SecurityServiceInterface defines the security service interface
type SecurityServiceInterface interface {
	RegisterDevice(walletID uint, deviceInfo map[string]interface{}) (*DeviceRegistration, error)
	VerifyDevice(deviceFingerprint, verificationCode string) error
	Setup2FA(walletID uint, method string, contact string) error
	Verify2FA(walletID uint, code string) error
	LogSecurityEvent(walletID uint, eventType, description string, metadata map[string]interface{}) error
	CheckFraud(walletID uint, transactionData map[string]interface{}) (*FraudAlert, error)
	UpdateSecuritySettings(walletID uint, settings map[string]interface{}) error
	GetSecuritySettings(walletID uint) (*SecuritySettings, error)
}

// AnalyticsServiceInterface defines the analytics service interface
type AnalyticsServiceInterface interface {
	GenerateWalletAnalytics(walletID uint, period string, startDate, endDate string) (*AnalyticsResponse, error)
	GenerateCardAnalytics(cardID uint, period string, startDate, endDate string) (*AnalyticsResponse, error)
	GenerateSystemAnalytics(period string, startDate, endDate string) (*AnalyticsResponse, error)
	GetDashboardMetrics() (*DashboardMetrics, error)
	CreateReport(reportType, period string, filters map[string]interface{}) (*AnalyticsReport, error)
	ScheduleReport(reportType, schedule string, parameters map[string]interface{}) (*ReportSchedule, error)
}

// ServiceManagerInterface defines the service subscription interface
type ServiceManagerInterface interface {
	GetAvailableServices() ([]ServiceResponse, error)
	SubscribeToService(walletID, serviceID uint, billingCycle string) (*ServiceSubscriptionResponse, error)
	GetSubscriptions(walletID uint) ([]ServiceSubscriptionResponse, error)
	UpdateSubscription(subscriptionID uint, updates map[string]interface{}) (*ServiceSubscriptionResponse, error)
	CancelSubscription(subscriptionID uint, reason string) error
	ProcessServiceBilling(subscriptionID uint) (*ServiceBilling, error)
	RecordServiceUsage(subscriptionID uint, usageType string, quantity int) error
	GetServiceUsage(subscriptionID uint, startDate, endDate, usageType string) ([]models.ServiceUsageResponse, error)
}

// Response types (these would be imported from models in a real implementation)
type WalletResponse struct {
	ID            uint    `json:"id"`
	PhoneNumber   string  `json:"phone_number"`
	AccountNumber string  `json:"account_number"`
	WalletType    string  `json:"wallet_type"`
	Balance       float64 `json:"balance"`
	Currency      string  `json:"currency"`
	Status        string  `json:"status"`
	IsVerified    bool    `json:"is_verified"`
	KYCLevel      string  `json:"kyc_level"`
}

type TransferResponse struct {
	ID          uint    `json:"id"`
	Reference   string  `json:"reference"`
	Amount      float64 `json:"amount"`
	Fee         float64 `json:"fee"`
	TotalAmount float64 `json:"total_amount"`
	Status      string  `json:"status"`
	Description string  `json:"description"`
}

type TransactionResponse struct {
	ID           uint    `json:"id"`
	Type         string  `json:"type"`
	Amount       float64 `json:"amount"`
	BalanceAfter float64 `json:"balance_after"`
	Reference    string  `json:"reference"`
	Description  string  `json:"description"`
	Category     string  `json:"category"`
}

type PayCardResponse struct {
	ID                   uint    `json:"id"`
	CardNumber           string  `json:"card_number"`
	WalletID             uint    `json:"wallet_id"`
	CardHolderName       string  `json:"card_holder_name"`
	CardType             string  `json:"card_type"`
	Status               string  `json:"status"`
	SpendingLimit        float64 `json:"spending_limit"`
	DailySpendingLimit   float64 `json:"daily_spending_limit"`
	MonthlySpendingLimit float64 `json:"monthly_spending_limit"`
	IsPinSet             bool    `json:"is_pin_set"`
}

type PayCardTransactionResponse struct {
	ID               uint    `json:"id"`
	TransactionID    string  `json:"transaction_id"`
	CardID           uint    `json:"card_id"`
	Amount           float64 `json:"amount"`
	Currency         string  `json:"currency"`
	MerchantName     string  `json:"merchant_name"`
	MerchantCategory string  `json:"merchant_category"`
	TransactionType  string  `json:"transaction_type"`
	Status           string  `json:"status"`
	Reference        string  `json:"reference"`
}

type DeviceRegistration struct {
	ID                uint   `json:"id"`
	WalletID          uint   `json:"wallet_id"`
	DeviceFingerprint string `json:"device_fingerprint"`
	DeviceName        string `json:"device_name"`
	DeviceType        string `json:"device_type"`
	IsTrusted         bool   `json:"is_trusted"`
	IsVerified        bool   `json:"is_verified"`
}

type FraudAlert struct {
	ID             uint     `json:"id"`
	WalletID       uint     `json:"wallet_id"`
	RiskLevel      string   `json:"risk_level"`
	RiskScore      float64  `json:"risk_score"`
	Reasons        []string `json:"reasons"`
	Recommendation string   `json:"recommendation"`
	Status         string   `json:"status"`
}

type SecuritySettings struct {
	ID                    uint `json:"id"`
	WalletID              uint `json:"wallet_id"`
	RequireDeviceAuth     bool `json:"require_device_auth"`
	Require2FAForTransfer bool `json:"require_2fa_for_transfer"`
	Require2FAForCards    bool `json:"require_2fa_for_cards"`
	SessionTimeout        int  `json:"session_timeout"`
}

type AnalyticsResponse struct {
	ReportType string                   `json:"report_type"`
	PeriodType string                   `json:"period_type"`
	Data       []map[string]interface{} `json:"data"`
	Summary    map[string]interface{}   `json:"summary"`
	Charts     []map[string]interface{} `json:"charts"`
}

type DashboardMetrics struct {
	TotalWallets      int     `json:"total_wallets"`
	ActiveWallets     int     `json:"active_wallets"`
	TotalBalance      float64 `json:"total_balance"`
	TotalCards        int     `json:"total_cards"`
	ActiveCards       int     `json:"active_cards"`
	TransactionsToday int     `json:"transactions_today"`
	VolumeToday       float64 `json:"volume_today"`
}

type AnalyticsReport struct {
	ID         uint                   `json:"id"`
	ReportType string                 `json:"report_type"`
	ReportName string                 `json:"report_name"`
	Status     string                 `json:"status"`
	Data       map[string]interface{} `json:"data"`
	FileURL    string                 `json:"file_url"`
}

type ReportSchedule struct {
	ID              uint                   `json:"id"`
	ReportType      string                 `json:"report_type"`
	ReportName      string                 `json:"report_name"`
	SchedulePattern string                 `json:"schedule_pattern"`
	IsActive        bool                   `json:"is_active"`
	Parameters      map[string]interface{} `json:"parameters"`
}

type ServiceResponse struct {
	ID               uint    `json:"id"`
	Name             string  `json:"name"`
	Description      string  `json:"description"`
	Category         string  `json:"category"`
	Provider         string  `json:"provider"`
	Price            float64 `json:"price"`
	Currency         string  `json:"currency"`
	BillingCycle     string  `json:"billing_cycle"`
	ServiceType      string  `json:"service_type"`
	IsActive         bool    `json:"is_active"`
	MinWalletBalance float64 `json:"min_wallet_balance"`
}

type ServiceSubscriptionResponse struct {
	ID               uint    `json:"id"`
	ServiceID        uint    `json:"service_id"`
	ServiceName      string  `json:"service_name"`
	Status           string  `json:"status"`
	SubscriptionType string  `json:"subscription_type"`
	BillingCycle     string  `json:"billing_cycle"`
	Amount           float64 `json:"amount"`
	Currency         string  `json:"currency"`
	UsageCount       int     `json:"usage_count"`
	UsageLimit       int     `json:"usage_limit"`
	AutoRenew        bool    `json:"auto_renew"`
}

type ServiceBilling struct {
	ID                 uint    `json:"id"`
	SubscriptionID     uint    `json:"subscription_id"`
	Amount             float64 `json:"amount"`
	Currency           string  `json:"currency"`
	Status             string  `json:"status"`
	BillingPeriodStart string  `json:"billing_period_start"`
	BillingPeriodEnd   string  `json:"billing_period_end"`
}
