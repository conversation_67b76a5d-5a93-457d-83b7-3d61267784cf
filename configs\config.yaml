# Wallet Platform Configuration

app:
  name: "Wallet Platform"
  version: "1.0.0"
  environment: "development"
  debug: true

server:
  port: 8086
  host: "0.0.0.0"
  environment: "development"
  read_timeout: 30
  write_timeout: 30
  idle_timeout: 120
  cors:
    allow_origins:
      - "*"
    allow_methods:
      - "GET"
      - "POST"
      - "PUT"
      - "DELETE"
      - "OPTIONS"
    allow_headers:
      - "*"
    allow_credentials: true
    max_age: 86400

database:
  driver: "mysql"
  host: "localhost"
  port: 3306
  username: ""  # MUST be set via DATABASE_USERNAME environment variable
  password: ""  # MUST be set via DATABASE_PASSWORD environment variable
  database: "wallet_platform"
  ssl_mode: "true"  # Always use SSL in production
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: 300
  conn_max_idle_time: 60

redis:
  enabled: false  # Set to true to enable Redis for rate limiting
  host: "localhost"
  port: 6379
  password: ""
  database: 0
  pool_size: 10
  min_idle_conns: 2
  max_retries: 3

jwt:
  secret_key: ""  # MUST be set via JWT_SECRET_KEY environment variable
  expiration_time: 3600  # 1 hour
  refresh_time: 86400    # 24 hours
  issuer: "wallet-platform"
  audience: "wallet-platform-users"

log:
  level: "info"
  format: "json"
  output: "stdout"

rate_limit:
  enabled: true
  default_limit: 100
  default_window: 60
  endpoint_limits:
    "/api/v1/auth/login":
      limit: 5
      window: 300  # 5 minutes
    "/api/v1/wallets/transfer":
      limit: 10
      window: 60
    "/api/v1/cards/*/transactions":
      limit: 20
      window: 60

security:
  encryption_key: ""  # MUST be set via SECURITY_ENCRYPTION_KEY environment variable
  hash_salt: ""       # MUST be set via SECURITY_HASH_SALT environment variable
  max_login_attempts: 5
  lockout_duration: 900  # 15 minutes
  session_timeout: 3600  # 1 hour
  require_https: true   # MUST be true in production
  csrf_protection: true
  content_type_no_sniff: true

# Internal API configuration for service-to-service communication
internal_api:
  enabled: true
  key: ""  # MUST be set via INTERNAL_API_KEY environment variable
  allowed_services:
    - "payment-engine"
    - "user-service"
    - "notification-service"
    - "admin-panel"
  rate_limit:
    enabled: true
    limit: 1000  # Higher limit for internal services
    window: 60

external:
  payment_engine:
    base_url: ""  # MUST be set via EXTERNAL_PAYMENT_ENGINE_BASE_URL environment variable
    api_key: ""   # MUST be set via EXTERNAL_PAYMENT_ENGINE_API_KEY environment variable
    timeout: 30
    retry_count: 3

  sms:
    provider: "centurion"
    api_key: ""   # MUST be set via CENTURION_SMS_API_KEY environment variable
    api_url: "https://auth.centurionbd.com/api/v1/sms/send"

  email:
    provider: "centurion"
    api_key: ""   # MUST be set via CENTURION_EMAIL_API_KEY environment variable
    api_url: "https://auth.centurionbd.com/api/v1/email/send"

  webhook:
    secret: ""    # MUST be set via EXTERNAL_WEBHOOK_SECRET environment variable
    timeout: 30
    max_retries: 3
