package load

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

const (
	baseURL           = "http://localhost:8080"
	internalAPIKey    = "test-internal-key"
	concurrentUsers   = 50
	requestsPerUser   = 20
	testDuration      = 30 * time.Second
)

type LoadTestResult struct {
	TotalRequests    int
	SuccessfulReqs   int
	FailedReqs       int
	AverageLatency   time.Duration
	MaxLatency       time.Duration
	MinLatency       time.Duration
	RequestsPerSec   float64
	ErrorRate        float64
}

type RequestMetric struct {
	Duration time.Duration
	Success  bool
	Error    error
}

func TestWalletCreationLoad(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping load test in short mode")
	}

	results := make(chan RequestMetric, concurrentUsers*requestsPerUser)
	var wg sync.WaitGroup

	startTime := time.Now()

	// Launch concurrent users
	for i := 0; i < concurrentUsers; i++ {
		wg.Add(1)
		go func(userID int) {
			defer wg.Done()
			
			for j := 0; j < requestsPerUser; j++ {
				phoneNumber := fmt.Sprintf("+25670%d%03d", userID, j)
				metric := createWalletRequest(phoneNumber)
				results <- metric
				
				// Small delay between requests
				time.Sleep(10 * time.Millisecond)
			}
		}(i)
	}

	// Wait for all goroutines to complete
	wg.Wait()
	close(results)

	// Analyze results
	loadResult := analyzeResults(results, startTime)
	
	// Assertions
	assert.Greater(t, loadResult.SuccessfulReqs, 0, "Should have successful requests")
	assert.Less(t, loadResult.ErrorRate, 0.05, "Error rate should be less than 5%")
	assert.Less(t, loadResult.AverageLatency, 500*time.Millisecond, "Average latency should be less than 500ms")
	assert.Greater(t, loadResult.RequestsPerSec, 10.0, "Should handle at least 10 requests per second")

	// Print results
	printLoadTestResults("Wallet Creation Load Test", loadResult)
}

func TestWalletTransferLoad(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping load test in short mode")
	}

	// First create wallets for testing
	senderWallet := createTestWallet("+256701111111")
	receiverWallet := createTestWallet("+256702222222")
	
	// Topup sender wallet
	topupWallet(senderWallet["id"].(float64), 10000.0)

	results := make(chan RequestMetric, concurrentUsers*requestsPerUser)
	var wg sync.WaitGroup

	startTime := time.Now()

	// Launch concurrent transfer requests
	for i := 0; i < concurrentUsers; i++ {
		wg.Add(1)
		go func(userID int) {
			defer wg.Done()
			
			for j := 0; j < requestsPerUser; j++ {
				metric := transferFundsRequest(
					senderWallet["id"].(float64),
					receiverWallet["id"].(float64),
					1.0, // Small amount to avoid balance issues
				)
				results <- metric
				
				time.Sleep(20 * time.Millisecond)
			}
		}(i)
	}

	wg.Wait()
	close(results)

	loadResult := analyzeResults(results, startTime)
	
	// Assertions for transfer operations (more strict)
	assert.Greater(t, loadResult.SuccessfulReqs, 0, "Should have successful transfers")
	assert.Less(t, loadResult.ErrorRate, 0.10, "Transfer error rate should be less than 10%")
	assert.Less(t, loadResult.AverageLatency, 1*time.Second, "Transfer latency should be less than 1s")

	printLoadTestResults("Wallet Transfer Load Test", loadResult)
}

func TestInternalAPILoad(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping load test in short mode")
	}

	results := make(chan RequestMetric, concurrentUsers*requestsPerUser)
	var wg sync.WaitGroup

	startTime := time.Now()

	// Test internal API endpoints under load
	for i := 0; i < concurrentUsers; i++ {
		wg.Add(1)
		go func(userID int) {
			defer wg.Done()
			
			for j := 0; j < requestsPerUser; j++ {
				phoneNumber := fmt.Sprintf("+25671%d%03d", userID, j)
				
				// Create wallet via internal API
				createMetric := createWalletInternalRequest(phoneNumber)
				results <- createMetric
				
				if createMetric.Success {
					// Get wallet by phone via internal API
					getMetric := getWalletByPhoneRequest(phoneNumber)
					results <- getMetric
				}
				
				time.Sleep(15 * time.Millisecond)
			}
		}(i)
	}

	wg.Wait()
	close(results)

	loadResult := analyzeResults(results, startTime)
	
	// Internal API should have better performance
	assert.Greater(t, loadResult.SuccessfulReqs, 0, "Should have successful internal API requests")
	assert.Less(t, loadResult.ErrorRate, 0.03, "Internal API error rate should be less than 3%")
	assert.Less(t, loadResult.AverageLatency, 300*time.Millisecond, "Internal API latency should be less than 300ms")
	assert.Greater(t, loadResult.RequestsPerSec, 20.0, "Internal API should handle at least 20 requests per second")

	printLoadTestResults("Internal API Load Test", loadResult)
}

func createWalletRequest(phoneNumber string) RequestMetric {
	start := time.Now()
	
	payload := map[string]interface{}{
		"phone_number": phoneNumber,
		"wallet_type":  "individual",
	}
	
	success, err := makeHTTPRequest("POST", "/api/v1/internal/wallets", payload)
	
	return RequestMetric{
		Duration: time.Since(start),
		Success:  success,
		Error:    err,
	}
}

func createWalletInternalRequest(phoneNumber string) RequestMetric {
	start := time.Now()
	
	payload := map[string]interface{}{
		"phone_number": phoneNumber,
		"wallet_type":  "individual",
	}
	
	success, err := makeInternalHTTPRequest("POST", "/api/v1/internal/wallets", payload)
	
	return RequestMetric{
		Duration: time.Since(start),
		Success:  success,
		Error:    err,
	}
}

func getWalletByPhoneRequest(phoneNumber string) RequestMetric {
	start := time.Now()
	
	success, err := makeInternalHTTPRequest("GET", "/api/v1/internal/wallets/phone/"+phoneNumber, nil)
	
	return RequestMetric{
		Duration: time.Since(start),
		Success:  success,
		Error:    err,
	}
}

func transferFundsRequest(fromWalletID, toWalletID, amount float64) RequestMetric {
	start := time.Now()
	
	payload := map[string]interface{}{
		"from_wallet_id": fromWalletID,
		"to_wallet_id":   toWalletID,
		"amount":         amount,
		"reference":      fmt.Sprintf("LOAD_TEST_%d", time.Now().UnixNano()),
	}
	
	success, err := makeInternalHTTPRequest("POST", "/api/v1/internal/wallets/transfer", payload)
	
	return RequestMetric{
		Duration: time.Since(start),
		Success:  success,
		Error:    err,
	}
}

func makeHTTPRequest(method, endpoint string, payload interface{}) (bool, error) {
	return makeInternalHTTPRequest(method, endpoint, payload)
}

func makeInternalHTTPRequest(method, endpoint string, payload interface{}) (bool, error) {
	var reqBody *bytes.Buffer
	if payload != nil {
		jsonData, _ := json.Marshal(payload)
		reqBody = bytes.NewBuffer(jsonData)
	} else {
		reqBody = bytes.NewBuffer([]byte{})
	}

	client := &http.Client{Timeout: 10 * time.Second}
	req, err := http.NewRequest(method, baseURL+endpoint, reqBody)
	if err != nil {
		return false, err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Internal-Key", internalAPIKey)
	req.Header.Set("X-Service-Name", "load-test")

	resp, err := client.Do(req)
	if err != nil {
		return false, err
	}
	defer resp.Body.Close()

	return resp.StatusCode >= 200 && resp.StatusCode < 300, nil
}

func createTestWallet(phoneNumber string) map[string]interface{} {
	payload := map[string]interface{}{
		"phone_number": phoneNumber,
		"wallet_type":  "individual",
	}
	
	jsonData, _ := json.Marshal(payload)
	client := &http.Client{Timeout: 10 * time.Second}
	req, _ := http.NewRequest("POST", baseURL+"/api/v1/internal/wallets", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Internal-Key", internalAPIKey)
	req.Header.Set("X-Service-Name", "load-test")

	resp, _ := client.Do(req)
	defer resp.Body.Close()

	var response map[string]interface{}
	json.NewDecoder(resp.Body).Decode(&response)
	
	return response["data"].(map[string]interface{})
}

func topupWallet(walletID, amount float64) {
	payload := map[string]interface{}{
		"amount":         amount,
		"payment_method": "mobile_money",
		"reference":      "LOAD_TEST_TOPUP",
	}
	
	jsonData, _ := json.Marshal(payload)
	client := &http.Client{Timeout: 10 * time.Second}
	req, _ := http.NewRequest("POST", fmt.Sprintf("%s/api/v1/internal/wallets/%.0f/topup", baseURL, walletID), bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Internal-Key", internalAPIKey)
	req.Header.Set("X-Service-Name", "load-test")

	resp, _ := client.Do(req)
	resp.Body.Close()
}

func analyzeResults(results chan RequestMetric, startTime time.Time) LoadTestResult {
	var totalRequests, successfulReqs, failedReqs int
	var totalDuration, maxLatency, minLatency time.Duration
	minLatency = time.Hour // Initialize with a large value

	for metric := range results {
		totalRequests++
		totalDuration += metric.Duration
		
		if metric.Success {
			successfulReqs++
		} else {
			failedReqs++
		}
		
		if metric.Duration > maxLatency {
			maxLatency = metric.Duration
		}
		if metric.Duration < minLatency {
			minLatency = metric.Duration
		}
	}

	totalTime := time.Since(startTime)
	averageLatency := totalDuration / time.Duration(totalRequests)
	requestsPerSec := float64(totalRequests) / totalTime.Seconds()
	errorRate := float64(failedReqs) / float64(totalRequests)

	return LoadTestResult{
		TotalRequests:  totalRequests,
		SuccessfulReqs: successfulReqs,
		FailedReqs:     failedReqs,
		AverageLatency: averageLatency,
		MaxLatency:     maxLatency,
		MinLatency:     minLatency,
		RequestsPerSec: requestsPerSec,
		ErrorRate:      errorRate,
	}
}

func printLoadTestResults(testName string, result LoadTestResult) {
	fmt.Printf("\n=== %s Results ===\n", testName)
	fmt.Printf("Total Requests: %d\n", result.TotalRequests)
	fmt.Printf("Successful: %d\n", result.SuccessfulReqs)
	fmt.Printf("Failed: %d\n", result.FailedReqs)
	fmt.Printf("Error Rate: %.2f%%\n", result.ErrorRate*100)
	fmt.Printf("Average Latency: %v\n", result.AverageLatency)
	fmt.Printf("Min Latency: %v\n", result.MinLatency)
	fmt.Printf("Max Latency: %v\n", result.MaxLatency)
	fmt.Printf("Requests/Second: %.2f\n", result.RequestsPerSec)
	fmt.Printf("================================\n\n")
}
