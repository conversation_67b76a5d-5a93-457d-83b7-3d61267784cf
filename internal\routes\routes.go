package routes

import (
	"fmt"
	"wallet-platform/internal/controllers"
	"wallet-platform/internal/middleware"
	"wallet-platform/internal/services"

	"github.com/gin-gonic/gin"
)

// SetupRoutes sets up all API routes
func SetupRoutes(router *gin.Engine, container *services.Container) {
	// Debug: Check if container is nil
	fmt.Printf("ROUTES DEBUG: SetupRoutes called, container is nil: %v\n", container == nil)
	if container == nil {
		fmt.Printf("ROUTES ERROR: Service container is nil!\n")
		panic("Service container is nil in SetupRoutes")
	}

	// Debug: Check container services
	fmt.Printf("ROUTES DEBUG: WalletService is nil: %v\n", container.WalletService == nil)
	fmt.Printf("ROUTES DEBUG: Logger is nil: %v\n", container.Logger == nil)

	// Initialize controllers
	fmt.Printf("ROUTES DEBUG: Creating WalletController\n")
	walletController := controllers.NewWalletController(container)
	if walletController == nil {
		fmt.Printf("ROUTES ERROR: WalletController creation returned nil\n")
		panic("WalletController creation failed")
	}
	fmt.Printf("ROUTES DEBUG: WalletController created successfully\n")

	fmt.Printf("ROUTES DEBUG: Creating other controllers\n")
	payCardController := controllers.NewPayCardController(container)
	webhookController := controllers.NewWebhookController(container)
	analyticsController := controllers.NewAnalyticsController(container)
	migrationController := controllers.NewMigrationController(container)
	authController := controllers.NewAuthController(container)
	securityController := controllers.NewSecurityController(container)
	serviceController := controllers.NewServiceController(container)
	adminController := controllers.NewAdminController(container)
	internalController := controllers.NewInternalController(container)
	externalWebhookController := controllers.NewExternalWebhookController(container)
	fmt.Printf("ROUTES DEBUG: All controllers created successfully\n")

	// Initialize validation middleware
	validationMiddleware := middleware.NewValidationMiddleware()

	// Apply global security middleware
	router.Use(validationMiddleware.ValidateRequestSize(10 * 1024 * 1024)) // 10MB limit
	router.Use(validationMiddleware.ValidateHeaders())
	router.Use(middleware.SecurityHeaders())

	// API version 1
	v1 := router.Group("/api/v1")
	{
		// Public routes (no authentication required)
		public := v1.Group("/")
		{
			public.GET("/health", healthCheck)
			public.POST("/auth/login", authController.Login)
			public.POST("/auth/register", authController.Register)
			public.POST("/auth/verify", authController.VerifyAccount)
			public.POST("/auth/forgot-password", authController.ForgotPassword)
			public.POST("/auth/reset-password", authController.ResetPassword)

			// Public webhook endpoint for payment engine
			public.POST("/webhooks/receive", webhookController.ReceiveWebhook)
		}

		// Protected routes (authentication required - supports both user auth and internal service auth)
		protected := v1.Group("/")
		protected.Use(middleware.InternalAuth())
		protected.Use(validationMiddleware.ValidateContentType("application/json"))
		protected.Use(validationMiddleware.SanitizeInput())
		{
			// Wallet routes
			walletRoutes := protected.Group("/wallets")
			{
				walletRoutes.POST("/", walletController.CreateWallet)
				walletRoutes.GET("/me", walletController.GetMyWallet)
				walletRoutes.GET("/:id", walletController.GetWallet)
				walletRoutes.PUT("/:id", walletController.UpdateWallet)
				walletRoutes.GET("/:id/balance", walletController.GetWalletBalance)
				walletRoutes.POST("/:id/topup", walletController.TopupWallet)
				walletRoutes.POST("/transfer", walletController.TransferFunds)
				walletRoutes.GET("/:id/transactions", walletController.GetWalletTransactions)
				walletRoutes.GET("/:id/analytics", analyticsController.GetWalletAnalytics)

				// Phone-based convenience endpoints
				walletRoutes.POST("/transfer/phone", walletController.TransferByPhone)
				walletRoutes.POST("/phone/:phone/topup", walletController.TopupWalletByPhone)
				walletRoutes.GET("/phone/:phone/balance", walletController.GetBalanceByPhone)
				walletRoutes.GET("/phone/:phone/transactions", walletController.GetTransactionHistoryByPhone)
			}

			// PayCard routes
			cardRoutes := protected.Group("/cards")
			{
				cardRoutes.POST("/", payCardController.CreateCard)
				cardRoutes.GET("/", payCardController.GetMyCards)
				cardRoutes.GET("/:id", payCardController.GetCard)
				cardRoutes.PUT("/:id", payCardController.UpdateCard)
				cardRoutes.POST("/:id/pin", payCardController.UpdateCardPIN)
				cardRoutes.POST("/:id/block", payCardController.BlockCard)
				cardRoutes.POST("/:id/unblock", payCardController.UnblockCard)
				cardRoutes.POST("/:id/transactions", payCardController.ProcessCardTransaction)
				cardRoutes.GET("/:id/transactions", payCardController.GetCardTransactions)

				// Enhanced PayCard features
				cardRoutes.POST("/:id/request-physical", payCardController.RequestPhysicalCard)
				cardRoutes.POST("/activate-physical", payCardController.ActivatePhysicalCard)
				cardRoutes.POST("/:id/generate-token", payCardController.GenerateCardToken)
				cardRoutes.POST("/:id/whitelist/add", payCardController.AddMerchantToWhitelist)
				cardRoutes.POST("/:id/controls/set", payCardController.SetSpendingControl)
				cardRoutes.POST("/contactless/pay", payCardController.ProcessContactlessPayment)

				// Enhanced card management with fraud prevention
				cardRoutes.GET("/wallet/:wallet_id/count", payCardController.GetCardCount)
				cardRoutes.GET("/wallet/:wallet_id/active", payCardController.GetActiveCardsByType)
				cardRoutes.PUT("/:id/limits", payCardController.SetSpendingLimits)
				cardRoutes.PUT("/:id/merchant-restrictions", payCardController.SetMerchantRestrictions)
				cardRoutes.PUT("/:id/time-restrictions", payCardController.SetTimeRestrictions)
				cardRoutes.GET("/:id/security-settings", payCardController.GetCardSecuritySettings)
				cardRoutes.GET("/:id/analytics", payCardController.GetCardUsageAnalytics)
				cardRoutes.POST("/:id/features/:feature/enable", payCardController.EnableCardFeature)
				cardRoutes.POST("/:id/features/:feature/disable", payCardController.DisableCardFeature)

				// QR Code payment endpoints
				cardRoutes.GET("/:id/qr-code", payCardController.GenerateQRCode)
				cardRoutes.POST("/qr-code/validate", payCardController.ValidateQRCode)
				cardRoutes.POST("/qr-code/payment", payCardController.ProcessQRCodePayment)
			}

			// Security routes
			securityRoutes := protected.Group("/security")
			{
				securityRoutes.POST("/devices", securityController.RegisterDevice)
				securityRoutes.POST("/devices/verify", securityController.VerifyDevice)
				securityRoutes.POST("/2fa/setup", securityController.Setup2FA)
				securityRoutes.POST("/2fa/verify", securityController.Verify2FA)
				securityRoutes.GET("/settings", securityController.GetSecuritySettings)
				securityRoutes.PUT("/settings", securityController.UpdateSecuritySettings)
				securityRoutes.GET("/events", securityController.GetSecurityEvents)
				securityRoutes.GET("/fraud-alerts", securityController.GetFraudAlerts)
			}

			// Service routes
			serviceRoutes := protected.Group("/services")
			{
				serviceRoutes.GET("/", serviceController.GetAvailableServices)
				serviceRoutes.POST("/subscribe", serviceController.SubscribeToService)
				serviceRoutes.GET("/subscriptions", serviceController.GetMySubscriptions)
				serviceRoutes.PUT("/subscriptions/:id", serviceController.UpdateSubscription)
				serviceRoutes.DELETE("/subscriptions/:id", serviceController.CancelSubscription)
				serviceRoutes.GET("/subscriptions/:id/usage", serviceController.GetServiceUsage)
				serviceRoutes.POST("/subscriptions/:id/usage", serviceController.RecordServiceUsage)
			}

			// Analytics routes
			analyticsRoutes := protected.Group("/analytics")
			{
				analyticsRoutes.GET("/dashboard", analyticsController.GetDashboardData)
				analyticsRoutes.GET("/realtime", analyticsController.GetRealTimeMetrics)
				analyticsRoutes.GET("/business", analyticsController.GetBusinessMetrics)
				analyticsRoutes.GET("/system", analyticsController.GetSystemAnalytics)
				analyticsRoutes.GET("/wallets/:wallet_id", analyticsController.GetWalletAnalytics)
				analyticsRoutes.GET("/cards/:card_id", analyticsController.GetCardAnalytics)
			}

			// Monitoring routes
			monitoringRoutes := protected.Group("/monitoring")
			{
				monitoringRoutes.GET("/health", analyticsController.GetSystemHealth)
				monitoringRoutes.GET("/performance", analyticsController.GetPerformanceMetrics)
				monitoringRoutes.POST("/alerts", analyticsController.CreateAlert)
			}

			// Migration routes (admin only)
			migrationRoutes := protected.Group("/migration")
			{
				migrationRoutes.POST("/start", migrationController.StartMigration)
				migrationRoutes.POST("/wallets", migrationController.MigrateWallets)
				migrationRoutes.POST("/cards", migrationController.MigratePayCards)
				migrationRoutes.POST("/transactions", migrationController.MigrateTransactions)
				migrationRoutes.GET("/status", migrationController.GetMigrationStatus)
				migrationRoutes.GET("/status/:type", migrationController.GetMigrationStatusByType)
				migrationRoutes.GET("/progress/:id", migrationController.GetMigrationProgress)
				migrationRoutes.GET("/summary", migrationController.GetMigrationSummary)
				migrationRoutes.POST("/validate", migrationController.ValidateMigration)
				migrationRoutes.POST("/rollback", migrationController.RollbackMigration)
			}

			// Webhook routes
			webhookRoutes := protected.Group("/webhooks")
			{
				webhookRoutes.POST("/register", webhookController.RegisterWebhookEndpoint)
				webhookRoutes.GET("/events", webhookController.GetWebhookEvents)
				webhookRoutes.POST("/retry", webhookController.RetryFailedWebhooks)
			}
		}

		// Platform wallet routes (admin only)
		platform := v1.Group("/platform")
		platform.Use(middleware.AdminAuth())
		platform.Use(validationMiddleware.ValidateContentType("application/json"))
		platform.Use(validationMiddleware.SanitizeInput())
		{
			platform.GET("/wallet", walletController.GetPlatformWallet)
		}

		// Admin routes (admin authentication required)
		admin := v1.Group("/admin")
		admin.Use(middleware.AdminAuth())
		admin.Use(validationMiddleware.ValidateContentType("application/json"))
		admin.Use(validationMiddleware.SanitizeInput())
		{
			// System management
			systemRoutes := admin.Group("/system")
			{
				systemRoutes.GET("/health", adminController.SystemHealthCheck)
				systemRoutes.GET("/metrics", adminController.GetSystemMetrics)
				systemRoutes.GET("/analytics", adminController.GetSystemAnalytics)
				systemRoutes.POST("/maintenance", adminController.EnableMaintenanceMode)
				systemRoutes.DELETE("/maintenance", adminController.DisableMaintenanceMode)
			}

			// User management
			userRoutes := admin.Group("/users")
			{
				userRoutes.GET("/", adminController.GetAllUsers)
				userRoutes.GET("/:id", adminController.GetUser)
				userRoutes.PUT("/:id", adminController.UpdateUser)
				userRoutes.DELETE("/:id", adminController.DeleteUser)
				userRoutes.POST("/:id/suspend", adminController.SuspendUser)
				userRoutes.POST("/:id/unsuspend", adminController.UnsuspendUser)
			}

			// Wallet management
			adminWalletRoutes := admin.Group("/wallets")
			{
				adminWalletRoutes.GET("/", adminController.GetAllWallets)
				adminWalletRoutes.GET("/:id", adminController.GetWalletDetails)
				adminWalletRoutes.PUT("/:id", adminController.AdminUpdateWallet)
				adminWalletRoutes.POST("/:id/freeze", adminController.FreezeWallet)
				adminWalletRoutes.POST("/:id/unfreeze", adminController.UnfreezeWallet)
				adminWalletRoutes.GET("/:id/audit", adminController.GetWalletAudit)
			}

			// Card management
			adminCardRoutes := admin.Group("/cards")
			{
				adminCardRoutes.GET("/", adminController.GetAllCards)
				adminCardRoutes.GET("/:id", adminController.GetCardDetails)
				adminCardRoutes.PUT("/:id", adminController.AdminUpdateCard)
				adminCardRoutes.POST("/:id/force-block", adminController.ForceBlockCard)
				adminCardRoutes.GET("/:id/audit", adminController.GetCardAudit)
			}

			// Security management
			adminSecurityRoutes := admin.Group("/security")
			{
				adminSecurityRoutes.GET("/events", adminController.GetAllSecurityEvents)
				adminSecurityRoutes.GET("/fraud-alerts", adminController.GetAllFraudAlerts)
				adminSecurityRoutes.PUT("/fraud-alerts/:id", adminController.UpdateFraudAlert)
				adminSecurityRoutes.GET("/risk-profiles", adminController.GetRiskProfiles)
				adminSecurityRoutes.PUT("/risk-profiles/:id", adminController.UpdateRiskProfile)
			}

			// Service management
			adminServiceRoutes := admin.Group("/services")
			{
				adminServiceRoutes.GET("/", adminController.GetAllServices)
				adminServiceRoutes.POST("/", adminController.CreateService)
				adminServiceRoutes.PUT("/:id", adminController.UpdateService)
				adminServiceRoutes.DELETE("/:id", adminController.DeleteService)
				adminServiceRoutes.GET("/subscriptions", adminController.GetAllSubscriptions)
				adminServiceRoutes.GET("/billing", adminController.GetServiceBilling)
			}

			// Analytics and reporting
			adminAnalyticsRoutes := admin.Group("/analytics")
			{
				adminAnalyticsRoutes.GET("/system", adminController.GetSystemAnalytics)
				adminAnalyticsRoutes.GET("/revenue", adminController.GetRevenueAnalytics)
				adminAnalyticsRoutes.GET("/merchants", adminController.GetMerchantAnalytics)
				adminAnalyticsRoutes.GET("/reports", adminController.GetAllReports)
				adminAnalyticsRoutes.POST("/reports/generate", adminController.GenerateAdminReport)
			}
		}

		// Internal API routes (for service-to-service communication)
		// These routes require X-Internal-Key header for authentication
		internal := v1.Group("/internal")
		internal.Use(middleware.InternalAuth())
		internal.Use(validationMiddleware.ValidateContentType("application/json"))
		// Note: SanitizeInput middleware removed to prevent body consumption conflicts
		{
			// Internal wallet operations
			fmt.Printf("ROUTES DEBUG: Setting up internal wallet routes\n")
			internalWalletRoutes := internal.Group("/wallets")
			{
				fmt.Printf("ROUTES DEBUG: Registering POST / route with walletController.CreateWallet\n")
				if walletController == nil {
					fmt.Printf("ROUTES ERROR: walletController is nil when registering routes!\n")
					panic("walletController is nil during route registration")
				}

				// Test if the method can be accessed
				if walletController.CreateWallet == nil {
					fmt.Printf("ROUTES ERROR: walletController.CreateWallet method is nil!\n")
					panic("walletController.CreateWallet method is nil")
				}
				fmt.Printf("ROUTES DEBUG: walletController.CreateWallet method is accessible\n")

				internalWalletRoutes.POST("/", walletController.CreateWallet)
				internalWalletRoutes.GET("/:id", walletController.GetWallet)
				internalWalletRoutes.GET("/phone/:phone", internalController.GetWalletByPhone)
				internalWalletRoutes.PUT("/:id", walletController.UpdateWallet)
				internalWalletRoutes.GET("/:id/balance", walletController.GetWalletBalance)
				internalWalletRoutes.POST("/:id/topup", walletController.TopupWallet)
				internalWalletRoutes.POST("/transfer", walletController.TransferFunds)
				internalWalletRoutes.GET("/:id/transactions", walletController.GetWalletTransactions)
				internalWalletRoutes.POST("/fund", walletController.FundOrUnfundWallet)

				// Phone-based internal endpoints
				internalWalletRoutes.POST("/transfer/phone", walletController.TransferByPhone)
				internalWalletRoutes.POST("/phone/:phone/topup", walletController.TopupWalletByPhone)
				internalWalletRoutes.GET("/phone/:phone/balance", walletController.GetBalanceByPhone)
				internalWalletRoutes.GET("/phone/:phone/transactions", walletController.GetTransactionHistoryByPhone)

				// Missing documented endpoints
				internalWalletRoutes.POST("/:id/withdraw", walletController.Withdraw)
				internalWalletRoutes.POST("/batch-lookup", internalController.BulkLookup)
			}

			// Internal PayCard operations
			internalCardRoutes := internal.Group("/cards")
			{
				internalCardRoutes.POST("/", payCardController.CreateCard)
				internalCardRoutes.GET("/:id", payCardController.GetCard)
				internalCardRoutes.GET("/wallet/:wallet_id", internalController.GetCardsByWallet)
				internalCardRoutes.PUT("/:id", payCardController.UpdateCard)
				internalCardRoutes.POST("/:id/pin", payCardController.UpdateCardPIN)
				internalCardRoutes.POST("/:id/block", payCardController.BlockCard)
				internalCardRoutes.POST("/:id/unblock", payCardController.UnblockCard)
				internalCardRoutes.POST("/:id/transactions", payCardController.ProcessCardTransaction)
				internalCardRoutes.GET("/:id/transactions", payCardController.GetCardTransactions)
				internalCardRoutes.POST("/contactless/pay", payCardController.ProcessContactlessPayment)

				// Enhanced card management for internal services
				internalCardRoutes.GET("/wallet/:wallet_id/count", payCardController.GetCardCount)
				internalCardRoutes.GET("/wallet/:wallet_id/active", payCardController.GetActiveCardsByType)
				internalCardRoutes.PUT("/:id/limits", payCardController.SetSpendingLimits)
				internalCardRoutes.PUT("/:id/merchant-restrictions", payCardController.SetMerchantRestrictions)
				internalCardRoutes.PUT("/:id/time-restrictions", payCardController.SetTimeRestrictions)
				internalCardRoutes.GET("/:id/security-settings", payCardController.GetCardSecuritySettings)
				internalCardRoutes.GET("/:id/analytics", payCardController.GetCardUsageAnalytics)
				internalCardRoutes.POST("/:id/features/:feature/enable", payCardController.EnableCardFeature)
				internalCardRoutes.POST("/:id/features/:feature/disable", payCardController.DisableCardFeature)

				// QR Code payment endpoints for internal services
				internalCardRoutes.GET("/:id/qr-code", payCardController.GenerateQRCode)
				internalCardRoutes.POST("/qr-code/validate", payCardController.ValidateQRCode)
				internalCardRoutes.POST("/qr-code/payment", payCardController.ProcessQRCodePayment)
			}

			// Internal analytics and monitoring
			internalAnalyticsRoutes := internal.Group("/analytics")
			{
				internalAnalyticsRoutes.GET("/wallets/:wallet_id", analyticsController.GetWalletAnalytics)
				internalAnalyticsRoutes.GET("/cards/:card_id", analyticsController.GetCardAnalytics)
				internalAnalyticsRoutes.GET("/system", analyticsController.GetSystemAnalytics)
			}

			// Internal webhook management
			internalWebhookRoutes := internal.Group("/webhooks")
			{
				internalWebhookRoutes.POST("/send", internalController.SendInternalWebhook)
				internalWebhookRoutes.GET("/events", webhookController.GetWebhookEvents)
			}

			// Internal platform operations
			internalPlatformRoutes := internal.Group("/platform")
			{
				internalPlatformRoutes.GET("/wallet", walletController.GetPlatformWallet)
				internalPlatformRoutes.POST("/collect-fee", internalController.CollectPlatformFee)
			}

			// Internal subscription operations
			internalSubscriptionRoutes := internal.Group("/subscriptions")
			{
				internalSubscriptionRoutes.POST("/", internalController.CreateSubscription)
				internalSubscriptionRoutes.GET("/wallet/:wallet_id", internalController.GetWalletSubscriptions)
				internalSubscriptionRoutes.POST("/:id/cancel", internalController.CancelSubscription)
			}

			// Internal system operations
			internalSystemRoutes := internal.Group("/")
			{
				internalSystemRoutes.GET("/health", internalController.HealthCheck)
				internalSystemRoutes.POST("/auth/validate-key", internalController.ValidateAPIKey)
				internalSystemRoutes.GET("/service/info", internalController.GetServiceInfo)
			}
		}

		// Webhook routes (for external integrations)
		webhookRoutes := v1.Group("/webhooks")
		{
			webhookRoutes.POST("/payment-engine", externalWebhookController.PaymentEngineWebhook)
			webhookRoutes.POST("/sms-provider", externalWebhookController.SMSProviderWebhook)
			webhookRoutes.POST("/email-provider", externalWebhookController.EmailProviderWebhook)
			webhookRoutes.POST("/fraud-detection", externalWebhookController.FraudDetectionWebhook)
		}
	}

	// Swagger documentation
	router.GET("/swagger/*any", gin.WrapH(nil)) // TODO: Implement Swagger handler
}

// healthCheck provides a simple health check endpoint
func healthCheck(c *gin.Context) {
	c.JSON(200, gin.H{
		"status":    "healthy",
		"service":   "wallet-platform",
		"version":   "1.0.0",
		"timestamp": "2024-01-01T00:00:00Z",
	})
}
