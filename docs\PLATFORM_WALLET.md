# Platform Wallet System

## Overview

The platform wallet is a special system wallet used to collect service charges, card fees, and other platform revenue. This wallet is automatically created and managed by the system to ensure proper fee collection and revenue tracking.

## Features

### **Automatic Platform Wallet Creation**
- **Phone Number**: `+268PLATFORM` (unique identifier)
- **Wallet Type**: `platform`
- **Status**: Always `active`
- **High Limits**: Configured for large-scale platform operations
- **Auto-Creation**: Created automatically when first accessed

### **Fee Collection System**
- **Card Creation Fees**: Automatically collected when new cards are created
- **Withdrawal Fees**: Collected during withdrawal operations
- **Service Charges**: Can be collected for various platform services
- **Transaction Safety**: All fee collections use database transactions

## Implementation Details

### **Platform Wallet Constants**
```go
const (
    PlatformWalletPhone = "+268PLATFORM"
    PlatformWalletType  = "platform"
)
```

### **Core Methods**

#### **EnsurePlatformWallet()**
- Creates platform wallet if it doesn't exist
- Returns existing platform wallet if already created
- Handles database errors gracefully
- Logs platform wallet creation events

#### **CollectPlatformFee()**
- Transfers fees from user wallets to platform wallet
- Uses database transactions for atomicity
- Creates proper transaction records for both wallets
- Validates sufficient balance before collection
- Comprehensive error handling and logging

### **Fee Structure**

#### **Configurable Fees**
All platform fees are configurable via environment variables and configuration files:

#### **Card Creation Fees**
- **Standard Card**: Configurable via `CARD_CREATION_FEE_STANDARD` (default: 25 SZL)
- **Premium Card**: Configurable via `CARD_CREATION_FEE_PREMIUM` (default: 50 SZL)
- **Business Card**: Configurable via `CARD_CREATION_FEE_BUSINESS` (default: 100 SZL)

#### **Transaction Fees**
- **Withdrawal Fee**: Configurable via `WITHDRAWAL_FEE_PERCENTAGE` (default: 1%)
- **Transaction Fee**: Configurable via `TRANSACTION_FEE_PERCENTAGE` (default: 0.5%)
- **International Transaction Fee**: Configurable via `INTERNATIONAL_TRANSACTION_FEE_PERCENTAGE` (default: 2.5%)

#### **Other Fees**
- **Monthly Maintenance**: Configurable via `MONTHLY_MAINTENANCE_FEE` (default: 10 SZL)
- **Card Replacement**: Configurable via `CARD_REPLACEMENT_FEE` (default: 15 SZL)
- **ATM Withdrawal**: Configurable via `ATM_WITHDRAWAL_FEE` (default: 5 SZL)

## Integration Points

### **Wallet Service Integration**
```go
// Service interface methods
EnsurePlatformWallet() (*models.Wallet, error)
GetPlatformWallet() (*models.Wallet, error)
CollectPlatformFee(fromWalletID uint, amount float64, feeType, description string) error
```

### **PayCard Service Integration**
- Card creation automatically triggers fee collection
- Fee collection failure doesn't prevent card creation
- Comprehensive logging for fee collection issues

### **Withdrawal Integration**
- Withdrawal fees automatically collected to platform wallet
- Fee collection happens after successful external payment
- Separate from main withdrawal transaction for clarity

## API Endpoints

### **Get Platform Wallet**
```http
GET /api/v1/platform/wallet
Authorization: X-Admin-Key: your_admin_key
```

**Response:**
```json
{
  "success": true,
  "message": "Platform wallet retrieved successfully",
  "data": {
    "id": 1,
    "phone_number": "+268PLATFORM",
    "wallet_type": "platform",
    "status": "active",
    "balance": 15750.00,
    "currency": "SZL",
    "daily_limit": 1000000.00,
    "monthly_limit": 30000000.00,
    "daily_spent": 0.00,
    "monthly_spent": 0.00,
    "is_verified": true,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

## Transaction Categories

### **Fee Collection Transactions**
- **Source Wallet**: `category: "fee"`, `type: "debit"`
- **Platform Wallet**: `category: "fee_collection"`, `type: "credit"`
- **Payment Method**: `"platform_fee"`
- **Reference**: Shared UUID for transaction linking

### **Fee Types**
- `"card_creation_fee"` - Card creation charges
- `"withdrawal_fee"` - Withdrawal processing fees
- `"service_fee"` - General service charges
- `"transaction_fee"` - Transaction processing fees

## Error Handling

### **Fee Collection Failures**
- **Card Creation**: Fee failure doesn't prevent card creation
- **Withdrawals**: Fee failure logged but doesn't affect withdrawal
- **Logging**: All failures logged with `"warning": true` or `"critical": true`
- **Reconciliation**: Failed fee collections can be retried manually

### **Platform Wallet Issues**
- **Creation Failure**: Logged with detailed error context
- **Access Failure**: Returns appropriate error responses
- **Balance Issues**: Comprehensive validation and error messages

## Security Features

### **Access Control**
- **Admin Only**: Platform wallet access restricted to admin users
- **API Key Authentication**: Requires valid admin API key
- **Audit Logging**: All platform wallet operations logged

### **Transaction Integrity**
- **Database Transactions**: All fee collections use database transactions
- **Rollback Protection**: Failed operations automatically rolled back
- **Balance Validation**: Ensures sufficient balance before fee collection

## Monitoring & Analytics

### **Key Metrics**
- **Total Platform Revenue**: Platform wallet balance
- **Fee Collection Rate**: Success rate of fee collections
- **Revenue by Fee Type**: Breakdown of different fee categories
- **Failed Collections**: Monitoring for reconciliation needs

### **Logging Events**
- Platform wallet creation
- Successful fee collections
- Failed fee collection attempts
- Platform wallet access events
- Balance updates and transactions

## Deployment Considerations

### **Database Migration**
- Platform wallet created automatically on first access
- No manual database setup required
- Existing systems will auto-create platform wallet

### **Configuration**
- **Fee Configuration**: All fees configurable via environment variables and YAML config files
- **Environment-Specific Fees**: Different fee structures for development, staging, and production
- **Runtime Updates**: Fee changes require application restart
- **High Transaction Limits**: Pre-configured for platform operations
- **Currency**: Set to SZL (Swazi Lilangeni)

### **Environment Variables**
```bash
# Card Creation Fees
CARD_CREATION_FEE_STANDARD=25.00
CARD_CREATION_FEE_PREMIUM=50.00
CARD_CREATION_FEE_BUSINESS=100.00

# Transaction Fees (percentages)
WITHDRAWAL_FEE_PERCENTAGE=1.0
TRANSACTION_FEE_PERCENTAGE=0.5
INTERNATIONAL_TRANSACTION_FEE_PERCENTAGE=2.5

# Fixed Fees
MONTHLY_MAINTENANCE_FEE=10.00
CARD_REPLACEMENT_FEE=15.00
ATM_WITHDRAWAL_FEE=5.00
```

### **Backup & Recovery**
- Platform wallet included in standard wallet backups
- Transaction history preserved for audit purposes
- Balance reconciliation procedures documented

## Future Enhancements

### **Planned Features**
1. **Configurable Fee Rates** - Admin interface for fee management
2. **Revenue Analytics** - Detailed revenue reporting dashboard
3. **Fee Scheduling** - Time-based fee adjustments
4. **Multi-Currency Support** - Platform wallets for different currencies
5. **Automated Reconciliation** - System for handling failed fee collections

### **Integration Opportunities**
1. **External Accounting** - Integration with accounting systems
2. **Tax Reporting** - Automated tax calculation and reporting
3. **Revenue Sharing** - Partner revenue distribution
4. **Compliance Reporting** - Regulatory compliance automation

## Best Practices

### **Fee Collection**
- Always validate balance before collection
- Use descriptive fee descriptions
- Log all fee collection attempts
- Handle failures gracefully

### **Platform Wallet Management**
- Monitor platform wallet balance regularly
- Set up alerts for unusual activity
- Regular reconciliation of fee collections
- Backup transaction history frequently

### **Security**
- Restrict platform wallet access to authorized personnel
- Regular audit of platform wallet transactions
- Monitor for unauthorized access attempts
- Implement proper logging and alerting
