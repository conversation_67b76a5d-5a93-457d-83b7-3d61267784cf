package models

import (
	"time"

	"gorm.io/datatypes"
)

// PayCard represents a QR-code-based debit card linked to a wallet
type PayCard struct {
	ID             uint   `json:"id" gorm:"primaryKey"`
	CardNumber     string `json:"card_number" gorm:"type:varchar(20);uniqueIndex;not null"` // QR code data
	WalletID       uint   `json:"wallet_id" gorm:"index;not null"`
	Wallet         Wallet `json:"wallet" gorm:"foreignKey:WalletID;constraint:OnDelete:RESTRICT"`
	CardHolderName string `json:"card_holder_name" gorm:"type:varchar(100);not null"`
	CardType       string `json:"card_type" gorm:"type:varchar(20);not null"`               // standard, premium, business
	Status         string `json:"status" gorm:"type:varchar(20);not null;default:'active'"` // active, suspended, expired, cancelled

	// Spending limits and controls
	SpendingLimit        float64    `json:"spending_limit" gorm:"type:decimal(15,2);not null;default:0"`         // 0 = no limit
	DailySpendingLimit   float64    `json:"daily_spending_limit" gorm:"type:decimal(15,2);not null;default:0"`   // 0 = no limit
	MonthlySpendingLimit float64    `json:"monthly_spending_limit" gorm:"type:decimal(15,2);not null;default:0"` // 0 = no limit
	CurrentDailySpent    float64    `json:"current_daily_spent" gorm:"type:decimal(15,2);not null;default:0"`
	CurrentMonthlySpent  float64    `json:"current_monthly_spent" gorm:"type:decimal(15,2);not null;default:0"`
	LastSpendingReset    *time.Time `json:"last_spending_reset"`

	// Subscription and billing
	SubscriptionPlanID *uint        `json:"subscription_plan_id"`
	SubscriptionPlan   *PayCardPlan `json:"subscription_plan" gorm:"foreignKey:SubscriptionPlanID"`
	NextBillingDate    *time.Time   `json:"next_billing_date"`
	LastBillingDate    *time.Time   `json:"last_billing_date"`
	BillingStatus      string       `json:"billing_status" gorm:"type:varchar(20);not null;default:'active'"` // active, overdue, cancelled

	// Security and authentication
	QRCodeData           string     `json:"qr_code_data" gorm:"type:text"`                  // Encrypted QR code data
	SecurityPin          string     `json:"security_pin" gorm:"type:varchar(255);not null"` // Hashed PIN
	IsPinSet             bool       `json:"is_pin_set" gorm:"not null;default:false"`
	FailedPinAttempts    int        `json:"failed_pin_attempts" gorm:"not null;default:0"`
	LastFailedPinAttempt *time.Time `json:"last_failed_pin_attempt"`
	CardLockedUntil      *time.Time `json:"card_locked_until"`

	// Restrictions and controls
	MerchantRestrictions   datatypes.JSON `json:"merchant_restrictions"`   // JSON array of restricted merchant categories
	GeographicRestrictions datatypes.JSON `json:"geographic_restrictions"` // JSON array of restricted countries/regions
	TimeRestrictions       datatypes.JSON `json:"time_restrictions"`       // JSON object for time-based restrictions

	// Physical card fields
	PhysicalCardIssued    bool       `json:"physical_card_issued" gorm:"not null;default:false"`
	PhysicalCardSerial    string     `json:"physical_card_serial" gorm:"type:varchar(50);uniqueIndex"`
	PhysicalCardBatch     string     `json:"physical_card_batch" gorm:"type:varchar(50)"`
	PhysicalCardPrintedAt *time.Time `json:"physical_card_printed_at"`
	PhysicalCardStatus    string     `json:"physical_card_status" gorm:"type:varchar(20);not null;default:'pending'"` // pending, printed, shipped, delivered, activated, lost, blocked, destroyed
	PhysicalCardDesign    string     `json:"physical_card_design" gorm:"type:varchar(100)"`
	PhysicalCardNotes     string     `json:"physical_card_notes" gorm:"type:text"`

	// Activation code fields
	ActivationCode        string     `json:"activation_code" gorm:"type:varchar(10)"` // 6-8 digit activation code
	ActivationCodeExpires *time.Time `json:"activation_code_expires"`
	ActivationAttempts    int        `json:"activation_attempts" gorm:"not null;default:0"`
	LastActivationAttempt *time.Time `json:"last_activation_attempt"`
	ActivationLockedUntil *time.Time `json:"activation_locked_until"`

	// Enhanced features
	ContactlessEnabled    bool `json:"contactless_enabled" gorm:"default:true"`
	OnlinePaymentsEnabled bool `json:"online_payments_enabled" gorm:"default:true"`
	ATMWithdrawalsEnabled bool `json:"atm_withdrawals_enabled" gorm:"default:false"`
	InternationalEnabled  bool `json:"international_enabled" gorm:"default:false"`

	// Metadata and settings
	Settings datatypes.JSON `json:"settings"` // JSON object for card settings
	Metadata datatypes.JSON `json:"metadata"` // Additional card metadata

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	ExpiresAt time.Time `json:"expires_at"`
}

// PayCardPlan represents subscription plans for pay cards
type PayCardPlan struct {
	ID               uint           `json:"id" gorm:"primaryKey"`
	Name             string         `json:"name" gorm:"type:varchar(50);not null;uniqueIndex"` // Standard, Premium, Business
	Description      string         `json:"description" gorm:"type:varchar(500)"`
	AnnualFee        float64        `json:"annual_fee" gorm:"type:decimal(15,2);not null"`
	MonthlyFee       float64        `json:"monthly_fee" gorm:"type:decimal(15,2);not null"`
	BillingCycle     string         `json:"billing_cycle" gorm:"type:varchar(20);not null"`                  // monthly, yearly
	Features         datatypes.JSON `json:"features"`                                                        // JSON array of features
	MaxSpendingLimit float64        `json:"max_spending_limit" gorm:"type:decimal(15,2);not null;default:0"` // 0 = unlimited
	MaxDailyLimit    float64        `json:"max_daily_limit" gorm:"type:decimal(15,2);not null;default:0"`    // 0 = unlimited
	MaxMonthlyLimit  float64        `json:"max_monthly_limit" gorm:"type:decimal(15,2);not null;default:0"`  // 0 = unlimited

	// Enhanced plan features
	PhysicalCardIncluded bool    `json:"physical_card_included" gorm:"default:false"`
	InternationalAccess  bool    `json:"international_access" gorm:"default:false"`
	ATMWithdrawals       bool    `json:"atm_withdrawals" gorm:"default:false"`
	PrioritySupport      bool    `json:"priority_support" gorm:"default:false"`
	CashbackPercentage   float64 `json:"cashback_percentage" gorm:"type:decimal(5,2);default:0"`

	IsActive  bool      `json:"is_active" gorm:"not null;default:true"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// PayCardTransaction represents transactions made with the pay card
type PayCardTransaction struct {
	ID            uint    `json:"id" gorm:"primaryKey"`
	CardID        uint    `json:"card_id" gorm:"index;not null"`
	Card          PayCard `json:"card" gorm:"foreignKey:CardID;constraint:OnDelete:RESTRICT"`
	TransactionID string  `json:"transaction_id" gorm:"type:varchar(50);uniqueIndex;not null"`
	Amount        float64 `json:"amount" gorm:"type:decimal(15,2);not null"`
	Currency      string  `json:"currency" gorm:"type:varchar(10);not null;default:'SZL'"`

	// Merchant information
	MerchantName     string `json:"merchant_name" gorm:"type:varchar(100)"`
	MerchantCategory string `json:"merchant_category" gorm:"type:varchar(50)"`
	MerchantLocation string `json:"merchant_location" gorm:"type:varchar(200)"`
	MerchantID       string `json:"merchant_id" gorm:"type:varchar(50)"`

	// Transaction details
	TransactionType string `json:"transaction_type" gorm:"type:varchar(20);not null"`         // purchase, withdrawal, refund, reversal
	PaymentMethod   string `json:"payment_method" gorm:"type:varchar(20);default:'qr_code'"`  // qr_code, contactless, chip_pin, online
	Status          string `json:"status" gorm:"type:varchar(20);not null;default:'pending'"` // pending, completed, failed, declined, reversed
	Reference       string `json:"reference" gorm:"type:varchar(100);uniqueIndex"`
	QRCodeScanned   string `json:"qr_code_scanned" gorm:"type:varchar(20)"` // The QR code that was scanned

	// Security and fraud detection
	DeviceInfo        datatypes.JSON `json:"device_info"`   // Device information when transaction was made
	LocationInfo      datatypes.JSON `json:"location_info"` // GPS coordinates if available
	IPAddress         string         `json:"ip_address" gorm:"type:varchar(45)"`
	DeviceFingerprint string         `json:"device_fingerprint" gorm:"type:varchar(64)"`
	RiskScore         float64        `json:"risk_score" gorm:"type:decimal(5,2);default:0"`
	FraudFlags        datatypes.JSON `json:"fraud_flags"` // Array of fraud detection flags

	// Processing details
	FailureReason     string `json:"failure_reason" gorm:"type:varchar(255)"`
	ProcessorResponse string `json:"processor_response" gorm:"type:varchar(100)"`
	AuthorizationCode string `json:"authorization_code" gorm:"type:varchar(20)"`

	// Linked transactions
	WalletTransactionID *uint               `json:"wallet_transaction_id"`
	WalletTransaction   *WalletTransaction  `json:"wallet_transaction" gorm:"foreignKey:WalletTransactionID"`
	ParentTransactionID *uint               `json:"parent_transaction_id"` // For refunds/reversals
	ParentTransaction   *PayCardTransaction `json:"parent_transaction" gorm:"foreignKey:ParentTransactionID"`

	// Enhanced features
	CashbackAmount float64 `json:"cashback_amount" gorm:"type:decimal(15,2);default:0"`
	RewardsPoints  int     `json:"rewards_points" gorm:"default:0"`

	// Metadata
	Metadata datatypes.JSON `json:"metadata"` // Additional transaction metadata

	ProcessedAt *time.Time `json:"processed_at"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
}

// PayCardToken represents tokenized card data for secure storage
type PayCardToken struct {
	ID         uint      `json:"id" gorm:"primaryKey"`
	CardID     uint      `json:"card_id" gorm:"index;not null"`
	Card       PayCard   `json:"card" gorm:"foreignKey:CardID;constraint:OnDelete:CASCADE"`
	TokenValue string    `json:"token_value" gorm:"type:varchar(64);uniqueIndex;not null"`
	TokenType  string    `json:"token_type" gorm:"type:varchar(20);not null"` // payment, api, temporary
	ExpiresAt  time.Time `json:"expires_at"`
	IsActive   bool      `json:"is_active" gorm:"default:true"`
	UsageCount int       `json:"usage_count" gorm:"default:0"`
	MaxUsage   int       `json:"max_usage" gorm:"default:0"`     // 0 = unlimited
	Scope      string    `json:"scope" gorm:"type:varchar(100)"` // permissions/scope for the token
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
}

// PayCardRequest represents a request to create a new pay card
type PayCardRequest struct {
	WalletID               uint     `json:"wallet_id" binding:"required"`
	CardHolderName         string   `json:"card_holder_name" binding:"required"`
	CardType               string   `json:"card_type" binding:"required,oneof=standard premium business"`
	SpendingLimit          float64  `json:"spending_limit" binding:"min=0"`
	DailySpendingLimit     float64  `json:"daily_spending_limit" binding:"min=0"`
	MonthlySpendingLimit   float64  `json:"monthly_spending_limit" binding:"min=0"`
	SecurityPin            string   `json:"security_pin" binding:"required,min=4,max=6"`
	MerchantRestrictions   []string `json:"merchant_restrictions"`
	GeographicRestrictions []string `json:"geographic_restrictions"`
	SubscriptionPlanID     *uint    `json:"subscription_plan_id"`
}

// PayCardUpdateRequest represents a request to update pay card settings
type PayCardUpdateRequest struct {
	CardHolderName         *string  `json:"card_holder_name"`
	SpendingLimit          *float64 `json:"spending_limit" binding:"omitempty,min=0"`
	DailySpendingLimit     *float64 `json:"daily_spending_limit" binding:"omitempty,min=0"`
	MonthlySpendingLimit   *float64 `json:"monthly_spending_limit" binding:"omitempty,min=0"`
	Status                 *string  `json:"status" binding:"omitempty,oneof=active suspended cancelled"`
	MerchantRestrictions   []string `json:"merchant_restrictions"`
	GeographicRestrictions []string `json:"geographic_restrictions"`
	ContactlessEnabled     *bool    `json:"contactless_enabled"`
	OnlinePaymentsEnabled  *bool    `json:"online_payments_enabled"`
	ATMWithdrawalsEnabled  *bool    `json:"atm_withdrawals_enabled"`
	InternationalEnabled   *bool    `json:"international_enabled"`
}

// PayCardPinUpdateRequest represents a request to update the PIN
type PayCardPinUpdateRequest struct {
	CurrentPin string `json:"current_pin" binding:"required,min=4,max=6"`
	NewPin     string `json:"new_pin" binding:"required,min=4,max=6"`
}

// PayCardTransactionRequest represents a request to process a card transaction
type PayCardTransactionRequest struct {
	CardNumber       string                 `json:"card_number" binding:"required"`
	Amount           float64                `json:"amount" binding:"required,gt=0"`
	MerchantName     string                 `json:"merchant_name"`
	MerchantCategory string                 `json:"merchant_category"`
	MerchantLocation string                 `json:"merchant_location"`
	MerchantID       string                 `json:"merchant_id"`
	Reference        string                 `json:"reference" binding:"required"`
	PaymentMethod    string                 `json:"payment_method" binding:"omitempty,oneof=qr_code contactless chip_pin online"`
	DeviceInfo       map[string]interface{} `json:"device_info"`
	LocationInfo     map[string]interface{} `json:"location_info"`
	Pin              string                 `json:"pin"`
}

// PayCardResponse represents the response format for PayCard operations
type PayCardResponse struct {
	ID                    uint      `json:"id"`
	CardNumber            string    `json:"card_number"`
	WalletID              uint      `json:"wallet_id"`
	CardHolderName        string    `json:"card_holder_name"`
	CardType              string    `json:"card_type"`
	Status                string    `json:"status"`
	SpendingLimit         float64   `json:"spending_limit"`
	DailySpendingLimit    float64   `json:"daily_spending_limit"`
	MonthlySpendingLimit  float64   `json:"monthly_spending_limit"`
	CurrentDailySpent     float64   `json:"current_daily_spent"`
	CurrentMonthlySpent   float64   `json:"current_monthly_spent"`
	IsPinSet              bool      `json:"is_pin_set"`
	PhysicalCardIssued    bool      `json:"physical_card_issued"`
	PhysicalCardStatus    string    `json:"physical_card_status"`
	ContactlessEnabled    bool      `json:"contactless_enabled"`
	OnlinePaymentsEnabled bool      `json:"online_payments_enabled"`
	ATMWithdrawalsEnabled bool      `json:"atm_withdrawals_enabled"`
	InternationalEnabled  bool      `json:"international_enabled"`
	CreatedAt             time.Time `json:"created_at"`
	UpdatedAt             time.Time `json:"updated_at"`
	ExpiresAt             time.Time `json:"expires_at"`
}

// PayCardTransactionResponse represents the response format for transaction operations
type PayCardTransactionResponse struct {
	ID                uint       `json:"id"`
	TransactionID     string     `json:"transaction_id"`
	CardID            uint       `json:"card_id"`
	Amount            float64    `json:"amount"`
	Currency          string     `json:"currency"`
	MerchantName      string     `json:"merchant_name"`
	MerchantCategory  string     `json:"merchant_category"`
	TransactionType   string     `json:"transaction_type"`
	PaymentMethod     string     `json:"payment_method"`
	Status            string     `json:"status"`
	Reference         string     `json:"reference"`
	AuthorizationCode string     `json:"authorization_code"`
	CashbackAmount    float64    `json:"cashback_amount"`
	RewardsPoints     int        `json:"rewards_points"`
	ProcessedAt       *time.Time `json:"processed_at"`
	CreatedAt         time.Time  `json:"created_at"`
}
