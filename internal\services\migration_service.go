package services

import (
	"context"
	"fmt"
	"time"

	"wallet-platform/internal/models"
	"wallet-platform/pkg/logger"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// MigrationService handles data migration from the main payment engine
type MigrationService struct {
	sourceDB *gorm.DB // Main payment engine database
	targetDB *gorm.DB // Standalone wallet platform database
	logger   *logger.Logger
}

// NewMigrationService creates a new migration service
func NewMigrationService(sourceDB, targetDB *gorm.DB, log *logger.Logger) *MigrationService {
	return &MigrationService{
		sourceDB: sourceDB,
		targetDB: targetDB,
		logger:   log,
	}
}

// MigrationStatus represents the status of a migration operation
type MigrationStatus struct {
	ID               uint           `json:"id" gorm:"primaryKey"`
	MigrationType    string         `json:"migration_type" gorm:"type:varchar(50);not null"`
	Status           string         `json:"status" gorm:"type:varchar(20);not null"` // pending, running, completed, failed
	StartTime        time.Time      `json:"start_time"`
	EndTime          *time.Time     `json:"end_time"`
	RecordsTotal     int            `json:"records_total"`
	RecordsProcessed int            `json:"records_processed"`
	RecordsFailed    int            `json:"records_failed"`
	ErrorMessage     string         `json:"error_message" gorm:"type:text"`
	Metadata         datatypes.JSON `json:"metadata"`
	CreatedAt        time.Time      `json:"created_at"`
	UpdatedAt        time.Time      `json:"updated_at"`
}

// MigrationResult represents the result of a migration operation
type MigrationResult struct {
	Type             string                 `json:"type"`
	Status           string                 `json:"status"`
	RecordsTotal     int                    `json:"records_total"`
	RecordsProcessed int                    `json:"records_processed"`
	RecordsFailed    int                    `json:"records_failed"`
	Duration         time.Duration          `json:"duration"`
	Errors           []string               `json:"errors"`
	Summary          map[string]interface{} `json:"summary"`
}

// SourceDigitalWallet represents the digital wallet structure in the main system
type SourceDigitalWallet struct {
	ID            uint    `gorm:"primaryKey"`
	PhoneNumber   string  `gorm:"type:varchar(20);uniqueIndex;not null"`
	AccountNumber string  `gorm:"type:varchar(20);uniqueIndex;not null"`
	WalletType    string  `gorm:"type:varchar(20);not null;default:'individual'"`
	Balance       float64 `gorm:"default:0"`
	Currency      string  `gorm:"type:varchar(10);default:'SZL'"`
	Status        string  `gorm:"type:varchar(20);default:'active'"`
	IsVerified    bool    `gorm:"default:false"`
	KYCLevel      string  `gorm:"type:varchar(20);default:'basic'"`
	Email         string  `gorm:"type:varchar(100)"`
	Settings      datatypes.JSON
	Metadata      datatypes.JSON
	CreatedAt     time.Time
	UpdatedAt     time.Time
}

// SourcePayCard represents the paycard structure in the main system
type SourcePayCard struct {
	ID                     uint    `gorm:"primaryKey"`
	CardNumber             string  `gorm:"type:varchar(20);uniqueIndex;not null"`
	WalletID               uint    `gorm:"not null"`
	CardHolderName         string  `gorm:"type:varchar(100);not null"`
	CardType               string  `gorm:"type:varchar(20);not null"`
	Status                 string  `gorm:"type:varchar(20);default:'active'"`
	SpendingLimit          float64 `gorm:"default:1000"`
	DailySpendingLimit     float64 `gorm:"default:500"`
	MonthlySpendingLimit   float64 `gorm:"default:5000"`
	CurrentDailySpent      float64 `gorm:"default:0"`
	CurrentMonthlySpent    float64 `gorm:"default:0"`
	LastSpendingReset      time.Time
	QRCodeData             string `gorm:"type:text"`
	SecurityPin            string `gorm:"type:varchar(255);not null"`
	IsPinSet               bool   `gorm:"not null;default:false"`
	FailedPinAttempts      int    `gorm:"not null;default:0"`
	LastFailedPinAttempt   *time.Time
	CardLockedUntil        *time.Time
	MerchantRestrictions   datatypes.JSON
	GeographicRestrictions datatypes.JSON
	CreatedAt              time.Time
	UpdatedAt              time.Time
}

// SourceWalletTransaction represents transaction structure in the main system
type SourceWalletTransaction struct {
	ID                    uint    `gorm:"primaryKey"`
	WalletID              uint    `gorm:"not null"`
	Type                  string  `gorm:"type:varchar(50);not null"`
	Amount                float64 `gorm:"not null"`
	Currency              string  `gorm:"type:varchar(10);default:'SZL'"`
	Status                string  `gorm:"type:varchar(20);not null"`
	Reference             string  `gorm:"type:varchar(100);uniqueIndex"`
	Description           string  `gorm:"type:text"`
	Category              string  `gorm:"type:varchar(50)"`
	ExternalTransactionID *string `gorm:"type:varchar(100);index"`
	PaymentMethod         string  `gorm:"type:varchar(50)"`
	ProviderCode          string  `gorm:"type:varchar(50)"`
	Fee                   float64 `gorm:"type:decimal(15,2);default:0"`
	IPAddress             string  `gorm:"type:varchar(45)"`
	DeviceInfo            datatypes.JSON
	LocationInfo          datatypes.JSON
	Meta                  datatypes.JSON
	CreatedAt             time.Time
}

// TableName methods for source models
func (SourceDigitalWallet) TableName() string     { return "digital_wallets" }
func (SourcePayCard) TableName() string           { return "pay_cards" }
func (SourceWalletTransaction) TableName() string { return "digital_wallet_transactions" }

// MigrateAllData performs complete data migration
func (m *MigrationService) MigrateAllData(ctx context.Context) (*MigrationResult, error) {
	m.logger.LogSystem("migration_started", "migration", "", "Starting complete data migration")

	startTime := time.Now()
	result := &MigrationResult{
		Type:    "complete_migration",
		Status:  "running",
		Errors:  []string{},
		Summary: make(map[string]interface{}),
	}

	// Create migration status record
	migrationStatus := &MigrationStatus{
		MigrationType: "complete_migration",
		Status:        "running",
		StartTime:     startTime,
		Metadata:      datatypes.JSON(`{}`),
	}
	m.targetDB.Create(migrationStatus)

	// Step 1: Migrate Digital Wallets
	walletResult, err := m.MigrateDigitalWallets(ctx)
	if err != nil {
		result.Errors = append(result.Errors, fmt.Sprintf("Wallet migration failed: %v", err))
	}
	result.Summary["wallets"] = walletResult

	// Step 2: Migrate PayCards
	cardResult, err := m.MigratePayCards(ctx)
	if err != nil {
		result.Errors = append(result.Errors, fmt.Sprintf("PayCard migration failed: %v", err))
	}
	result.Summary["paycards"] = cardResult

	// Step 3: Migrate Wallet Transactions
	txnResult, err := m.MigrateWalletTransactions(ctx)
	if err != nil {
		result.Errors = append(result.Errors, fmt.Sprintf("Transaction migration failed: %v", err))
	}
	result.Summary["transactions"] = txnResult

	// Step 4: Migrate Security Data
	securityResult, err := m.MigrateSecurityData(ctx)
	if err != nil {
		result.Errors = append(result.Errors, fmt.Sprintf("Security migration failed: %v", err))
	}
	result.Summary["security"] = securityResult

	// Calculate totals
	result.RecordsTotal = walletResult.RecordsTotal + cardResult.RecordsTotal + txnResult.RecordsTotal + securityResult.RecordsTotal
	result.RecordsProcessed = walletResult.RecordsProcessed + cardResult.RecordsProcessed + txnResult.RecordsProcessed + securityResult.RecordsProcessed
	result.RecordsFailed = walletResult.RecordsFailed + cardResult.RecordsFailed + txnResult.RecordsFailed + securityResult.RecordsFailed

	// Update status
	endTime := time.Now()
	result.Duration = endTime.Sub(startTime)

	if len(result.Errors) == 0 {
		result.Status = "completed"
		migrationStatus.Status = "completed"
	} else {
		result.Status = "completed_with_errors"
		migrationStatus.Status = "completed_with_errors"
		migrationStatus.ErrorMessage = fmt.Sprintf("%d errors occurred", len(result.Errors))
	}

	migrationStatus.EndTime = &endTime
	migrationStatus.RecordsTotal = result.RecordsTotal
	migrationStatus.RecordsProcessed = result.RecordsProcessed
	migrationStatus.RecordsFailed = result.RecordsFailed
	m.targetDB.Save(migrationStatus)

	m.logger.LogSystem("migration_completed", "migration", "",
		fmt.Sprintf("Migration completed: %d/%d records processed in %v",
			result.RecordsProcessed, result.RecordsTotal, result.Duration))

	return result, nil
}

// MigrateDigitalWallets migrates digital wallet data
func (m *MigrationService) MigrateDigitalWallets(ctx context.Context) (*MigrationResult, error) {
	m.logger.LogSystem("wallet_migration_started", "migration", "", "Starting wallet migration")

	result := &MigrationResult{
		Type:   "digital_wallets",
		Status: "running",
		Errors: []string{},
	}

	// Get total count
	var totalCount int64
	if err := m.sourceDB.Model(&SourceDigitalWallet{}).Count(&totalCount).Error; err != nil {
		return nil, fmt.Errorf("failed to count source wallets: %w", err)
	}
	result.RecordsTotal = int(totalCount)

	// Process in batches
	batchSize := 100
	offset := 0
	processed := 0
	failed := 0

	for {
		var sourceWallets []SourceDigitalWallet
		err := m.sourceDB.Limit(batchSize).Offset(offset).Find(&sourceWallets).Error
		if err != nil {
			return nil, fmt.Errorf("failed to fetch source wallets: %w", err)
		}

		if len(sourceWallets) == 0 {
			break
		}

		for _, sourceWallet := range sourceWallets {
			targetWallet := &models.Wallet{
				PhoneNumber:    sourceWallet.PhoneNumber,
				AccountNumber:  sourceWallet.AccountNumber,
				WalletType:     sourceWallet.WalletType,
				Balance:        sourceWallet.Balance,
				Currency:       sourceWallet.Currency,
				Status:         sourceWallet.Status,
				IsVerified:     sourceWallet.IsVerified,
				KYCLevel:       sourceWallet.KYCLevel,
				Email:          &sourceWallet.Email,
				DailyLimit:     1000.0, // Default values for new fields
				MonthlyLimit:   10000.0,
				DailySpent:     0.0,
				MonthlySpent:   0.0,
				LastLimitReset: func() *time.Time { t := time.Now(); return &t }(),
				Settings:       sourceWallet.Settings,
				Metadata:       sourceWallet.Metadata,
				CreatedAt:      sourceWallet.CreatedAt,
				UpdatedAt:      sourceWallet.UpdatedAt,
			}

			// Check if wallet already exists
			var existingWallet models.Wallet
			if err := m.targetDB.Where("phone_number = ?", sourceWallet.PhoneNumber).First(&existingWallet).Error; err == nil {
				// Update existing wallet
				m.targetDB.Model(&existingWallet).Updates(targetWallet)
			} else {
				// Create new wallet
				if err := m.targetDB.Create(targetWallet).Error; err != nil {
					failed++
					result.Errors = append(result.Errors, fmt.Sprintf("Failed to create wallet %s: %v", sourceWallet.PhoneNumber, err))
					continue
				}
			}
			processed++
		}

		offset += batchSize

		// Check context cancellation
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		default:
		}
	}

	result.RecordsProcessed = processed
	result.RecordsFailed = failed
	result.Status = "completed"

	m.logger.LogSystem("wallet_migration_completed", "migration", "",
		fmt.Sprintf("Wallet migration completed: %d/%d processed", processed, result.RecordsTotal))

	return result, nil
}

// MigratePayCards migrates paycard data
func (m *MigrationService) MigratePayCards(ctx context.Context) (*MigrationResult, error) {
	m.logger.LogSystem("paycard_migration_started", "migration", "", "Starting paycard migration")

	result := &MigrationResult{
		Type:   "paycards",
		Status: "running",
		Errors: []string{},
	}

	// Get total count
	var totalCount int64
	if err := m.sourceDB.Model(&SourcePayCard{}).Count(&totalCount).Error; err != nil {
		return nil, fmt.Errorf("failed to count source paycards: %w", err)
	}
	result.RecordsTotal = int(totalCount)

	// Process in batches
	batchSize := 100
	offset := 0
	processed := 0
	failed := 0

	for {
		var sourceCards []SourcePayCard
		err := m.sourceDB.Limit(batchSize).Offset(offset).Find(&sourceCards).Error
		if err != nil {
			return nil, fmt.Errorf("failed to fetch source paycards: %w", err)
		}

		if len(sourceCards) == 0 {
			break
		}

		for _, sourceCard := range sourceCards {
			// Find corresponding wallet in target database
			var targetWallet models.Wallet
			if err := m.targetDB.Where("id = ?", sourceCard.WalletID).First(&targetWallet).Error; err != nil {
				failed++
				result.Errors = append(result.Errors, fmt.Sprintf("Wallet not found for card %s: %v", sourceCard.CardNumber, err))
				continue
			}

			targetCard := &models.PayCard{
				CardNumber:             sourceCard.CardNumber,
				WalletID:               sourceCard.WalletID,
				CardHolderName:         sourceCard.CardHolderName,
				CardType:               sourceCard.CardType,
				Status:                 sourceCard.Status,
				SpendingLimit:          sourceCard.SpendingLimit,
				DailySpendingLimit:     sourceCard.DailySpendingLimit,
				MonthlySpendingLimit:   sourceCard.MonthlySpendingLimit,
				CurrentDailySpent:      sourceCard.CurrentDailySpent,
				CurrentMonthlySpent:    sourceCard.CurrentMonthlySpent,
				LastSpendingReset:      &sourceCard.LastSpendingReset,
				QRCodeData:             sourceCard.QRCodeData,
				SecurityPin:            sourceCard.SecurityPin,
				IsPinSet:               sourceCard.IsPinSet,
				FailedPinAttempts:      sourceCard.FailedPinAttempts,
				LastFailedPinAttempt:   sourceCard.LastFailedPinAttempt,
				CardLockedUntil:        sourceCard.CardLockedUntil,
				MerchantRestrictions:   sourceCard.MerchantRestrictions,
				GeographicRestrictions: sourceCard.GeographicRestrictions,
				CreatedAt:              sourceCard.CreatedAt,
				UpdatedAt:              sourceCard.UpdatedAt,
			}

			// Check if card already exists
			var existingCard models.PayCard
			if err := m.targetDB.Where("card_number = ?", sourceCard.CardNumber).First(&existingCard).Error; err == nil {
				// Update existing card
				m.targetDB.Model(&existingCard).Updates(targetCard)
			} else {
				// Create new card
				if err := m.targetDB.Create(targetCard).Error; err != nil {
					failed++
					result.Errors = append(result.Errors, fmt.Sprintf("Failed to create card %s: %v", sourceCard.CardNumber, err))
					continue
				}
			}
			processed++
		}

		offset += batchSize

		// Check context cancellation
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		default:
		}
	}

	result.RecordsProcessed = processed
	result.RecordsFailed = failed
	result.Status = "completed"

	m.logger.LogSystem("paycard_migration_completed", "migration", "",
		fmt.Sprintf("PayCard migration completed: %d/%d processed", processed, result.RecordsTotal))

	return result, nil
}

// MigrateWalletTransactions migrates wallet transaction data
func (m *MigrationService) MigrateWalletTransactions(ctx context.Context) (*MigrationResult, error) {
	m.logger.LogSystem("transaction_migration_started", "migration", "", "Starting transaction migration")

	result := &MigrationResult{
		Type:   "wallet_transactions",
		Status: "running",
		Errors: []string{},
	}

	// Get total count
	var totalCount int64
	if err := m.sourceDB.Model(&SourceWalletTransaction{}).Count(&totalCount).Error; err != nil {
		return nil, fmt.Errorf("failed to count source transactions: %w", err)
	}
	result.RecordsTotal = int(totalCount)

	// Process in batches
	batchSize := 500 // Larger batch for transactions
	offset := 0
	processed := 0
	failed := 0

	for {
		var sourceTxns []SourceWalletTransaction
		err := m.sourceDB.Limit(batchSize).Offset(offset).Find(&sourceTxns).Error
		if err != nil {
			return nil, fmt.Errorf("failed to fetch source transactions: %w", err)
		}

		if len(sourceTxns) == 0 {
			break
		}

		// Batch insert for better performance
		var targetTxns []models.WalletTransaction
		for _, sourceTxn := range sourceTxns {
			// Verify wallet exists in target database
			var walletExists bool
			m.targetDB.Model(&models.Wallet{}).Select("count(*) > 0").Where("id = ?", sourceTxn.WalletID).Find(&walletExists)
			if !walletExists {
				failed++
				result.Errors = append(result.Errors, fmt.Sprintf("Wallet not found for transaction %s", sourceTxn.Reference))
				continue
			}

			targetTxn := models.WalletTransaction{
				WalletID:              sourceTxn.WalletID,
				Type:                  sourceTxn.Type,
				Amount:                sourceTxn.Amount,
				BalanceAfter:          sourceTxn.Amount, // Set to amount for now, would need to calculate actual balance
				Reference:             sourceTxn.Reference,
				Description:           sourceTxn.Description,
				Category:              sourceTxn.Category,
				ExternalTransactionID: sourceTxn.ExternalTransactionID,
				PaymentMethod:         sourceTxn.PaymentMethod,
				ProviderCode:          sourceTxn.ProviderCode,
				Fee:                   sourceTxn.Fee,
				IPAddress:             sourceTxn.IPAddress,
				DeviceInfo:            sourceTxn.DeviceInfo,
				LocationInfo:          sourceTxn.LocationInfo,
				Meta:                  sourceTxn.Meta,
				CreatedAt:             sourceTxn.CreatedAt,
			}
			targetTxns = append(targetTxns, targetTxn)
		}

		// Batch insert
		if len(targetTxns) > 0 {
			if err := m.targetDB.CreateInBatches(targetTxns, 100).Error; err != nil {
				failed += len(targetTxns)
				result.Errors = append(result.Errors, fmt.Sprintf("Failed to batch insert transactions: %v", err))
			} else {
				processed += len(targetTxns)
			}
		}

		offset += batchSize

		// Check context cancellation
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		default:
		}
	}

	result.RecordsProcessed = processed
	result.RecordsFailed = failed
	result.Status = "completed"

	m.logger.LogSystem("transaction_migration_completed", "migration", "",
		fmt.Sprintf("Transaction migration completed: %d/%d processed", processed, result.RecordsTotal))

	return result, nil
}

// MigrateSecurityData migrates security-related data
func (m *MigrationService) MigrateSecurityData(ctx context.Context) (*MigrationResult, error) {
	m.logger.LogSystem("security_migration_started", "migration", "", "Starting security data migration")

	result := &MigrationResult{
		Type:   "security_data",
		Status: "running",
		Errors: []string{},
	}

	// Migrate security events, fraud alerts, device registrations, etc.
	// For now, we'll create a basic implementation
	// In a real scenario, you'd migrate actual security data

	result.RecordsTotal = 0
	result.RecordsProcessed = 0
	result.RecordsFailed = 0
	result.Status = "completed"

	m.logger.LogSystem("security_migration_completed", "migration", "", "Security data migration completed")

	return result, nil
}

// GetMigrationStatus returns the status of all migrations
func (m *MigrationService) GetMigrationStatus() ([]MigrationStatus, error) {
	var statuses []MigrationStatus
	err := m.targetDB.Order("created_at DESC").Find(&statuses).Error
	return statuses, err
}

// GetMigrationStatusByType returns the status of a specific migration type
func (m *MigrationService) GetMigrationStatusByType(migrationType string) (*MigrationStatus, error) {
	var status MigrationStatus
	err := m.targetDB.Where("migration_type = ?", migrationType).Order("created_at DESC").First(&status).Error
	return &status, err
}

// ValidateMigration validates the migrated data integrity
func (m *MigrationService) ValidateMigration(ctx context.Context) (*MigrationResult, error) {
	m.logger.LogSystem("validation_started", "migration", "", "Starting migration validation")

	result := &MigrationResult{
		Type:    "validation",
		Status:  "running",
		Errors:  []string{},
		Summary: make(map[string]interface{}),
	}

	// Validate wallet counts
	var sourceWalletCount, targetWalletCount int64
	m.sourceDB.Model(&SourceDigitalWallet{}).Count(&sourceWalletCount)
	m.targetDB.Model(&models.Wallet{}).Count(&targetWalletCount)

	result.Summary["source_wallets"] = sourceWalletCount
	result.Summary["target_wallets"] = targetWalletCount

	if sourceWalletCount != targetWalletCount {
		result.Errors = append(result.Errors, fmt.Sprintf("Wallet count mismatch: source=%d, target=%d", sourceWalletCount, targetWalletCount))
	}

	// Validate paycard counts
	var sourceCardCount, targetCardCount int64
	m.sourceDB.Model(&SourcePayCard{}).Count(&sourceCardCount)
	m.targetDB.Model(&models.PayCard{}).Count(&targetCardCount)

	result.Summary["source_cards"] = sourceCardCount
	result.Summary["target_cards"] = targetCardCount

	if sourceCardCount != targetCardCount {
		result.Errors = append(result.Errors, fmt.Sprintf("PayCard count mismatch: source=%d, target=%d", sourceCardCount, targetCardCount))
	}

	// Validate transaction counts
	var sourceTxnCount, targetTxnCount int64
	m.sourceDB.Model(&SourceWalletTransaction{}).Count(&sourceTxnCount)
	m.targetDB.Model(&models.WalletTransaction{}).Count(&targetTxnCount)

	result.Summary["source_transactions"] = sourceTxnCount
	result.Summary["target_transactions"] = targetTxnCount

	if sourceTxnCount != targetTxnCount {
		result.Errors = append(result.Errors, fmt.Sprintf("Transaction count mismatch: source=%d, target=%d", sourceTxnCount, targetTxnCount))
	}

	// Validate balance integrity
	var sourceBalanceSum, targetBalanceSum float64
	m.sourceDB.Model(&SourceDigitalWallet{}).Select("COALESCE(SUM(balance), 0)").Scan(&sourceBalanceSum)
	m.targetDB.Model(&models.Wallet{}).Select("COALESCE(SUM(balance), 0)").Scan(&targetBalanceSum)

	result.Summary["source_balance_sum"] = sourceBalanceSum
	result.Summary["target_balance_sum"] = targetBalanceSum

	if sourceBalanceSum != targetBalanceSum {
		result.Errors = append(result.Errors, fmt.Sprintf("Balance sum mismatch: source=%.2f, target=%.2f", sourceBalanceSum, targetBalanceSum))
	}

	if len(result.Errors) == 0 {
		result.Status = "completed"
	} else {
		result.Status = "failed"
	}

	m.logger.LogSystem("validation_completed", "migration", "",
		fmt.Sprintf("Migration validation completed with %d errors", len(result.Errors)))

	return result, nil
}

// RollbackMigration rolls back the migration (for testing purposes)
func (m *MigrationService) RollbackMigration(ctx context.Context) error {
	m.logger.LogSystem("rollback_started", "migration", "", "Starting migration rollback")

	// Delete all migrated data in reverse order
	if err := m.targetDB.Where("1 = 1").Delete(&models.WalletTransaction{}).Error; err != nil {
		return fmt.Errorf("failed to rollback transactions: %w", err)
	}

	if err := m.targetDB.Where("1 = 1").Delete(&models.PayCard{}).Error; err != nil {
		return fmt.Errorf("failed to rollback paycards: %w", err)
	}

	if err := m.targetDB.Where("1 = 1").Delete(&models.Wallet{}).Error; err != nil {
		return fmt.Errorf("failed to rollback wallets: %w", err)
	}

	if err := m.targetDB.Where("1 = 1").Delete(&MigrationStatus{}).Error; err != nil {
		return fmt.Errorf("failed to rollback migration status: %w", err)
	}

	m.logger.LogSystem("rollback_completed", "migration", "", "Migration rollback completed")
	return nil
}
