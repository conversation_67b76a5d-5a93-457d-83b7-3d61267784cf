package integration

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"

	"wallet-platform/internal/config"
	"wallet-platform/internal/controllers"
	"wallet-platform/internal/database"
	"wallet-platform/internal/models"
	"wallet-platform/internal/routes"
	"wallet-platform/internal/services"
	"wallet-platform/pkg/logger"
)

type APITestSuite struct {
	suite.Suite
	app    *gin.Engine
	db     *gorm.DB
	config *config.Config
	logger *logger.Logger
}

func (suite *APITestSuite) SetupSuite() {
	// Set test environment
	os.Setenv("APP_ENVIRONMENT", "test")
	os.Setenv("INTERNAL_API_KEY", "test-internal-key")
	
	// Setup test database
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	assert.NoError(suite.T(), err)

	// Auto-migrate all models
	err = database.AutoMigrate(db)
	assert.NoError(suite.T(), err)

	suite.db = db
	suite.logger = logger.New()

	// Setup test configuration
	suite.config = &config.Config{
		App: config.AppConfig{
			Environment: "test",
			Debug:       true,
		},
		Server: config.ServerConfig{
			Port: 8080,
			Host: "localhost",
		},
		Database: config.DatabaseConfig{
			Driver: "sqlite",
		},
	}

	// Setup services
	container := services.NewContainer(db, suite.logger, suite.config)

	// Setup controllers
	walletController := controllers.NewWalletController(container)
	payCardController := controllers.NewPayCardController(container)
	analyticsController := controllers.NewAnalyticsController(container)
	webhookController := controllers.NewWebhookController(container)

	// Setup Gin app
	gin.SetMode(gin.TestMode)
	suite.app = gin.New()
	
	// Setup routes
	routes.SetupRoutes(suite.app, walletController, payCardController, analyticsController, webhookController)
}

func (suite *APITestSuite) TearDownSuite() {
	sqlDB, _ := suite.db.DB()
	sqlDB.Close()
	
	os.Unsetenv("APP_ENVIRONMENT")
	os.Unsetenv("INTERNAL_API_KEY")
}

func (suite *APITestSuite) SetupTest() {
	// Clean database before each test
	suite.db.Exec("DELETE FROM wallet_transactions")
	suite.db.Exec("DELETE FROM card_transactions")
	suite.db.Exec("DELETE FROM pay_cards")
	suite.db.Exec("DELETE FROM wallets")
}

// Helper function to make authenticated requests
func (suite *APITestSuite) makeRequest(method, url string, body interface{}, headers map[string]string) *httptest.ResponseRecorder {
	var reqBody *bytes.Buffer
	if body != nil {
		jsonData, _ := json.Marshal(body)
		reqBody = bytes.NewBuffer(jsonData)
	} else {
		reqBody = bytes.NewBuffer([]byte{})
	}

	req, _ := http.NewRequest(method, url, reqBody)
	req.Header.Set("Content-Type", "application/json")
	
	// Add custom headers
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	w := httptest.NewRecorder()
	suite.app.ServeHTTP(w, req)
	return w
}

// Helper function to make internal API requests
func (suite *APITestSuite) makeInternalRequest(method, url string, body interface{}) *httptest.ResponseRecorder {
	headers := map[string]string{
		"X-Internal-Key":  "test-internal-key",
		"X-Service-Name":  "test-service",
		"X-User-ID":       "user123",
	}
	return suite.makeRequest(method, url, body, headers)
}

func (suite *APITestSuite) TestCreateWallet_Success() {
	payload := map[string]interface{}{
		"phone_number": "+************",
		"wallet_type":  "individual",
	}

	w := suite.makeInternalRequest("POST", "/api/v1/internal/wallets", payload)

	assert.Equal(suite.T(), http.StatusCreated, w.Code)
	
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.True(suite.T(), response["success"].(bool))
	
	data := response["data"].(map[string]interface{})
	assert.Equal(suite.T(), "+************", data["phone_number"])
	assert.Equal(suite.T(), "individual", data["wallet_type"])
	assert.NotEmpty(suite.T(), data["account_number"])
}

func (suite *APITestSuite) TestCreateWallet_InvalidPhoneNumber() {
	payload := map[string]interface{}{
		"phone_number": "invalid-phone",
		"wallet_type":  "individual",
	}

	w := suite.makeInternalRequest("POST", "/api/v1/internal/wallets", payload)

	assert.Equal(suite.T(), http.StatusBadRequest, w.Code)
	
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.False(suite.T(), response["success"].(bool))
}

func (suite *APITestSuite) TestGetWallet_Success() {
	// First create a wallet
	createPayload := map[string]interface{}{
		"phone_number": "+************",
		"wallet_type":  "individual",
	}
	createResp := suite.makeInternalRequest("POST", "/api/v1/internal/wallets", createPayload)
	assert.Equal(suite.T(), http.StatusCreated, createResp.Code)

	var createResponse map[string]interface{}
	json.Unmarshal(createResp.Body.Bytes(), &createResponse)
	data := createResponse["data"].(map[string]interface{})
	walletID := data["id"].(float64)

	// Get the wallet
	w := suite.makeInternalRequest("GET", fmt.Sprintf("/api/v1/internal/wallets/%.0f", walletID), nil)

	assert.Equal(suite.T(), http.StatusOK, w.Code)
	
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.True(suite.T(), response["success"].(bool))
}

func (suite *APITestSuite) TestGetWalletByPhone_Success() {
	// First create a wallet
	createPayload := map[string]interface{}{
		"phone_number": "+************",
		"wallet_type":  "individual",
	}
	createResp := suite.makeInternalRequest("POST", "/api/v1/internal/wallets", createPayload)
	assert.Equal(suite.T(), http.StatusCreated, createResp.Code)

	// Get wallet by phone
	w := suite.makeInternalRequest("GET", "/api/v1/internal/wallets/phone/+************", nil)

	assert.Equal(suite.T(), http.StatusOK, w.Code)
	
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.True(suite.T(), response["success"].(bool))
	
	data := response["data"].(map[string]interface{})
	assert.Equal(suite.T(), "+************", data["phone_number"])
}

func (suite *APITestSuite) TestTopupWallet_Success() {
	// First create a wallet
	createPayload := map[string]interface{}{
		"phone_number": "+************",
		"wallet_type":  "individual",
	}
	createResp := suite.makeInternalRequest("POST", "/api/v1/internal/wallets", createPayload)
	assert.Equal(suite.T(), http.StatusCreated, createResp.Code)

	var createResponse map[string]interface{}
	json.Unmarshal(createResp.Body.Bytes(), &createResponse)
	data := createResponse["data"].(map[string]interface{})
	walletID := data["id"].(float64)

	// Topup wallet
	topupPayload := map[string]interface{}{
		"amount":         100.0,
		"payment_method": "mobile_money",
		"reference":      "TOPUP123",
	}
	w := suite.makeInternalRequest("POST", fmt.Sprintf("/api/v1/internal/wallets/%.0f/topup", walletID), topupPayload)

	assert.Equal(suite.T(), http.StatusOK, w.Code)
	
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.True(suite.T(), response["success"].(bool))
}

func (suite *APITestSuite) TestTransferFunds_Success() {
	// Create sender wallet
	senderPayload := map[string]interface{}{
		"phone_number": "+************",
		"wallet_type":  "individual",
	}
	senderResp := suite.makeInternalRequest("POST", "/api/v1/internal/wallets", senderPayload)
	assert.Equal(suite.T(), http.StatusCreated, senderResp.Code)

	var senderResponse map[string]interface{}
	json.Unmarshal(senderResp.Body.Bytes(), &senderResponse)
	senderData := senderResponse["data"].(map[string]interface{})
	senderID := senderData["id"].(float64)

	// Create receiver wallet
	receiverPayload := map[string]interface{}{
		"phone_number": "+256701234568",
		"wallet_type":  "individual",
	}
	receiverResp := suite.makeInternalRequest("POST", "/api/v1/internal/wallets", receiverPayload)
	assert.Equal(suite.T(), http.StatusCreated, receiverResp.Code)

	var receiverResponse map[string]interface{}
	json.Unmarshal(receiverResp.Body.Bytes(), &receiverResponse)
	receiverData := receiverResponse["data"].(map[string]interface{})
	receiverID := receiverData["id"].(float64)

	// Topup sender wallet
	topupPayload := map[string]interface{}{
		"amount":         200.0,
		"payment_method": "mobile_money",
		"reference":      "TOPUP123",
	}
	suite.makeInternalRequest("POST", fmt.Sprintf("/api/v1/internal/wallets/%.0f/topup", senderID), topupPayload)

	// Transfer funds
	transferPayload := map[string]interface{}{
		"from_wallet_id": senderID,
		"to_wallet_id":   receiverID,
		"amount":         50.0,
		"reference":      "TRANSFER123",
	}
	w := suite.makeInternalRequest("POST", "/api/v1/internal/wallets/transfer", transferPayload)

	assert.Equal(suite.T(), http.StatusOK, w.Code)
	
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.True(suite.T(), response["success"].(bool))
}

func (suite *APITestSuite) TestCreateCard_Success() {
	// First create a wallet
	createPayload := map[string]interface{}{
		"phone_number": "+************",
		"wallet_type":  "individual",
	}
	createResp := suite.makeInternalRequest("POST", "/api/v1/internal/wallets", createPayload)
	assert.Equal(suite.T(), http.StatusCreated, createResp.Code)

	var createResponse map[string]interface{}
	json.Unmarshal(createResp.Body.Bytes(), &createResponse)
	data := createResponse["data"].(map[string]interface{})
	walletID := data["id"].(float64)

	// Create card
	cardPayload := map[string]interface{}{
		"wallet_id":  walletID,
		"card_type":  "virtual",
		"card_name":  "Test Card",
	}
	w := suite.makeInternalRequest("POST", "/api/v1/internal/cards", cardPayload)

	assert.Equal(suite.T(), http.StatusCreated, w.Code)
	
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.True(suite.T(), response["success"].(bool))
	
	cardData := response["data"].(map[string]interface{})
	assert.Equal(suite.T(), "virtual", cardData["card_type"])
	assert.Equal(suite.T(), "Test Card", cardData["card_name"])
	assert.NotEmpty(suite.T(), cardData["card_number"])
}

func (suite *APITestSuite) TestInternalAuth_InvalidKey() {
	payload := map[string]interface{}{
		"phone_number": "+************",
		"wallet_type":  "individual",
	}

	headers := map[string]string{
		"X-Internal-Key": "invalid-key",
	}
	w := suite.makeRequest("POST", "/api/v1/internal/wallets", payload, headers)

	assert.Equal(suite.T(), http.StatusUnauthorized, w.Code)
	
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Contains(suite.T(), response["error"].(map[string]interface{})["message"], "Invalid internal API key")
}

func (suite *APITestSuite) TestInternalAuth_MissingKey() {
	payload := map[string]interface{}{
		"phone_number": "+************",
		"wallet_type":  "individual",
	}

	w := suite.makeRequest("POST", "/api/v1/internal/wallets", payload, map[string]string{})

	assert.Equal(suite.T(), http.StatusUnauthorized, w.Code)
}

func (suite *APITestSuite) TestHealthCheck() {
	w := suite.makeRequest("GET", "/health", nil, map[string]string{})

	assert.Equal(suite.T(), http.StatusOK, w.Code)
	
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "healthy", response["status"])
}

func TestAPITestSuite(t *testing.T) {
	suite.Run(t, new(APITestSuite))
}
