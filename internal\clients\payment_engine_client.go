package clients

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"wallet-platform/internal/config"
	"wallet-platform/pkg/logger"
)

// PaymentEngineClient handles communication with the main payment engine
type PaymentEngineClient struct {
	baseURL    string
	apiKey     string
	httpClient *http.Client
	logger     *logger.Logger
}

// NewPaymentEngineClient creates a new payment engine client
func NewPaymentEngineClient(cfg *config.Config, logger *logger.Logger) *PaymentEngineClient {
	return &PaymentEngineClient{
		baseURL: cfg.External.PaymentEngine.BaseURL,
		apiKey:  cfg.External.PaymentEngine.APIKey,
		httpClient: &http.Client{
			Timeout: time.Duration(cfg.External.PaymentEngine.Timeout) * time.Second,
		},
		logger: logger,
	}
}

// PaymentRequest represents a payment processing request
type PaymentRequest struct {
	WalletID      uint                   `json:"wallet_id"`
	Amount        float64                `json:"amount"`
	Currency      string                 `json:"currency"`
	PaymentMethod string                 `json:"payment_method"`
	ProviderCode  string                 `json:"provider_code"`
	Reference     string                 `json:"reference"`
	Metadata      map[string]interface{} `json:"metadata"`
}

// PaymentResponse represents a payment processing response
type PaymentResponse struct {
	Success       bool                   `json:"success"`
	TransactionID string                 `json:"transaction_id"`
	Status        string                 `json:"status"`
	Amount        float64                `json:"amount"`
	Currency      string                 `json:"currency"`
	Reference     string                 `json:"reference"`
	Metadata      map[string]interface{} `json:"metadata"`
	CreatedAt     time.Time              `json:"created_at"`
}

// ServiceValidationRequest represents a service validation request
type ServiceValidationRequest struct {
	ServiceID   uint   `json:"service_id"`
	UserID      uint   `json:"user_id"`
	Action      string `json:"action"`
	ResourceID  string `json:"resource_id"`
}

// ServiceValidationResponse represents a service validation response
type ServiceValidationResponse struct {
	Valid       bool                   `json:"valid"`
	Permissions []string               `json:"permissions"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// ProcessPayment processes a payment through the payment engine
func (c *PaymentEngineClient) ProcessPayment(req *PaymentRequest) (*PaymentResponse, error) {
	endpoint := "/api/v1/payments/process"
	
	var response PaymentResponse
	err := c.makeRequest("POST", endpoint, req, &response)
	if err != nil {
		c.logger.LogError(err, map[string]interface{}{
			"action":   "process_payment",
			"wallet_id": req.WalletID,
			"amount":   req.Amount,
		})
		return nil, err
	}

	c.logger.LogTransaction(
		fmt.Sprintf("%d", req.WalletID),
		req.Reference,
		"payment_processed",
		req.Amount,
		fmt.Sprintf("Payment processed via payment engine: %s", response.TransactionID),
	)

	return &response, nil
}

// ValidateService validates a service operation with the payment engine
func (c *PaymentEngineClient) ValidateService(req *ServiceValidationRequest) (*ServiceValidationResponse, error) {
	endpoint := fmt.Sprintf("/api/v1/services/%d/validate", req.ServiceID)
	
	var response ServiceValidationResponse
	err := c.makeRequest("POST", endpoint, req, &response)
	if err != nil {
		c.logger.LogError(err, map[string]interface{}{
			"action":     "validate_service",
			"service_id": req.ServiceID,
			"user_id":    req.UserID,
		})
		return nil, err
	}

	return &response, nil
}

// NotifyPaymentEngine sends a notification to the payment engine
func (c *PaymentEngineClient) NotifyPaymentEngine(eventType string, data map[string]interface{}) error {
	endpoint := "/api/v1/notifications/receive"
	
	payload := map[string]interface{}{
		"event_type": eventType,
		"data":       data,
		"timestamp":  time.Now(),
		"source":     "wallet_platform",
	}

	err := c.makeRequest("POST", endpoint, payload, nil)
	if err != nil {
		c.logger.LogError(err, map[string]interface{}{
			"action":     "notify_payment_engine",
			"event_type": eventType,
		})
		return err
	}

	return nil
}

// GetServiceConfiguration retrieves service configuration from payment engine
func (c *PaymentEngineClient) GetServiceConfiguration(serviceID uint) (map[string]interface{}, error) {
	endpoint := fmt.Sprintf("/api/v1/services/%d/config", serviceID)
	
	var response map[string]interface{}
	err := c.makeRequest("GET", endpoint, nil, &response)
	if err != nil {
		c.logger.LogError(err, map[string]interface{}{
			"action":     "get_service_config",
			"service_id": serviceID,
		})
		return nil, err
	}

	return response, nil
}

// makeRequest makes an HTTP request to the payment engine
func (c *PaymentEngineClient) makeRequest(method, endpoint string, payload interface{}, response interface{}) error {
	url := c.baseURL + endpoint
	
	var body io.Reader
	if payload != nil {
		jsonData, err := json.Marshal(payload)
		if err != nil {
			return fmt.Errorf("failed to marshal request: %w", err)
		}
		body = bytes.NewBuffer(jsonData)
	}

	req, err := http.NewRequest(method, url, body)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-API-Key", c.apiKey)
	req.Header.Set("Accept", "application/vnd.paymentengine.v1+json")
	req.Header.Set("User-Agent", "WalletPlatform/1.0")

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		bodyBytes, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("request failed with status %d: %s", resp.StatusCode, string(bodyBytes))
	}

	if response != nil {
		if err := json.NewDecoder(resp.Body).Decode(response); err != nil {
			return fmt.Errorf("failed to decode response: %w", err)
		}
	}

	return nil
}

// HealthCheck checks the health of the payment engine
func (c *PaymentEngineClient) HealthCheck() error {
	endpoint := "/api/v1/health"
	
	err := c.makeRequest("GET", endpoint, nil, nil)
	if err != nil {
		return fmt.Errorf("payment engine health check failed: %w", err)
	}

	return nil
}
