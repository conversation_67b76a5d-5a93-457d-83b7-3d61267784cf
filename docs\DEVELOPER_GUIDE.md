# Wallet Platform Developer Guide

## Getting Started

This guide helps developers set up the development environment and understand the codebase structure for contributing to the Wallet Platform.

## Development Environment Setup

### Prerequisites

- **Go**: Version 1.19 or higher
- **Git**: Latest version
- **Docker**: For containerized development (optional)
- **MySQL**: Version 8.0+ or Docker container
- **Redis**: Version 6.0+ or Docker container
- **IDE**: VS Code, GoLand, or any Go-compatible editor

### Local Setup

#### 1. Clone Repository
```bash
git clone <repository-url>
cd wallet-platform
```

#### 2. Install Dependencies
```bash
go mod download
go mod tidy
```

#### 3. Setup Development Database
```bash
# Using Docker
docker run --name wallet-mysql \
  -e MYSQL_DATABASE=wallet_platform_dev \
  -e MYSQL_USER=wallet_dev \
  -e MYSQL_PASSWORD=dev_password \
  -e MYSQL_ROOT_PASSWORD=root_password \
  -p 3306:3306 -d mysql:8.0

# Using Docker Compose
docker-compose -f docker-compose.dev.yml up -d
```

#### 4. Setup Redis
```bash
# Using Docker
docker run --name wallet-redis -p 6379:6379 -d redis:6-alpine
```

#### 5. Environment Configuration
Create `.env.dev` file:
```env
# Development Configuration
PORT=8086
HOST=localhost
ENV=development

# Database
DB_HOST=localhost
DB_PORT=3306
DB_NAME=wallet_platform_dev
DB_USER=wallet_dev
DB_PASSWORD=dev_password
DB_SSL_MODE=disable

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Security (Development keys - DO NOT use in production)
JWT_SECRET=dev-jwt-secret-key-change-in-production
API_KEY_SECRET=dev-api-key-secret
ENCRYPTION_KEY=dev-encryption-key-32-characters

# Logging
LOG_LEVEL=debug
LOG_FORMAT=text

# Development Features
METRICS_ENABLED=true
HEALTH_CHECK_ENABLED=true
```

#### 6. Run Database Migrations
```bash
go run cmd/migrate/main.go
```

#### 7. Start Development Server
```bash
go run cmd/server/main.go
```

## Project Structure

```
wallet-platform/
├── cmd/                    # Application entry points
│   ├── server/            # Main server application
│   └── migrate/           # Database migration tool
├── internal/              # Private application code
│   ├── config/           # Configuration management
│   ├── controllers/      # HTTP request handlers
│   ├── middleware/       # HTTP middleware
│   ├── models/          # Data models and structures
│   ├── routes/          # Route definitions
│   └── services/        # Business logic layer
├── pkg/                  # Public library code
│   ├── database/        # Database utilities
│   ├── logger/          # Logging utilities
│   └── utils/           # Common utilities
├── docs/                # Documentation
├── scripts/             # Build and deployment scripts
├── tests/               # Test files
├── config/              # Configuration files
└── docker/              # Docker-related files
```

## Code Organization

### Controllers Layer
Controllers handle HTTP requests and responses:

```go
// Example controller structure
type WalletController struct {
    container *services.Container
}

func (wc *WalletController) CreateWallet(c *gin.Context) {
    // 1. Validate request
    var req CreateWalletRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        // Handle validation error
        return
    }
    
    // 2. Call service layer
    wallet, err := wc.container.WalletService.CreateWallet(req.PhoneNumber, "individual")
    if err != nil {
        // Handle service error
        return
    }
    
    // 3. Return response
    c.JSON(http.StatusOK, SuccessResponse{
        Success: true,
        Message: "Wallet created successfully",
        Data:    wallet,
    })
}
```

### Services Layer
Services contain business logic:

```go
// Example service structure
type WalletService struct {
    db     *gorm.DB
    logger *logger.Logger
}

func (s *WalletService) CreateWallet(phoneNumber, walletType string) (*models.WalletResponse, error) {
    // 1. Validate input
    if phoneNumber == "" {
        return nil, fmt.Errorf("phone number is required")
    }
    
    // 2. Check business rules
    var existingWallet models.Wallet
    if err := s.db.Where("phone_number = ?", phoneNumber).First(&existingWallet).Error; err == nil {
        return nil, fmt.Errorf("wallet already exists")
    }
    
    // 3. Create wallet
    wallet := models.Wallet{
        PhoneNumber: phoneNumber,
        WalletType:  walletType,
        Status:      "active",
    }
    
    // 4. Save to database
    if err := s.db.Create(&wallet).Error; err != nil {
        return nil, err
    }
    
    return s.walletToResponse(&wallet), nil
}
```

### Models Layer
Models define data structures:

```go
// Database model
type Wallet struct {
    ID            uint      `gorm:"primaryKey" json:"id"`
    PhoneNumber   string    `gorm:"uniqueIndex;not null" json:"phone_number"`
    AccountNumber string    `gorm:"uniqueIndex;not null" json:"account_number"`
    WalletType    string    `gorm:"type:varchar(20);not null" json:"wallet_type"`
    Balance       float64   `gorm:"type:decimal(15,2);default:0" json:"balance"`
    Status        string    `gorm:"type:varchar(20);default:'active'" json:"status"`
    CreatedAt     time.Time `json:"created_at"`
    UpdatedAt     time.Time `json:"updated_at"`
}

// Response model
type WalletResponse struct {
    ID            uint      `json:"id"`
    PhoneNumber   string    `json:"phone_number"`
    AccountNumber string    `json:"account_number"`
    WalletType    string    `json:"wallet_type"`
    Balance       float64   `json:"balance"`
    Status        string    `json:"status"`
    CreatedAt     time.Time `json:"created_at"`
    UpdatedAt     time.Time `json:"updated_at"`
}
```

## Development Workflow

### 1. Feature Development

#### Create Feature Branch
```bash
git checkout -b feature/new-wallet-feature
```

#### Development Process
1. Write tests first (TDD approach)
2. Implement the feature
3. Run tests and ensure they pass
4. Update documentation
5. Create pull request

### 2. Testing

#### Unit Tests
```bash
# Run all tests
go test ./...

# Run tests with coverage
go test -cover ./...

# Run tests for specific package
go test ./internal/services/
```

#### Integration Tests
```bash
# Run integration tests
go test -tags=integration ./tests/integration/
```

#### Example Test
```go
func TestWalletService_CreateWallet(t *testing.T) {
    // Setup
    db := setupTestDB()
    service := services.NewWalletService(db, logger.New())
    
    // Test case
    wallet, err := service.CreateWallet("+1234567890", "individual")
    
    // Assertions
    assert.NoError(t, err)
    assert.NotNil(t, wallet)
    assert.Equal(t, "+1234567890", wallet.PhoneNumber)
    assert.Equal(t, "individual", wallet.WalletType)
}
```

### 3. Code Quality

#### Linting
```bash
# Install golangci-lint
go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest

# Run linter
golangci-lint run
```

#### Formatting
```bash
# Format code
go fmt ./...

# Import organization
goimports -w .
```

#### Code Coverage
```bash
# Generate coverage report
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out -o coverage.html
```

## API Development

### Adding New Endpoints

#### 1. Define Route
```go
// In internal/routes/routes.go
walletRoutes.POST("/new-endpoint", walletController.NewEndpoint)
```

#### 2. Create Controller Method
```go
// In internal/controllers/wallet_controller.go
func (wc *WalletController) NewEndpoint(c *gin.Context) {
    // Implementation
}
```

#### 3. Add Service Method
```go
// In internal/services/wallet_service.go
func (s *WalletService) NewFeature() error {
    // Business logic
}
```

#### 4. Update Models (if needed)
```go
// In internal/models/wallet.go
type NewModel struct {
    // Fields
}
```

### Request/Response Patterns

#### Standard Request Structure
```go
type CreateWalletRequest struct {
    PhoneNumber string `json:"phone_number" binding:"required"`
    PIN         string `json:"pin" binding:"required,len=4"`
}
```

#### Standard Response Structure
```go
type SuccessResponse struct {
    Success bool        `json:"success"`
    Message string      `json:"message"`
    Data    interface{} `json:"data,omitempty"`
}

type ErrorResponse struct {
    Error   string `json:"error"`
    Message string `json:"message"`
    Code    string `json:"code"`
}
```

## Database Development

### Migrations

#### Create Migration
```go
// In cmd/migrate/main.go
func runMigrations(db *gorm.DB) error {
    return db.AutoMigrate(
        &models.Wallet{},
        &models.PayCard{},
        &models.WalletTransaction{},
        // Add new models here
    )
}
```

#### Database Seeding
```go
// Create seed data for development
func seedDatabase(db *gorm.DB) error {
    // Create test wallets
    wallets := []models.Wallet{
        {PhoneNumber: "+1234567890", WalletType: "individual"},
        {PhoneNumber: "+0987654321", WalletType: "business"},
    }
    
    for _, wallet := range wallets {
        db.FirstOrCreate(&wallet, models.Wallet{PhoneNumber: wallet.PhoneNumber})
    }
    
    return nil
}
```

### Query Optimization

#### Efficient Queries
```go
// Good: Use specific fields
var wallet models.Wallet
db.Select("id, balance").Where("id = ?", walletID).First(&wallet)

// Good: Use joins for related data
var transactions []models.WalletTransaction
db.Preload("Wallet").Where("wallet_id = ?", walletID).Find(&transactions)

// Good: Use pagination
db.Offset(offset).Limit(limit).Find(&transactions)
```

## Security Development

### Authentication Middleware
```go
func AuthMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        token := c.GetHeader("Authorization")
        if token == "" {
            c.JSON(http.StatusUnauthorized, ErrorResponse{
                Error: "UNAUTHORIZED",
                Message: "Authorization header required",
                Code: "401",
            })
            c.Abort()
            return
        }
        
        // Validate token
        claims, err := validateJWT(token)
        if err != nil {
            c.JSON(http.StatusUnauthorized, ErrorResponse{
                Error: "INVALID_TOKEN",
                Message: "Invalid or expired token",
                Code: "401",
            })
            c.Abort()
            return
        }
        
        c.Set("user_id", claims.UserID)
        c.Next()
    }
}
```

### Input Validation
```go
// Use struct tags for validation
type TransferRequest struct {
    FromWalletID uint    `json:"from_wallet_id" binding:"required"`
    ToWalletID   uint    `json:"to_wallet_id" binding:"required"`
    Amount       float64 `json:"amount" binding:"required,gt=0"`
    Description  string  `json:"description" binding:"max=255"`
}

// Custom validation
func validateTransferRequest(req TransferRequest) error {
    if req.FromWalletID == req.ToWalletID {
        return fmt.Errorf("cannot transfer to same wallet")
    }
    if req.Amount > 10000 {
        return fmt.Errorf("amount exceeds maximum limit")
    }
    return nil
}
```

## Performance Optimization

### Caching Strategies
```go
// Redis caching example
func (s *WalletService) GetWalletWithCache(walletID uint) (*models.WalletResponse, error) {
    // Check cache first
    cacheKey := fmt.Sprintf("wallet:%d", walletID)
    cached, err := s.redis.Get(cacheKey).Result()
    if err == nil {
        var wallet models.WalletResponse
        json.Unmarshal([]byte(cached), &wallet)
        return &wallet, nil
    }
    
    // Get from database
    wallet, err := s.GetWallet(walletID)
    if err != nil {
        return nil, err
    }
    
    // Cache result
    walletJSON, _ := json.Marshal(wallet)
    s.redis.Set(cacheKey, walletJSON, 5*time.Minute)
    
    return wallet, nil
}
```

### Database Connection Pooling
```go
// Configure connection pool
func setupDatabase(config *config.Config) (*gorm.DB, error) {
    db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
    if err != nil {
        return nil, err
    }
    
    sqlDB, err := db.DB()
    if err != nil {
        return nil, err
    }
    
    // Configure connection pool
    sqlDB.SetMaxOpenConns(25)
    sqlDB.SetMaxIdleConns(5)
    sqlDB.SetConnMaxLifetime(5 * time.Minute)
    
    return db, nil
}
```

## Debugging and Troubleshooting

### Logging Best Practices
```go
// Structured logging
func (s *WalletService) CreateWallet(phoneNumber, walletType string) (*models.WalletResponse, error) {
    s.logger.LogInfo("wallet_creation_started", map[string]interface{}{
        "phone_number": phoneNumber,
        "wallet_type":  walletType,
    })
    
    wallet, err := s.createWallet(phoneNumber, walletType)
    if err != nil {
        s.logger.LogError("wallet_creation_failed", map[string]interface{}{
            "phone_number": phoneNumber,
            "error":        err.Error(),
        })
        return nil, err
    }
    
    s.logger.LogInfo("wallet_creation_completed", map[string]interface{}{
        "wallet_id":    wallet.ID,
        "phone_number": phoneNumber,
    })
    
    return wallet, nil
}
```

### Debug Mode
```go
// Enable debug mode in development
if config.ENV == "development" {
    gin.SetMode(gin.DebugMode)
    db.Logger = db.Logger.LogMode(logger.Info) // Enable SQL logging
}
```

## Contributing Guidelines

### Code Style
- Follow Go conventions and best practices
- Use meaningful variable and function names
- Write clear comments for complex logic
- Keep functions small and focused
- Use dependency injection for testability

### Pull Request Process
1. Create feature branch from main
2. Write tests for new functionality
3. Ensure all tests pass
4. Update documentation
5. Create pull request with clear description
6. Address review feedback
7. Merge after approval

### Commit Message Format
```
type(scope): description

Examples:
feat(wallet): add wallet creation endpoint
fix(paycard): resolve PIN validation issue
docs(api): update authentication documentation
test(services): add unit tests for wallet service
```

## Useful Commands

### Development
```bash
# Run with hot reload (install air first)
go install github.com/cosmtrek/air@latest
air

# Run specific test
go test -run TestWalletService_CreateWallet ./internal/services/

# Generate mocks (install mockgen first)
go install github.com/golang/mock/mockgen@latest
mockgen -source=internal/services/wallet_service.go -destination=mocks/wallet_service_mock.go

# Build for different platforms
GOOS=linux GOARCH=amd64 go build -o bin/wallet-platform-linux cmd/server/main.go
GOOS=windows GOARCH=amd64 go build -o bin/wallet-platform.exe cmd/server/main.go
```

### Database
```bash
# Reset development database
docker-compose -f docker-compose.dev.yml down -v
docker-compose -f docker-compose.dev.yml up -d

# Connect to database
docker exec -it wallet-postgres psql -U wallet_dev -d wallet_platform_dev
```

### Docker
```bash
# Build development image
docker build -f docker/Dockerfile.dev -t wallet-platform:dev .

# Run with Docker Compose
docker-compose -f docker-compose.dev.yml up --build
```
