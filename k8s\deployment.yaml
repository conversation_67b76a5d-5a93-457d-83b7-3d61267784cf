apiVersion: apps/v1
kind: Deployment
metadata:
  name: wallet-platform
  namespace: wallet-platform
  labels:
    app: wallet-platform
    version: v1.0.0
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: wallet-platform
  template:
    metadata:
      labels:
        app: wallet-platform
        version: v1.0.0
    spec:
      containers:
      - name: wallet-platform
        image: wallet-platform:production
        imagePullPolicy: Always
        ports:
        - containerPort: 8086
          name: http
          protocol: TCP
        envFrom:
        - configMapRef:
            name: wallet-platform-config
        - secretRef:
            name: wallet-platform-secrets
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8086
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8086
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: logs
          mountPath: /app/logs
        securityContext:
          allowPrivilegeEscalation: false
          runAsNonRoot: true
          runAsUser: 1001
          runAsGroup: 1001
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
      volumes:
      - name: logs
        emptyDir: {}
      securityContext:
        fsGroup: 1001
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: wallet-platform-service
  namespace: wallet-platform
  labels:
    app: wallet-platform
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 8086
    protocol: TCP
    name: http
  selector:
    app: wallet-platform
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: wallet-platform-ingress
  namespace: wallet-platform
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  tls:
  - hosts:
    - wallet-api.yourdomain.com
    secretName: wallet-platform-tls
  rules:
  - host: wallet-api.yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: wallet-platform-service
            port:
              number: 80
