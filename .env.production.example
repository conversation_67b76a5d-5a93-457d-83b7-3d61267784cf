# Production Environment Configuration
# Copy this file to .env.production and update with SECURE values

# Application
APP_NAME=Wallet Platform
APP_VERSION=1.0.0
APP_ENVIRONMENT=production
APP_DEBUG=false

# Server
SERVER_PORT=8086
SERVER_HOST=0.0.0.0
SERVER_ENVIRONMENT=production
SERVER_READ_TIMEOUT=30
SERVER_WRITE_TIMEOUT=30
SERVER_IDLE_TIMEOUT=120

# CORS Configuration (Production - Restrictive)
SERVER_CORS_ALLOW_ORIGINS=https://your-frontend-domain.com,https://admin.your-domain.com
SERVER_CORS_ALLOW_METHODS=GET,POST,PUT,DELETE,OPTIONS
SERVER_CORS_ALLOW_HEADERS=Content-Type,Authorization,X-Request-ID,X-2FA-Code
SERVER_CORS_ALLOW_CREDENTIALS=true
SERVER_CORS_MAX_AGE=86400

# Database (Production - SECURE CREDENTIALS REQUIRED)
DATABASE_DRIVER=mysql
DATABASE_HOST=your-production-db-host
DATABASE_PORT=3306
DATABASE_USERNAME=your_secure_db_username
DATABASE_PASSWORD=your_very_secure_db_password
DATABASE_DATABASE=wallet_platform
DATABASE_SSL_MODE=true
DATABASE_MAX_OPEN_CONNS=50
DATABASE_MAX_IDLE_CONNS=10
DATABASE_CONN_MAX_LIFETIME=300
DATABASE_CONN_MAX_IDLE_TIME=60

# Redis (Required in production)
REDIS_ENABLED=true
REDIS_HOST=your-redis-host
REDIS_PORT=6379
REDIS_PASSWORD=your_secure_redis_password
REDIS_DATABASE=0
REDIS_POOL_SIZE=20
REDIS_MIN_IDLE_CONNS=5
REDIS_MAX_RETRIES=3

# JWT (Production - GENERATE SECURE RANDOM VALUES)
# Generate with: openssl rand -base64 32
JWT_SECRET_KEY=REPLACE_WITH_SECURE_BASE64_ENCODED_256_BIT_KEY
JWT_EXPIRATION_TIME=3600
JWT_REFRESH_TIME=86400
JWT_ISSUER=wallet-platform
JWT_AUDIENCE=wallet-platform-users

# Logging (Production)
LOG_LEVEL=info
LOG_FORMAT=json
LOG_OUTPUT=stdout

# Rate Limiting (Required in production)
RATE_LIMIT_ENABLED=true
RATE_LIMIT_DEFAULT_LIMIT=100
RATE_LIMIT_DEFAULT_WINDOW=60

# Security (Production - GENERATE SECURE RANDOM VALUES)
# Generate encryption key with: openssl rand -hex 16
SECURITY_ENCRYPTION_KEY=REPLACE_WITH_32_CHARACTER_ENCRYPTION_KEY
# Generate hash salt with: openssl rand -hex 8
SECURITY_HASH_SALT=REPLACE_WITH_16_CHARACTER_HASH_SALT
SECURITY_MAX_LOGIN_ATTEMPTS=5
SECURITY_LOCKOUT_DURATION=900
SECURITY_SESSION_TIMEOUT=3600
SECURITY_REQUIRE_HTTPS=true
SECURITY_CSRF_PROTECTION=true
SECURITY_CONTENT_TYPE_NO_SNIFF=true

# Internal API (Production - GENERATE SECURE RANDOM KEY)
# Generate with: openssl rand -base64 32
INTERNAL_API_KEY=REPLACE_WITH_SECURE_BASE64_ENCODED_API_KEY

# External Services (Production - SET REAL VALUES)
EXTERNAL_PAYMENT_ENGINE_BASE_URL=https://your-payment-engine.com
EXTERNAL_PAYMENT_ENGINE_API_KEY=your_production_payment_engine_api_key
EXTERNAL_PAYMENT_ENGINE_TIMEOUT=30
EXTERNAL_PAYMENT_ENGINE_RETRY_COUNT=3

EXTERNAL_SMS_PROVIDER=centurion
CENTURION_SMS_API_KEY=your_production_centurion_sms_api_key

EXTERNAL_EMAIL_PROVIDER=centurion
CENTURION_EMAIL_API_KEY=your_production_centurion_email_api_key

EXTERNAL_WEBHOOK_SECRET=your_production_webhook_secret_key
EXTERNAL_WEBHOOK_TIMEOUT=30
EXTERNAL_WEBHOOK_MAX_RETRIES=3

# Additional Production Settings
# Set these in your deployment environment

# SSL/TLS Certificate paths (if using file-based certs)
# SSL_CERT_PATH=/path/to/certificate.crt
# SSL_KEY_PATH=/path/to/private.key

# Monitoring and Alerting
# MONITORING_ENABLED=true
# ALERT_EMAIL=<EMAIL>
# METRICS_ENDPOINT=https://your-metrics-collector.com

# Backup Configuration
# BACKUP_ENABLED=true
# BACKUP_SCHEDULE=0 2 * * *
# BACKUP_RETENTION_DAYS=30
