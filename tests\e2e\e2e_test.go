package e2e

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

const (
	baseURL        = "http://localhost:8080"
	internalAPIKey = "test-internal-key"
)

type E2ETestSuite struct {
	suite.Suite
	client *http.Client
}

func (suite *E2ETestSuite) SetupSuite() {
	suite.client = &http.Client{
		Timeout: 30 * time.Second,
	}
	
	// Wait for server to be ready
	suite.waitForServer()
}

func (suite *E2ETestSuite) waitForServer() {
	maxRetries := 30
	for i := 0; i < maxRetries; i++ {
		resp, err := suite.client.Get(baseURL + "/health")
		if err == nil && resp.StatusCode == http.StatusOK {
			resp.Body.Close()
			return
		}
		if resp != nil {
			resp.Body.Close()
		}
		time.Sleep(1 * time.Second)
	}
	suite.T().Fatal("Server did not start within expected time")
}

func (suite *E2ETestSuite) makeRequest(method, endpoint string, payload interface{}, headers map[string]string) (*http.Response, error) {
	var reqBody *bytes.Buffer
	if payload != nil {
		jsonData, _ := json.Marshal(payload)
		reqBody = bytes.NewBuffer(jsonData)
	} else {
		reqBody = bytes.NewBuffer([]byte{})
	}

	req, err := http.NewRequest(method, baseURL+endpoint, reqBody)
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	return suite.client.Do(req)
}

func (suite *E2ETestSuite) makeInternalRequest(method, endpoint string, payload interface{}) (*http.Response, error) {
	headers := map[string]string{
		"X-Internal-Key":  internalAPIKey,
		"X-Service-Name":  "e2e-test",
		"X-User-ID":       "test-user-123",
	}
	return suite.makeRequest(method, endpoint, payload, headers)
}

func (suite *E2ETestSuite) TestCompleteWalletWorkflow() {
	// Test Case: Complete wallet lifecycle
	phoneNumber := "+256701234567"
	
	// Step 1: Create wallet
	createPayload := map[string]interface{}{
		"phone_number": phoneNumber,
		"wallet_type":  "individual",
	}
	
	resp, err := suite.makeInternalRequest("POST", "/api/v1/internal/wallets", createPayload)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusCreated, resp.StatusCode)
	
	var createResponse map[string]interface{}
	json.NewDecoder(resp.Body).Decode(&createResponse)
	resp.Body.Close()
	
	assert.True(suite.T(), createResponse["success"].(bool))
	walletData := createResponse["data"].(map[string]interface{})
	walletID := walletData["id"].(float64)
	
	// Step 2: Get wallet by ID
	resp, err = suite.makeInternalRequest("GET", fmt.Sprintf("/api/v1/internal/wallets/%.0f", walletID), nil)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, resp.StatusCode)
	resp.Body.Close()
	
	// Step 3: Get wallet by phone number
	resp, err = suite.makeInternalRequest("GET", "/api/v1/internal/wallets/phone/"+phoneNumber, nil)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, resp.StatusCode)
	resp.Body.Close()
	
	// Step 4: Topup wallet
	topupPayload := map[string]interface{}{
		"amount":         500.0,
		"payment_method": "mobile_money",
		"reference":      "E2E_TOPUP_001",
	}
	
	resp, err = suite.makeInternalRequest("POST", fmt.Sprintf("/api/v1/internal/wallets/%.0f/topup", walletID), topupPayload)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, resp.StatusCode)
	resp.Body.Close()
	
	// Step 5: Check wallet balance
	resp, err = suite.makeInternalRequest("GET", fmt.Sprintf("/api/v1/internal/wallets/%.0f/balance", walletID), nil)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, resp.StatusCode)
	
	var balanceResponse map[string]interface{}
	json.NewDecoder(resp.Body).Decode(&balanceResponse)
	resp.Body.Close()
	
	balanceData := balanceResponse["data"].(map[string]interface{})
	assert.Equal(suite.T(), 500.0, balanceData["balance"].(float64))
	
	// Step 6: Get wallet transactions
	resp, err = suite.makeInternalRequest("GET", fmt.Sprintf("/api/v1/internal/wallets/%.0f/transactions", walletID), nil)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, resp.StatusCode)
	
	var transactionsResponse map[string]interface{}
	json.NewDecoder(resp.Body).Decode(&transactionsResponse)
	resp.Body.Close()
	
	transactions := transactionsResponse["data"].([]interface{})
	assert.Len(suite.T(), transactions, 1) // Should have one topup transaction
}

func (suite *E2ETestSuite) TestCompletePayCardWorkflow() {
	// Test Case: Complete PayCard lifecycle
	phoneNumber := "+256701234568"
	
	// Step 1: Create wallet first
	createWalletPayload := map[string]interface{}{
		"phone_number": phoneNumber,
		"wallet_type":  "individual",
	}
	
	resp, err := suite.makeInternalRequest("POST", "/api/v1/internal/wallets", createWalletPayload)
	assert.NoError(suite.T(), err)
	
	var walletResponse map[string]interface{}
	json.NewDecoder(resp.Body).Decode(&walletResponse)
	resp.Body.Close()
	
	walletData := walletResponse["data"].(map[string]interface{})
	walletID := walletData["id"].(float64)
	
	// Step 2: Topup wallet
	topupPayload := map[string]interface{}{
		"amount":         1000.0,
		"payment_method": "mobile_money",
		"reference":      "E2E_CARD_TOPUP",
	}
	
	resp, err = suite.makeInternalRequest("POST", fmt.Sprintf("/api/v1/internal/wallets/%.0f/topup", walletID), topupPayload)
	assert.NoError(suite.T(), err)
	resp.Body.Close()
	
	// Step 3: Create PayCard
	createCardPayload := map[string]interface{}{
		"wallet_id": walletID,
		"card_type": "virtual",
		"card_name": "E2E Test Card",
	}
	
	resp, err = suite.makeInternalRequest("POST", "/api/v1/internal/cards", createCardPayload)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusCreated, resp.StatusCode)
	
	var cardResponse map[string]interface{}
	json.NewDecoder(resp.Body).Decode(&cardResponse)
	resp.Body.Close()
	
	cardData := cardResponse["data"].(map[string]interface{})
	cardID := cardData["id"].(float64)
	
	// Step 4: Get card details
	resp, err = suite.makeInternalRequest("GET", fmt.Sprintf("/api/v1/internal/cards/%.0f", cardID), nil)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, resp.StatusCode)
	resp.Body.Close()
	
	// Step 5: Get cards by wallet
	resp, err = suite.makeInternalRequest("GET", fmt.Sprintf("/api/v1/internal/cards/wallet/%.0f", walletID), nil)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, resp.StatusCode)
	
	var cardsResponse map[string]interface{}
	json.NewDecoder(resp.Body).Decode(&cardsResponse)
	resp.Body.Close()
	
	cards := cardsResponse["data"].([]interface{})
	assert.Len(suite.T(), cards, 1) // Should have one card
	
	// Step 6: Update card PIN
	pinPayload := map[string]interface{}{
		"pin": "5678",
	}
	
	resp, err = suite.makeInternalRequest("POST", fmt.Sprintf("/api/v1/internal/cards/%.0f/pin", cardID), pinPayload)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, resp.StatusCode)
	resp.Body.Close()
	
	// Step 7: Process card transaction
	transactionPayload := map[string]interface{}{
		"amount":           100.0,
		"transaction_type": "purchase",
		"merchant_info": map[string]interface{}{
			"merchant_name": "E2E Test Store",
			"merchant_id":   "E2E_MERCHANT_001",
			"location":      "Kampala, Uganda",
		},
	}
	
	resp, err = suite.makeInternalRequest("POST", fmt.Sprintf("/api/v1/internal/cards/%.0f/transactions", cardID), transactionPayload)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, resp.StatusCode)
	resp.Body.Close()
	
	// Step 8: Get card transactions
	resp, err = suite.makeInternalRequest("GET", fmt.Sprintf("/api/v1/internal/cards/%.0f/transactions", cardID), nil)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, resp.StatusCode)
	
	var cardTransactionsResponse map[string]interface{}
	json.NewDecoder(resp.Body).Decode(&cardTransactionsResponse)
	resp.Body.Close()
	
	cardTransactions := cardTransactionsResponse["data"].([]interface{})
	assert.Len(suite.T(), cardTransactions, 1) // Should have one transaction
}

func (suite *E2ETestSuite) TestWalletToWalletTransfer() {
	// Test Case: Transfer funds between wallets
	
	// Create sender wallet
	senderPayload := map[string]interface{}{
		"phone_number": "+256701111111",
		"wallet_type":  "individual",
	}
	
	resp, err := suite.makeInternalRequest("POST", "/api/v1/internal/wallets", senderPayload)
	assert.NoError(suite.T(), err)
	
	var senderResponse map[string]interface{}
	json.NewDecoder(resp.Body).Decode(&senderResponse)
	resp.Body.Close()
	
	senderData := senderResponse["data"].(map[string]interface{})
	senderID := senderData["id"].(float64)
	
	// Create receiver wallet
	receiverPayload := map[string]interface{}{
		"phone_number": "+256702222222",
		"wallet_type":  "individual",
	}
	
	resp, err = suite.makeInternalRequest("POST", "/api/v1/internal/wallets", receiverPayload)
	assert.NoError(suite.T(), err)
	
	var receiverResponse map[string]interface{}
	json.NewDecoder(resp.Body).Decode(&receiverResponse)
	resp.Body.Close()
	
	receiverData := receiverResponse["data"].(map[string]interface{})
	receiverID := receiverData["id"].(float64)
	
	// Topup sender wallet
	topupPayload := map[string]interface{}{
		"amount":         1000.0,
		"payment_method": "mobile_money",
		"reference":      "E2E_TRANSFER_TOPUP",
	}
	
	resp, err = suite.makeInternalRequest("POST", fmt.Sprintf("/api/v1/internal/wallets/%.0f/topup", senderID), topupPayload)
	assert.NoError(suite.T(), err)
	resp.Body.Close()
	
	// Transfer funds
	transferPayload := map[string]interface{}{
		"from_wallet_id": senderID,
		"to_wallet_id":   receiverID,
		"amount":         250.0,
		"reference":      "E2E_TRANSFER_001",
	}
	
	resp, err = suite.makeInternalRequest("POST", "/api/v1/internal/wallets/transfer", transferPayload)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, resp.StatusCode)
	resp.Body.Close()
	
	// Verify sender balance
	resp, err = suite.makeInternalRequest("GET", fmt.Sprintf("/api/v1/internal/wallets/%.0f/balance", senderID), nil)
	assert.NoError(suite.T(), err)
	
	var senderBalanceResponse map[string]interface{}
	json.NewDecoder(resp.Body).Decode(&senderBalanceResponse)
	resp.Body.Close()
	
	senderBalanceData := senderBalanceResponse["data"].(map[string]interface{})
	assert.Equal(suite.T(), 750.0, senderBalanceData["balance"].(float64))
	
	// Verify receiver balance
	resp, err = suite.makeInternalRequest("GET", fmt.Sprintf("/api/v1/internal/wallets/%.0f/balance", receiverID), nil)
	assert.NoError(suite.T(), err)
	
	var receiverBalanceResponse map[string]interface{}
	json.NewDecoder(resp.Body).Decode(&receiverBalanceResponse)
	resp.Body.Close()
	
	receiverBalanceData := receiverBalanceResponse["data"].(map[string]interface{})
	assert.Equal(suite.T(), 250.0, receiverBalanceData["balance"].(float64))
}

func (suite *E2ETestSuite) TestServiceToServiceAuthentication() {
	// Test Case: Service-to-service authentication scenarios
	
	// Test with valid internal API key
	payload := map[string]interface{}{
		"phone_number": "+256703333333",
		"wallet_type":  "business",
	}
	
	headers := map[string]string{
		"X-Internal-Key":  internalAPIKey,
		"X-Service-Name":  "payment-engine",
		"X-User-ID":       "business-user-456",
	}
	
	resp, err := suite.makeRequest("POST", "/api/v1/internal/wallets", payload, headers)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusCreated, resp.StatusCode)
	resp.Body.Close()
	
	// Test with invalid internal API key
	invalidHeaders := map[string]string{
		"X-Internal-Key": "invalid-key",
		"X-Service-Name": "payment-engine",
	}
	
	resp, err = suite.makeRequest("POST", "/api/v1/internal/wallets", payload, invalidHeaders)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusUnauthorized, resp.StatusCode)
	resp.Body.Close()
	
	// Test without internal API key
	noKeyHeaders := map[string]string{
		"X-Service-Name": "payment-engine",
	}
	
	resp, err = suite.makeRequest("POST", "/api/v1/internal/wallets", payload, noKeyHeaders)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusUnauthorized, resp.StatusCode)
	resp.Body.Close()
}

func (suite *E2ETestSuite) TestHealthAndStatus() {
	// Test health endpoint
	resp, err := suite.makeRequest("GET", "/health", nil, map[string]string{})
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, resp.StatusCode)
	
	var healthResponse map[string]interface{}
	json.NewDecoder(resp.Body).Decode(&healthResponse)
	resp.Body.Close()
	
	assert.Equal(suite.T(), "healthy", healthResponse["status"])
}

func TestE2ETestSuite(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping E2E tests in short mode")
	}
	
	suite.Run(t, new(E2ETestSuite))
}
