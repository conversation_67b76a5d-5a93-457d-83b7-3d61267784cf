package controllers

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"wallet-platform/internal/services"

	"github.com/gin-gonic/gin"
)

// PayCardController handles PayCard-related HTTP requests
type PayCardController struct {
	container *services.Container
}

// NewPayCardController creates a new PayCard controller
func NewPayCardController(container *services.Container) *PayCardController {
	return &PayCardController{
		container: container,
	}
}

// CreateCard creates a new PayCard
func (pc *PayCardController) CreateCard(c *gin.Context) {
	var req CreateCardRequest
	if err := c.ShouldBindJ<PERSON>N(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	_, err := pc.container.PayCardService.CreateCard(req.WalletID, req.CardType, "Card Holder")
	if err != nil {
		status := http.StatusInternalServerError
		code := "CARD_CREATION_FAILED"

		if err.Error() == "wallet not found" {
			status = http.StatusNotFound
			code = "WALLET_NOT_FOUND"
		}

		c.JSON(status, ErrorResponse{
			Error:   code,
			Message: err.Error(),
			Code:    fmt.Sprintf("%d", status),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "PayCard created successfully",
	})
}

// GetMyCards gets all cards for the authenticated user
func (pc *PayCardController) GetMyCards(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
			Code:    "401",
		})
		return
	}

	// Get wallet ID from user context
	walletID, exists := c.Get("wallet_id")
	if !exists {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "MISSING_WALLET",
			Message: "Wallet ID not found in user context",
			Code:    "400",
		})
		return
	}

	cards, err := pc.container.PayCardService.GetCardsByWallet(walletID.(uint))
	if err != nil {
		pc.container.Logger.LogError(err, map[string]interface{}{
			"action":    "get_my_cards",
			"user_id":   userID,
			"wallet_id": walletID,
		})
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "FETCH_FAILED",
			Message: "Failed to fetch cards",
			Code:    "500",
		})
		return
	}

	pc.container.Logger.LogPayCard("", fmt.Sprintf("%d", walletID.(uint)), "cards_retrieved", 0, fmt.Sprintf("Retrieved %d cards", len(cards)))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Cards retrieved successfully",
		"data": gin.H{
			"cards": cards,
			"total": len(cards),
		},
	})
}

// GetCard gets card details by card ID
func (pc *PayCardController) GetCard(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_ID",
			Message: "Invalid card ID format",
			Code:    "400",
		})
		return
	}

	// Get user context for authorization
	walletID, exists := c.Get("wallet_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
			Code:    "401",
		})
		return
	}

	card, err := pc.container.PayCardService.GetCard(uint(id))
	if err != nil {
		pc.container.Logger.LogError(err, map[string]interface{}{
			"action":  "get_card",
			"card_id": id,
		})
		c.JSON(http.StatusNotFound, ErrorResponse{
			Error:   "CARD_NOT_FOUND",
			Message: "Card not found",
			Code:    "404",
		})
		return
	}

	// Verify card belongs to user's wallet
	if card.WalletID != walletID.(uint) {
		c.JSON(http.StatusForbidden, ErrorResponse{
			Error:   "ACCESS_DENIED",
			Message: "You don't have access to this card",
			Code:    "403",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Card retrieved successfully",
		"data":    card,
	})
}

// UpdateCard updates card information
func (pc *PayCardController) UpdateCard(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_ID",
			Message: "Invalid card ID format",
			Code:    "400",
		})
		return
	}

	var req UpdateCardRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	// Convert request to updates map
	updates := make(map[string]interface{})
	if req.SpendingLimit != nil {
		updates["spending_limit"] = *req.SpendingLimit
	}
	if req.Status != "" {
		updates["status"] = req.Status
	}
	if req.Metadata != nil {
		updates["metadata"] = req.Metadata
	}

	// Get user context for authorization
	walletID, exists := c.Get("wallet_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
			Code:    "401",
		})
		return
	}

	// Verify card belongs to user's wallet
	existingCard, err := pc.container.PayCardService.GetCard(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{
			Error:   "CARD_NOT_FOUND",
			Message: "Card not found",
			Code:    "404",
		})
		return
	}

	if existingCard.WalletID != walletID.(uint) {
		c.JSON(http.StatusForbidden, ErrorResponse{
			Error:   "ACCESS_DENIED",
			Message: "You don't have access to this card",
			Code:    "403",
		})
		return
	}

	card, err := pc.container.PayCardService.UpdateCard(uint(id), updates)
	if err != nil {
		pc.container.Logger.LogError(err, map[string]interface{}{
			"action":  "update_card",
			"card_id": id,
			"updates": updates,
		})
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "UPDATE_FAILED",
			Message: "Failed to update card",
			Code:    "500",
		})
		return
	}

	pc.container.Logger.LogPayCard(fmt.Sprintf("%d", id), fmt.Sprintf("%d", walletID.(uint)), "card_updated", 0, "Card details updated")

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Card updated successfully",
		"data":    card,
	})
}

// UpdateCardPIN updates the PIN for a card
func (pc *PayCardController) UpdateCardPIN(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_ID",
			Message: "Invalid card ID format",
			Code:    "400",
		})
		return
	}

	var req UpdatePINRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	// Get user context for authorization
	walletID, exists := c.Get("wallet_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
			Code:    "401",
		})
		return
	}

	// Verify card belongs to user's wallet
	card, err := pc.container.PayCardService.GetCard(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{
			Error:   "CARD_NOT_FOUND",
			Message: "Card not found",
			Code:    "404",
		})
		return
	}

	if card.WalletID != walletID.(uint) {
		c.JSON(http.StatusForbidden, ErrorResponse{
			Error:   "ACCESS_DENIED",
			Message: "You don't have access to this card",
			Code:    "403",
		})
		return
	}

	err = pc.container.PayCardService.UpdatePIN(uint(id), req.CurrentPIN, req.NewPIN)
	if err != nil {
		pc.container.Logger.LogError(err, map[string]interface{}{
			"action":  "update_pin",
			"card_id": id,
		})

		// Handle specific error cases
		if err.Error() == "invalid current PIN" {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Error:   "INVALID_PIN",
				Message: "Current PIN is incorrect",
				Code:    "400",
			})
			return
		}

		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "PIN_UPDATE_FAILED",
			Message: "Failed to update PIN",
			Code:    "500",
		})
		return
	}

	pc.container.Logger.LogPayCard(fmt.Sprintf("%d", id), fmt.Sprintf("%d", walletID.(uint)), "pin_updated", 0, "PIN updated successfully")

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "PIN updated successfully",
	})
}

// RequestPhysicalCard requests a physical card
func (pc *PayCardController) RequestPhysicalCard(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_ID",
			Message: "Invalid card ID format",
			Code:    "400",
		})
		return
	}

	var req struct {
		Design string `json:"design"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		// Set default design if not provided
		req.Design = "standard"
	}

	// Get user context for authorization
	walletID, exists := c.Get("wallet_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
			Code:    "401",
		})
		return
	}

	// Verify card belongs to user's wallet
	card, err := pc.container.PayCardService.GetCard(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{
			Error:   "CARD_NOT_FOUND",
			Message: "Card not found",
			Code:    "404",
		})
		return
	}

	if card.WalletID != walletID.(uint) {
		c.JSON(http.StatusForbidden, ErrorResponse{
			Error:   "ACCESS_DENIED",
			Message: "You don't have access to this card",
			Code:    "403",
		})
		return
	}

	err = pc.container.PayCardService.RequestPhysicalCard(uint(id), req.Design)
	if err != nil {
		pc.container.Logger.LogError(err, map[string]interface{}{
			"action":  "request_physical_card",
			"card_id": id,
			"design":  req.Design,
		})

		if err.Error() == "physical card already issued" {
			c.JSON(http.StatusConflict, ErrorResponse{
				Error:   "ALREADY_ISSUED",
				Message: "Physical card already issued for this card",
				Code:    "409",
			})
			return
		}

		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "REQUEST_FAILED",
			Message: "Failed to request physical card",
			Code:    "500",
		})
		return
	}

	pc.container.Logger.LogPayCard(fmt.Sprintf("%d", id), fmt.Sprintf("%d", walletID.(uint)), "physical_card_requested", 0, fmt.Sprintf("Design: %s", req.Design))

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "Physical card request submitted successfully",
	})
}

// ActivatePhysicalCard activates a physical card
func (pc *PayCardController) ActivatePhysicalCard(c *gin.Context) {
	var req struct {
		SerialNumber   string `json:"serial_number" binding:"required"`
		ActivationCode string `json:"activation_code" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	// Get user context for logging
	userID, _ := c.Get("user_id")

	err := pc.container.PayCardService.ActivatePhysicalCard(req.SerialNumber, req.ActivationCode)
	if err != nil {
		pc.container.Logger.LogError(err, map[string]interface{}{
			"action":        "activate_physical_card",
			"serial_number": req.SerialNumber,
			"user_id":       userID,
		})

		if err.Error() == "card not found" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:   "CARD_NOT_FOUND",
				Message: "Card with this serial number not found",
				Code:    "404",
			})
			return
		}

		if err.Error() == "invalid activation code" {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Error:   "INVALID_CODE",
				Message: "Invalid activation code",
				Code:    "400",
			})
			return
		}

		if err.Error() == "card already activated" {
			c.JSON(http.StatusConflict, ErrorResponse{
				Error:   "ALREADY_ACTIVATED",
				Message: "Card is already activated",
				Code:    "409",
			})
			return
		}

		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "ACTIVATION_FAILED",
			Message: "Failed to activate physical card",
			Code:    "500",
		})
		return
	}

	pc.container.Logger.LogPayCard("", "", "physical_card_activated", 0, fmt.Sprintf("Serial: %s", req.SerialNumber))

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "Physical card activated successfully",
	})
}

// GenerateCardToken generates a card token
func (pc *PayCardController) GenerateCardToken(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_ID",
			Message: "Invalid card ID format",
			Code:    "400",
		})
		return
	}

	var req struct {
		TokenType string `json:"token_type" binding:"required"`
		ExpiresIn int    `json:"expires_in"` // minutes
		MaxUsage  int    `json:"max_usage"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	// Set defaults
	if req.ExpiresIn == 0 {
		req.ExpiresIn = 60 // 1 hour default
	}
	if req.MaxUsage == 0 {
		req.MaxUsage = 1 // Single use default
	}

	// Get user context for authorization
	walletID, exists := c.Get("wallet_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
			Code:    "401",
		})
		return
	}

	// Verify card belongs to user's wallet
	card, err := pc.container.PayCardService.GetCard(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{
			Error:   "CARD_NOT_FOUND",
			Message: "Card not found",
			Code:    "404",
		})
		return
	}

	if card.WalletID != walletID.(uint) {
		c.JSON(http.StatusForbidden, ErrorResponse{
			Error:   "ACCESS_DENIED",
			Message: "You don't have access to this card",
			Code:    "403",
		})
		return
	}

	token, err := pc.container.PayCardService.GenerateCardToken(
		uint(id),
		req.TokenType,
		time.Duration(req.ExpiresIn)*time.Minute,
		req.MaxUsage,
	)
	if err != nil {
		pc.container.Logger.LogError(err, map[string]interface{}{
			"action":     "generate_card_token",
			"card_id":    id,
			"token_type": req.TokenType,
		})
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "TOKEN_GENERATION_FAILED",
			Message: "Failed to generate card token",
			Code:    "500",
		})
		return
	}

	pc.container.Logger.LogPayCard(fmt.Sprintf("%d", id), fmt.Sprintf("%d", walletID.(uint)), "token_generated", 0, fmt.Sprintf("Type: %s", req.TokenType))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Card token generated successfully",
		"data": gin.H{
			"token":      token.TokenValue,
			"token_type": token.TokenType,
			"expires_at": token.ExpiresAt,
			"max_usage":  token.MaxUsage,
		},
	})
}

// AddMerchantToWhitelist adds a merchant to whitelist
func (pc *PayCardController) AddMerchantToWhitelist(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_ID",
			Message: "Invalid card ID format",
			Code:    "400",
		})
		return
	}

	var req struct {
		MerchantID   string `json:"merchant_id" binding:"required"`
		MerchantName string `json:"merchant_name" binding:"required"`
		Category     string `json:"category"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	// Get user context for authorization
	walletID, exists := c.Get("wallet_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
			Code:    "401",
		})
		return
	}

	// Verify card belongs to user's wallet
	card, err := pc.container.PayCardService.GetCard(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{
			Error:   "CARD_NOT_FOUND",
			Message: "Card not found",
			Code:    "404",
		})
		return
	}

	if card.WalletID != walletID.(uint) {
		c.JSON(http.StatusForbidden, ErrorResponse{
			Error:   "ACCESS_DENIED",
			Message: "You don't have access to this card",
			Code:    "403",
		})
		return
	}

	// Set default category if not provided
	if req.Category == "" {
		req.Category = "general"
	}

	err = pc.container.PayCardService.AddMerchantToWhitelist(uint(id), req.MerchantName, req.MerchantID, req.Category)
	if err != nil {
		pc.container.Logger.LogError(err, map[string]interface{}{
			"action":        "add_merchant_whitelist",
			"card_id":       id,
			"merchant_id":   req.MerchantID,
			"merchant_name": req.MerchantName,
		})
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "WHITELIST_FAILED",
			Message: "Failed to add merchant to whitelist",
			Code:    "500",
		})
		return
	}

	pc.container.Logger.LogPayCard(fmt.Sprintf("%d", id), fmt.Sprintf("%d", walletID.(uint)), "merchant_whitelisted", 0, fmt.Sprintf("Merchant: %s", req.MerchantName))

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "Merchant added to whitelist successfully",
	})
}

// SetSpendingControl sets spending controls
func (pc *PayCardController) SetSpendingControl(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_ID",
			Message: "Invalid card ID format",
			Code:    "400",
		})
		return
	}

	var req struct {
		ControlType          string                 `json:"control_type" binding:"required"`
		DailyLimit           *float64               `json:"daily_limit"`
		MonthlyLimit         *float64               `json:"monthly_limit"`
		MaxAmount            *float64               `json:"max_amount"`
		MaxTransactions      *int                   `json:"max_transactions"`
		Categories           []string               `json:"categories"`
		TimeRestrictions     map[string]interface{} `json:"time_restrictions"`
		LocationRestrictions map[string]interface{} `json:"location_restrictions"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	// Get user context for authorization
	walletID, exists := c.Get("wallet_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
			Code:    "401",
		})
		return
	}

	// Verify card belongs to user's wallet
	card, err := pc.container.PayCardService.GetCard(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{
			Error:   "CARD_NOT_FOUND",
			Message: "Card not found",
			Code:    "404",
		})
		return
	}

	if card.WalletID != walletID.(uint) {
		c.JSON(http.StatusForbidden, ErrorResponse{
			Error:   "ACCESS_DENIED",
			Message: "You don't have access to this card",
			Code:    "403",
		})
		return
	}

	// Set defaults
	controlValue := 0.0
	maxAmount := 0.0
	maxTransactions := 0

	if req.DailyLimit != nil {
		controlValue = *req.DailyLimit
	}
	if req.MaxAmount != nil {
		maxAmount = *req.MaxAmount
	}
	if req.MaxTransactions != nil {
		maxTransactions = *req.MaxTransactions
	}

	// Set default time and location restrictions if not provided
	if req.TimeRestrictions == nil {
		req.TimeRestrictions = make(map[string]interface{})
	}
	if req.LocationRestrictions == nil {
		req.LocationRestrictions = make(map[string]interface{})
	}

	err = pc.container.PayCardService.SetSpendingControl(
		uint(id),
		req.ControlType,
		controlValue,
		maxAmount,
		maxTransactions,
		req.TimeRestrictions,
		req.LocationRestrictions,
	)
	if err != nil {
		pc.container.Logger.LogError(err, map[string]interface{}{
			"action":       "set_spending_control",
			"card_id":      id,
			"control_type": req.ControlType,
		})
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "CONTROL_UPDATE_FAILED",
			Message: "Failed to update spending controls",
			Code:    "500",
		})
		return
	}

	pc.container.Logger.LogPayCard(fmt.Sprintf("%d", id), fmt.Sprintf("%d", walletID.(uint)), "spending_control_updated", 0, fmt.Sprintf("Type: %s", req.ControlType))

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "Spending controls updated successfully",
	})
}

// ProcessContactlessPayment processes contactless payment
func (pc *PayCardController) ProcessContactlessPayment(c *gin.Context) {
	var req struct {
		CardToken    string  `json:"card_token" binding:"required"`
		Amount       float64 `json:"amount" binding:"required,gt=0"`
		MerchantID   string  `json:"merchant_id" binding:"required"`
		MerchantName string  `json:"merchant_name" binding:"required"`
		Description  string  `json:"description"`
		Category     string  `json:"category"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	// Get user context for logging
	userID, _ := c.Get("user_id")

	// Prepare merchant info
	merchantInfo := map[string]interface{}{
		"name":        req.MerchantName,
		"id":          req.MerchantID,
		"category":    req.Category,
		"description": req.Description,
	}

	// For contactless payment, we need to resolve the card token to card number
	// This is a simplified implementation - in production, you'd validate the token first
	transaction, err := pc.container.PayCardService.ProcessContactlessPayment(req.CardToken, req.Amount, merchantInfo)
	if err != nil {
		pc.container.Logger.LogError(err, map[string]interface{}{
			"action":      "process_contactless_payment",
			"card_token":  req.CardToken,
			"amount":      req.Amount,
			"merchant_id": req.MerchantID,
			"user_id":     userID,
		})

		// Handle specific error cases
		if err.Error() == "card not found" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:   "CARD_NOT_FOUND",
				Message: "Invalid card token",
				Code:    "404",
			})
			return
		}

		if err.Error() == "card is not active" {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Error:   "CARD_INACTIVE",
				Message: "Card is not active",
				Code:    "400",
			})
			return
		}

		if err.Error() == "insufficient wallet balance" {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Error:   "INSUFFICIENT_BALANCE",
				Message: "Insufficient wallet balance",
				Code:    "400",
			})
			return
		}

		if err.Error() == "daily spending limit exceeded" || err.Error() == "monthly spending limit exceeded" {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Error:   "SPENDING_LIMIT_EXCEEDED",
				Message: err.Error(),
				Code:    "400",
			})
			return
		}

		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "PAYMENT_FAILED",
			Message: "Failed to process contactless payment",
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Contactless payment processed successfully",
		"data":    transaction,
	})
}

// BlockCard blocks a card
func (pc *PayCardController) BlockCard(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_ID",
			Message: "Invalid card ID format",
			Code:    "400",
		})
		return
	}

	var req struct {
		Reason string `json:"reason" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	// Get user context for authorization
	walletID, exists := c.Get("wallet_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
			Code:    "401",
		})
		return
	}

	// Verify card belongs to user's wallet
	card, err := pc.container.PayCardService.GetCard(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{
			Error:   "CARD_NOT_FOUND",
			Message: "Card not found",
			Code:    "404",
		})
		return
	}

	if card.WalletID != walletID.(uint) {
		c.JSON(http.StatusForbidden, ErrorResponse{
			Error:   "ACCESS_DENIED",
			Message: "You don't have access to this card",
			Code:    "403",
		})
		return
	}

	err = pc.container.PayCardService.BlockCard(uint(id), req.Reason)
	if err != nil {
		pc.container.Logger.LogError(err, map[string]interface{}{
			"action":  "block_card",
			"card_id": id,
			"reason":  req.Reason,
		})
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "BLOCK_FAILED",
			Message: "Failed to block card",
			Code:    "500",
		})
		return
	}

	pc.container.Logger.LogPayCard(fmt.Sprintf("%d", id), fmt.Sprintf("%d", walletID.(uint)), "card_blocked", 0, req.Reason)

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "Card blocked successfully",
	})
}

// UnblockCard unblocks a card
func (pc *PayCardController) UnblockCard(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_ID",
			Message: "Invalid card ID format",
			Code:    "400",
		})
		return
	}

	var req struct {
		Reason string `json:"reason"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	// Get user context for authorization
	walletID, exists := c.Get("wallet_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
			Code:    "401",
		})
		return
	}

	// Verify card belongs to user's wallet
	card, err := pc.container.PayCardService.GetCard(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{
			Error:   "CARD_NOT_FOUND",
			Message: "Card not found",
			Code:    "404",
		})
		return
	}

	if card.WalletID != walletID.(uint) {
		c.JSON(http.StatusForbidden, ErrorResponse{
			Error:   "ACCESS_DENIED",
			Message: "You don't have access to this card",
			Code:    "403",
		})
		return
	}

	err = pc.container.PayCardService.UnblockCard(uint(id))
	if err != nil {
		pc.container.Logger.LogError(err, map[string]interface{}{
			"action":  "unblock_card",
			"card_id": id,
		})
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "UNBLOCK_FAILED",
			Message: "Failed to unblock card",
			Code:    "500",
		})
		return
	}

	pc.container.Logger.LogPayCard(fmt.Sprintf("%d", id), fmt.Sprintf("%d", walletID.(uint)), "card_unblocked", 0, req.Reason)

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "Card unblocked successfully",
	})
}

// ProcessCardTransaction processes a card transaction
func (pc *PayCardController) ProcessCardTransaction(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_ID",
			Message: "Invalid card ID format",
			Code:    "400",
		})
		return
	}

	var req struct {
		Amount       float64 `json:"amount" binding:"required,gt=0"`
		MerchantID   string  `json:"merchant_id" binding:"required"`
		MerchantName string  `json:"merchant_name" binding:"required"`
		Description  string  `json:"description"`
		Currency     string  `json:"currency" binding:"required"`
		Category     string  `json:"category"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	// Get user context for authorization
	walletID, exists := c.Get("wallet_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
			Code:    "401",
		})
		return
	}

	// Verify card belongs to user's wallet
	card, err := pc.container.PayCardService.GetCard(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{
			Error:   "CARD_NOT_FOUND",
			Message: "Card not found",
			Code:    "404",
		})
		return
	}

	if card.WalletID != walletID.(uint) {
		c.JSON(http.StatusForbidden, ErrorResponse{
			Error:   "ACCESS_DENIED",
			Message: "You don't have access to this card",
			Code:    "403",
		})
		return
	}

	// Prepare merchant info
	merchantInfo := map[string]interface{}{
		"name":        req.MerchantName,
		"id":          req.MerchantID,
		"category":    req.Category,
		"description": req.Description,
	}

	// Process transaction using card number
	transaction, err := pc.container.PayCardService.ProcessTransaction(card.CardNumber, req.Amount, merchantInfo)
	if err != nil {
		pc.container.Logger.LogError(err, map[string]interface{}{
			"action":      "process_card_transaction",
			"card_id":     id,
			"amount":      req.Amount,
			"merchant_id": req.MerchantID,
		})

		// Handle specific error cases
		if err.Error() == "insufficient wallet balance" {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Error:   "INSUFFICIENT_BALANCE",
				Message: "Insufficient wallet balance",
				Code:    "400",
			})
			return
		}

		if err.Error() == "daily spending limit exceeded" || err.Error() == "monthly spending limit exceeded" {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Error:   "SPENDING_LIMIT_EXCEEDED",
				Message: err.Error(),
				Code:    "400",
			})
			return
		}

		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "TRANSACTION_FAILED",
			Message: "Failed to process transaction",
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Transaction processed successfully",
		"data":    transaction,
	})
}

// GetCardTransactions gets card transaction history
func (pc *PayCardController) GetCardTransactions(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_ID",
			Message: "Invalid card ID format",
			Code:    "400",
		})
		return
	}

	// Get user context for authorization
	walletID, exists := c.Get("wallet_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
			Code:    "401",
		})
		return
	}

	// Verify card belongs to user's wallet
	card, err := pc.container.PayCardService.GetCard(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{
			Error:   "CARD_NOT_FOUND",
			Message: "Card not found",
			Code:    "404",
		})
		return
	}

	if card.WalletID != walletID.(uint) {
		c.JSON(http.StatusForbidden, ErrorResponse{
			Error:   "ACCESS_DENIED",
			Message: "You don't have access to this card",
			Code:    "403",
		})
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")
	transactionType := c.Query("type")

	// Build filters
	filters := make(map[string]interface{})
	if startDate != "" {
		filters["start_date"] = startDate
	}
	if endDate != "" {
		filters["end_date"] = endDate
	}
	if transactionType != "" {
		filters["transaction_type"] = transactionType
	}
	filters["page"] = page
	filters["limit"] = limit

	transactions, err := pc.container.PayCardService.GetTransactionHistory(uint(id), filters)
	if err != nil {
		pc.container.Logger.LogError(err, map[string]interface{}{
			"action":  "get_card_transactions",
			"card_id": id,
			"filters": filters,
		})
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "FETCH_FAILED",
			Message: "Failed to fetch transactions",
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Card transactions retrieved successfully",
		"data": gin.H{
			"transactions": transactions,
			"page":         page,
			"limit":        limit,
			"total":        len(transactions),
			"start_date":   startDate,
			"end_date":     endDate,
		},
	})
}

// Request/Response types
type CreateCardRequest struct {
	WalletID      uint    `json:"wallet_id" binding:"required"`
	CardType      string  `json:"card_type" binding:"required"`
	SpendingLimit float64 `json:"spending_limit"`
}

type UpdateCardRequest struct {
	SpendingLimit *float64               `json:"spending_limit,omitempty"`
	Status        string                 `json:"status,omitempty"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
}

type UpdatePINRequest struct {
	CurrentPIN string `json:"current_pin" binding:"required,len=4"`
	NewPIN     string `json:"new_pin" binding:"required,len=4"`
}

// Enhanced card management endpoints

// GetCardCount returns the number of cards for a wallet
func (pc *PayCardController) GetCardCount(c *gin.Context) {
	walletIDStr := c.Param("wallet_id")
	walletID, err := strconv.ParseUint(walletIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_WALLET_ID",
			Message: "Invalid wallet ID format",
			Code:    "400",
		})
		return
	}

	count, err := pc.container.PayCardService.GetCardCount(uint(walletID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "CARD_COUNT_FAILED",
			Message: err.Error(),
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Card count retrieved successfully",
		"data": map[string]interface{}{
			"wallet_id":  walletID,
			"card_count": count,
		},
	})
}

// SetSpendingLimits updates spending limits for a card
func (pc *PayCardController) SetSpendingLimits(c *gin.Context) {
	cardIDStr := c.Param("id")
	cardID, err := strconv.ParseUint(cardIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_CARD_ID",
			Message: "Invalid card ID format",
			Code:    "400",
		})
		return
	}

	var req struct {
		DailyLimit       float64 `json:"daily_limit" binding:"required,min=0"`
		MonthlyLimit     float64 `json:"monthly_limit" binding:"required,min=0"`
		TransactionLimit float64 `json:"transaction_limit" binding:"required,min=0"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	err = pc.container.PayCardService.SetSpendingLimits(uint(cardID), req.DailyLimit, req.MonthlyLimit, req.TransactionLimit)
	if err != nil {
		status := http.StatusInternalServerError
		code := "LIMITS_UPDATE_FAILED"

		if err.Error() == "card not found" {
			status = http.StatusNotFound
			code = "CARD_NOT_FOUND"
		}

		c.JSON(status, ErrorResponse{
			Error:   code,
			Message: err.Error(),
			Code:    fmt.Sprintf("%d", status),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Spending limits updated successfully",
		"data": map[string]interface{}{
			"card_id":           cardID,
			"daily_limit":       req.DailyLimit,
			"monthly_limit":     req.MonthlyLimit,
			"transaction_limit": req.TransactionLimit,
		},
	})
}

// SetMerchantRestrictions sets merchant restrictions for a card
func (pc *PayCardController) SetMerchantRestrictions(c *gin.Context) {
	cardIDStr := c.Param("id")
	cardID, err := strconv.ParseUint(cardIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_CARD_ID",
			Message: "Invalid card ID format",
			Code:    "400",
		})
		return
	}

	var req struct {
		BlacklistedCategories []string `json:"blacklisted_categories"`
		WhitelistedMerchants  []string `json:"whitelisted_merchants"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	err = pc.container.PayCardService.SetMerchantRestrictions(uint(cardID), req.BlacklistedCategories, req.WhitelistedMerchants)
	if err != nil {
		status := http.StatusInternalServerError
		code := "RESTRICTIONS_UPDATE_FAILED"

		if err.Error() == "card not found" {
			status = http.StatusNotFound
			code = "CARD_NOT_FOUND"
		}

		c.JSON(status, ErrorResponse{
			Error:   code,
			Message: err.Error(),
			Code:    fmt.Sprintf("%d", status),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Merchant restrictions updated successfully",
		"data": map[string]interface{}{
			"card_id":                cardID,
			"blacklisted_categories": req.BlacklistedCategories,
			"whitelisted_merchants":  req.WhitelistedMerchants,
		},
	})
}

// GetCardSecuritySettings retrieves security settings for a card
func (pc *PayCardController) GetCardSecuritySettings(c *gin.Context) {
	cardIDStr := c.Param("id")
	cardID, err := strconv.ParseUint(cardIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_CARD_ID",
			Message: "Invalid card ID format",
			Code:    "400",
		})
		return
	}

	settings, err := pc.container.PayCardService.GetCardSecuritySettings(uint(cardID))
	if err != nil {
		status := http.StatusInternalServerError
		code := "SETTINGS_FETCH_FAILED"

		if err.Error() == "card not found" {
			status = http.StatusNotFound
			code = "CARD_NOT_FOUND"
		}

		c.JSON(status, ErrorResponse{
			Error:   code,
			Message: err.Error(),
			Code:    fmt.Sprintf("%d", status),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Card security settings retrieved successfully",
		"data":    settings,
	})
}

// GetCardUsageAnalytics provides usage analytics for a card
func (pc *PayCardController) GetCardUsageAnalytics(c *gin.Context) {
	cardIDStr := c.Param("id")
	cardID, err := strconv.ParseUint(cardIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_CARD_ID",
			Message: "Invalid card ID format",
			Code:    "400",
		})
		return
	}

	daysStr := c.DefaultQuery("days", "30")
	days, err := strconv.Atoi(daysStr)
	if err != nil || days <= 0 {
		days = 30
	}

	analytics, err := pc.container.PayCardService.GetCardUsageAnalytics(uint(cardID), days)
	if err != nil {
		status := http.StatusInternalServerError
		code := "ANALYTICS_FETCH_FAILED"

		if err.Error() == "card not found" {
			status = http.StatusNotFound
			code = "CARD_NOT_FOUND"
		}

		c.JSON(status, ErrorResponse{
			Error:   code,
			Message: err.Error(),
			Code:    fmt.Sprintf("%d", status),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Card usage analytics retrieved successfully",
		"data":    analytics,
	})
}

// SetTimeRestrictions sets time-based restrictions for a card
func (pc *PayCardController) SetTimeRestrictions(c *gin.Context) {
	cardIDStr := c.Param("id")
	cardID, err := strconv.ParseUint(cardIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_CARD_ID",
			Message: "Invalid card ID format",
			Code:    "400",
		})
		return
	}

	var req struct {
		BusinessHoursOnly  bool                `json:"business_hours_only"`
		NoWeekends         bool                `json:"no_weekends"`
		AllowedTimeWindows []map[string]string `json:"allowed_time_windows"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	err = pc.container.PayCardService.SetTimeRestrictions(uint(cardID), req.BusinessHoursOnly, req.NoWeekends, req.AllowedTimeWindows)
	if err != nil {
		status := http.StatusInternalServerError
		code := "TIME_RESTRICTIONS_UPDATE_FAILED"

		if err.Error() == "card not found" {
			status = http.StatusNotFound
			code = "CARD_NOT_FOUND"
		}

		c.JSON(status, ErrorResponse{
			Error:   code,
			Message: err.Error(),
			Code:    fmt.Sprintf("%d", status),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Time restrictions updated successfully",
		"data": map[string]interface{}{
			"card_id":              cardID,
			"business_hours_only":  req.BusinessHoursOnly,
			"no_weekends":          req.NoWeekends,
			"allowed_time_windows": req.AllowedTimeWindows,
		},
	})
}

// EnableCardFeature enables a specific feature for a card
func (pc *PayCardController) EnableCardFeature(c *gin.Context) {
	cardIDStr := c.Param("id")
	cardID, err := strconv.ParseUint(cardIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_CARD_ID",
			Message: "Invalid card ID format",
			Code:    "400",
		})
		return
	}

	feature := c.Param("feature")
	if feature == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_FEATURE",
			Message: "Feature parameter is required",
			Code:    "400",
		})
		return
	}

	err = pc.container.PayCardService.EnableCardFeature(uint(cardID), feature)
	if err != nil {
		status := http.StatusInternalServerError
		code := "FEATURE_ENABLE_FAILED"

		if err.Error() == "card not found" {
			status = http.StatusNotFound
			code = "CARD_NOT_FOUND"
		} else if err.Error() == "invalid feature" {
			status = http.StatusBadRequest
			code = "INVALID_FEATURE"
		}

		c.JSON(status, ErrorResponse{
			Error:   code,
			Message: err.Error(),
			Code:    fmt.Sprintf("%d", status),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": fmt.Sprintf("Feature '%s' enabled successfully", feature),
		"data": map[string]interface{}{
			"card_id": cardID,
			"feature": feature,
			"enabled": true,
		},
	})
}

// DisableCardFeature disables a specific feature for a card
func (pc *PayCardController) DisableCardFeature(c *gin.Context) {
	cardIDStr := c.Param("id")
	cardID, err := strconv.ParseUint(cardIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_CARD_ID",
			Message: "Invalid card ID format",
			Code:    "400",
		})
		return
	}

	feature := c.Param("feature")
	if feature == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_FEATURE",
			Message: "Feature parameter is required",
			Code:    "400",
		})
		return
	}

	err = pc.container.PayCardService.DisableCardFeature(uint(cardID), feature)
	if err != nil {
		status := http.StatusInternalServerError
		code := "FEATURE_DISABLE_FAILED"

		if err.Error() == "card not found" {
			status = http.StatusNotFound
			code = "CARD_NOT_FOUND"
		} else if err.Error() == "invalid feature" {
			status = http.StatusBadRequest
			code = "INVALID_FEATURE"
		}

		c.JSON(status, ErrorResponse{
			Error:   code,
			Message: err.Error(),
			Code:    fmt.Sprintf("%d", status),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": fmt.Sprintf("Feature '%s' disabled successfully", feature),
		"data": map[string]interface{}{
			"card_id": cardID,
			"feature": feature,
			"enabled": false,
		},
	})
}

// GetActiveCardsByType retrieves active cards of a specific type for a wallet
func (pc *PayCardController) GetActiveCardsByType(c *gin.Context) {
	walletIDStr := c.Param("wallet_id")
	walletID, err := strconv.ParseUint(walletIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_WALLET_ID",
			Message: "Invalid wallet ID format",
			Code:    "400",
		})
		return
	}

	cardType := c.Query("type")
	if cardType == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "MISSING_CARD_TYPE",
			Message: "Card type parameter is required",
			Code:    "400",
		})
		return
	}

	cards, err := pc.container.PayCardService.GetActiveCardsByType(uint(walletID), cardType)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "CARDS_FETCH_FAILED",
			Message: err.Error(),
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Active cards retrieved successfully",
		"data": map[string]interface{}{
			"wallet_id": walletID,
			"card_type": cardType,
			"cards":     cards,
			"count":     len(cards),
		},
	})
}

// QR Code Payment Methods

// GenerateQRCode generates QR code data for a card
func (pc *PayCardController) GenerateQRCode(c *gin.Context) {
	cardIDStr := c.Param("id")
	cardID, err := strconv.ParseUint(cardIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_ID",
			Message: "Invalid card ID format",
			Code:    "400",
		})
		return
	}

	// Get user context for authorization
	walletID, exists := c.Get("wallet_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
			Code:    "401",
		})
		return
	}

	// Verify card belongs to user's wallet
	card, err := pc.container.PayCardService.GetCard(uint(cardID))
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{
			Error:   "CARD_NOT_FOUND",
			Message: "Card not found",
			Code:    "404",
		})
		return
	}

	if card.WalletID != walletID.(uint) {
		c.JSON(http.StatusForbidden, ErrorResponse{
			Error:   "ACCESS_DENIED",
			Message: "You don't have access to this card",
			Code:    "403",
		})
		return
	}

	qrCodeData, err := pc.container.PayCardService.GenerateQRCodeData(uint(cardID))
	if err != nil {
		pc.container.Logger.LogError(err, map[string]interface{}{
			"action":  "generate_qr_code",
			"card_id": cardID,
		})
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "QR_GENERATION_FAILED",
			Message: "Failed to generate QR code: " + err.Error(),
			Code:    "500",
		})
		return
	}

	pc.container.Logger.LogPayCard(fmt.Sprintf("%d", cardID), fmt.Sprintf("%d", walletID.(uint)), "qr_code_generated", 0, "QR code generated for payment")

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "QR code generated successfully",
		"data": gin.H{
			"qr_code_data": qrCodeData,
			"card_id":      cardID,
			"generated_at": time.Now(),
		},
	})
}

// ValidateQRCode validates QR code data
func (pc *PayCardController) ValidateQRCode(c *gin.Context) {
	var request struct {
		QRCodeData string `json:"qr_code_data" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	qrData, err := pc.container.PayCardService.ValidateQRCodeData(request.QRCodeData)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "QR_VALIDATION_FAILED",
			Message: "QR code validation failed: " + err.Error(),
			Code:    "400",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "QR code validated successfully",
		"data": gin.H{
			"valid":       true,
			"card_id":     qrData.CardID,
			"wallet_id":   qrData.WalletID,
			"card_type":   qrData.CardType,
			"holder_name": qrData.HolderName,
			"expires_at":  qrData.ExpiresAt,
		},
	})
}

// ProcessQRCodePayment processes a payment via QR code
func (pc *PayCardController) ProcessQRCodePayment(c *gin.Context) {
	var request services.QRCodeTransactionRequest

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	// Validate required fields
	if request.QRCodeData == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "MISSING_QR_CODE",
			Message: "QR code data is required",
			Code:    "400",
		})
		return
	}

	if request.Amount <= 0 {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_AMOUNT",
			Message: "Amount must be greater than 0",
			Code:    "400",
		})
		return
	}

	// Set defaults
	if request.Currency == "" {
		request.Currency = "SZL" // Default currency
	}

	if request.TransactionType == "" {
		request.TransactionType = "purchase" // Default transaction type
	}

	// Process the payment
	response, err := pc.container.PayCardService.ProcessQRCodePayment(request)
	if err != nil {
		pc.container.Logger.LogError(err, map[string]interface{}{
			"action":           "process_qr_payment",
			"amount":           request.Amount,
			"transaction_type": request.TransactionType,
		})

		// Handle specific error cases
		if err.Error() == "QR code validation failed" || err.Error() == "invalid QR code signature" {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Error:   "INVALID_QR_CODE",
				Message: err.Error(),
				Code:    "400",
			})
			return
		}

		if err.Error() == "card not found" || err.Error() == "card is not active" {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Error:   "CARD_UNAVAILABLE",
				Message: err.Error(),
				Code:    "400",
			})
			return
		}

		if err.Error() == "PIN verification failed" {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Error:   "PIN_VERIFICATION_FAILED",
				Message: "Invalid PIN",
				Code:    "400",
			})
			return
		}

		if err.Error() == "spending limit exceeded" {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Error:   "SPENDING_LIMIT_EXCEEDED",
				Message: err.Error(),
				Code:    "400",
			})
			return
		}

		if err.Error() == "fraud detection failed" {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Error:   "FRAUD_DETECTED",
				Message: "Transaction blocked due to fraud detection",
				Code:    "400",
			})
			return
		}

		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "PAYMENT_FAILED",
			Message: "Failed to process QR code payment",
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "QR code payment processed successfully",
		"data":    response,
	})
}
