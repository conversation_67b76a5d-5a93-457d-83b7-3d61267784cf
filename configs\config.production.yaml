# Production Environment Configuration
# This file contains production-specific settings

app:
  name: "Wallet Platform"
  version: "1.0.0"
  environment: "production"
  debug: false  # MUST be false in production

server:
  port: 8086
  host: "0.0.0.0"
  environment: "production"
  read_timeout: 30
  write_timeout: 30
  idle_timeout: 120
  cors:
    allow_origins:
      - "https://your-frontend-domain.com"
      - "https://admin.your-domain.com"
    allow_methods:
      - "GET"
      - "POST"
      - "PUT"
      - "DELETE"
      - "OPTIONS"
    allow_headers:
      - "Content-Type"
      - "Authorization"
      - "X-Request-ID"
      - "X-2FA-Code"
    allow_credentials: true
    max_age: 86400

database:
  driver: "mysql"
  host: ""      # Set via DATABASE_HOST env var
  port: 3306
  username: ""  # Set via DATABASE_USERNAME env var
  password: ""  # Set via DATABASE_PASSWORD env var
  database: "wallet_platform"
  ssl_mode: "true"  # MUST be true in production
  max_open_conns: 50  # Higher for production
  max_idle_conns: 10
  conn_max_lifetime: 300
  conn_max_idle_time: 60

redis:
  enabled: true  # SHOULD be enabled in production for rate limiting
  host: ""       # Set via REDIS_HOST env var
  port: 6379
  password: ""   # Set via REDIS_PASSWORD env var
  database: 0
  pool_size: 20  # Higher for production
  min_idle_conns: 5
  max_retries: 3

jwt:
  secret_key: ""  # MUST be set via JWT_SECRET_KEY env var
  expiration_time: 3600  # 1 hour
  refresh_time: 86400    # 24 hours
  issuer: "wallet-platform"
  audience: "wallet-platform-users"

log:
  level: "info"   # Less verbose in production
  format: "json"  # Structured logging for production
  output: "stdout"

rate_limit:
  enabled: true  # MUST be enabled in production
  default_limit: 100
  default_window: 60
  endpoint_limits:
    "/api/v1/auth/login":
      limit: 5
      window: 300  # 5 minutes
    "/api/v1/wallets/transfer":
      limit: 10
      window: 60
    "/api/v1/cards/*/transactions":
      limit: 20
      window: 60

security:
  encryption_key: ""  # MUST be set via SECURITY_ENCRYPTION_KEY env var
  hash_salt: ""       # MUST be set via SECURITY_HASH_SALT env var
  max_login_attempts: 5
  lockout_duration: 900  # 15 minutes
  session_timeout: 3600  # 1 hour
  require_https: true    # MUST be true in production
  csrf_protection: true  # MUST be true in production
  content_type_no_sniff: true

internal_api:
  enabled: true
  key: ""  # MUST be set via INTERNAL_API_KEY env var
  allowed_services:
    - "payment-engine"
    - "user-service"
    - "notification-service"
    - "admin-panel"
  rate_limit:
    enabled: true
    limit: 1000  # Higher limit for internal services
    window: 60

external:
  payment_engine:
    base_url: ""  # Set via EXTERNAL_PAYMENT_ENGINE_BASE_URL env var
    api_key: ""   # Set via EXTERNAL_PAYMENT_ENGINE_API_KEY env var
    timeout: 30
    retry_count: 3
  
  sms:
    provider: "centurion"
    api_key: ""   # Set via CENTURION_SMS_API_KEY env var
    api_url: "https://auth.centurionbd.com/api/v1/sms/send"

  email:
    provider: "centurion"
    api_key: ""   # Set via CENTURION_EMAIL_API_KEY env var
    api_url: "https://auth.centurionbd.com/api/v1/email/send"
  
  webhook:
    secret: ""    # Set via EXTERNAL_WEBHOOK_SECRET env var
    timeout: 30
    max_retries: 3

# Platform Fees Configuration (in SZL - Swazi Lilangeni)
# These values can be overridden via environment variables
fees:
  card_creation:
    standard: 25.00    # Standard card creation fee
    premium: 50.00     # Premium card creation fee
    business: 100.00   # Business card creation fee
  withdrawal_percentage: 1.0              # 1% withdrawal fee
  transaction_percentage: 0.5             # 0.5% transaction fee
  monthly_maintenance: 10.00              # Monthly maintenance fee
  card_replacement: 15.00                 # Card replacement fee
  international_transaction_percentage: 2.5  # 2.5% international transaction fee
  atm_withdrawal: 5.00                    # Fixed ATM withdrawal fee

# Card Limits Configuration (in SZL - Swazi Lilangeni)
# Production-level limits for security and compliance
card_limits:
  standard:
    spending_limit: 5000.00        # Per transaction limit
    daily_spending_limit: 1000.00  # Daily spending limit
    monthly_spending_limit: 10000.00  # Monthly spending limit
    max_cards_per_wallet: 3        # Maximum cards per wallet
  premium:
    spending_limit: 15000.00       # Per transaction limit
    daily_spending_limit: 3000.00  # Daily spending limit
    monthly_spending_limit: 30000.00  # Monthly spending limit
    max_cards_per_wallet: 5        # Maximum cards per wallet
  business:
    spending_limit: 50000.00       # Per transaction limit
    daily_spending_limit: 10000.00 # Daily spending limit
    monthly_spending_limit: 100000.00  # Monthly spending limit
    max_cards_per_wallet: 10       # Maximum cards per wallet
  creation:
    max_cards_per_day: 3           # Maximum cards created per day
    cooldown_hours: 24             # Hours to wait between card creation
  transaction:
    min_amount: 1.00               # Minimum transaction amount
    max_amount_standard: 5000.00   # Maximum transaction for standard cards
    max_amount_premium: 15000.00   # Maximum transaction for premium cards
    max_amount_business: 50000.00  # Maximum transaction for business cards
  atm:
    daily_limit_standard: 2000.00    # Daily ATM limit for standard cards
    daily_limit_premium: 5000.00     # Daily ATM limit for premium cards
    daily_limit_business: 10000.00   # Daily ATM limit for business cards
    monthly_limit_standard: 20000.00 # Monthly ATM limit for standard cards
    monthly_limit_premium: 50000.00  # Monthly ATM limit for premium cards
    monthly_limit_business: 100000.00 # Monthly ATM limit for business cards
