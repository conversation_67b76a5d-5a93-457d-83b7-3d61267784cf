package controllers

import (
	"net/http"
	"strconv"
	"time"

	"wallet-platform/internal/services"

	"github.com/gin-gonic/gin"
)

// AnalyticsController handles analytics-related requests
type AnalyticsController struct {
	analyticsService         *services.AnalyticsService
	enhancedAnalyticsService *services.EnhancedAnalyticsService
	monitoringService        *services.MonitoringService
	dashboardService         *services.DashboardService
}

// NewAnalyticsController creates a new analytics controller
func NewAnalyticsController(container *services.Container) *AnalyticsController {
	return &AnalyticsController{
		analyticsService:         container.AnalyticsService,
		enhancedAnalyticsService: container.EnhancedAnalyticsService,
		monitoringService:        container.MonitoringService,
		dashboardService:         container.DashboardService,
	}
}

// GetDashboardData returns comprehensive dashboard data
// @Summary Get dashboard data
// @Description Get real-time dashboard data including metrics, charts, and alerts
// @Tags Analytics
// @Accept json
// @Produce json
// @Success 200 {object} services.DashboardData
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/analytics/dashboard [get]
func (ac *AnalyticsController) GetDashboardData(c *gin.Context) {
	dashboardData, err := ac.dashboardService.GetDashboardData()
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to get dashboard data",
			Message: err.Error(),
			Code:    "DASHBOARD_ERROR",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    dashboardData,
	})
}

// GetRealTimeMetrics returns real-time platform metrics
// @Summary Get real-time metrics
// @Description Get current real-time platform metrics
// @Tags Analytics
// @Accept json
// @Produce json
// @Success 200 {object} services.RealTimeMetrics
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/analytics/realtime [get]
func (ac *AnalyticsController) GetRealTimeMetrics(c *gin.Context) {
	metrics, err := ac.enhancedAnalyticsService.GetRealTimeMetrics(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to get real-time metrics",
			Message: err.Error(),
			Code:    "METRICS_ERROR",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    metrics,
	})
}

// GetSystemHealth returns system health status
// @Summary Get system health
// @Description Get current system health and status
// @Tags Monitoring
// @Accept json
// @Produce json
// @Success 200 {object} services.SystemHealthMetrics
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/monitoring/health [get]
func (ac *AnalyticsController) GetSystemHealth(c *gin.Context) {
	health, err := ac.monitoringService.GetSystemHealth()
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to get system health",
			Message: err.Error(),
			Code:    "HEALTH_ERROR",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    health,
	})
}

// GetPerformanceMetrics returns system performance metrics
// @Summary Get performance metrics
// @Description Get current system performance metrics
// @Tags Monitoring
// @Accept json
// @Produce json
// @Success 200 {object} services.PerformanceMetrics
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/monitoring/performance [get]
func (ac *AnalyticsController) GetPerformanceMetrics(c *gin.Context) {
	metrics, err := ac.monitoringService.GetPerformanceMetrics()
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to get performance metrics",
			Message: err.Error(),
			Code:    "PERFORMANCE_ERROR",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    metrics,
	})
}

// GetBusinessMetrics returns business metrics
// @Summary Get business metrics
// @Description Get current business metrics and KPIs
// @Tags Analytics
// @Accept json
// @Produce json
// @Success 200 {object} services.BusinessMetrics
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/analytics/business [get]
func (ac *AnalyticsController) GetBusinessMetrics(c *gin.Context) {
	metrics, err := ac.monitoringService.GetBusinessMetrics()
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to get business metrics",
			Message: err.Error(),
			Code:    "BUSINESS_METRICS_ERROR",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    metrics,
	})
}

// GetWalletAnalytics returns analytics for a specific wallet
// @Summary Get wallet analytics
// @Description Get analytics data for a specific wallet
// @Tags Analytics
// @Accept json
// @Produce json
// @Param wallet_id path int true "Wallet ID"
// @Param period query string false "Period (daily, weekly, monthly)" default(weekly)
// @Param start_date query string false "Start date (YYYY-MM-DD)"
// @Param end_date query string false "End date (YYYY-MM-DD)"
// @Success 200 {object} services.AnalyticsResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/analytics/wallets/{wallet_id} [get]
func (ac *AnalyticsController) GetWalletAnalytics(c *gin.Context) {
	walletIDStr := c.Param("wallet_id")
	walletID, err := strconv.ParseUint(walletIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid wallet ID",
			Message: "Wallet ID must be a valid number",
			Code:    "INVALID_WALLET_ID",
		})
		return
	}

	period := c.DefaultQuery("period", "weekly")
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")

	// Set default dates if not provided
	if startDate == "" || endDate == "" {
		end := time.Now()
		var start time.Time
		switch period {
		case "daily":
			start = end.AddDate(0, 0, -7) // Last 7 days
		case "weekly":
			start = end.AddDate(0, 0, -30) // Last 30 days
		case "monthly":
			start = end.AddDate(0, -6, 0) // Last 6 months
		default:
			start = end.AddDate(0, 0, -30) // Default to 30 days
		}
		startDate = start.Format("2006-01-02")
		endDate = end.Format("2006-01-02")
	}

	analytics, err := ac.analyticsService.GenerateWalletAnalytics(uint(walletID), period, startDate, endDate)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to generate wallet analytics",
			Message: err.Error(),
			Code:    "ANALYTICS_ERROR",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    analytics,
	})
}

// GetCardAnalytics returns analytics for a specific card
// @Summary Get card analytics
// @Description Get analytics data for a specific PayCard
// @Tags Analytics
// @Accept json
// @Produce json
// @Param card_id path int true "Card ID"
// @Param period query string false "Period (daily, weekly, monthly)" default(weekly)
// @Param start_date query string false "Start date (YYYY-MM-DD)"
// @Param end_date query string false "End date (YYYY-MM-DD)"
// @Success 200 {object} services.AnalyticsResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/analytics/cards/{card_id} [get]
func (ac *AnalyticsController) GetCardAnalytics(c *gin.Context) {
	cardIDStr := c.Param("card_id")
	cardID, err := strconv.ParseUint(cardIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid card ID",
			Message: "Card ID must be a valid number",
			Code:    "INVALID_CARD_ID",
		})
		return
	}

	period := c.DefaultQuery("period", "weekly")
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")

	// Set default dates if not provided
	if startDate == "" || endDate == "" {
		end := time.Now()
		var start time.Time
		switch period {
		case "daily":
			start = end.AddDate(0, 0, -7)
		case "weekly":
			start = end.AddDate(0, 0, -30)
		case "monthly":
			start = end.AddDate(0, -6, 0)
		default:
			start = end.AddDate(0, 0, -30)
		}
		startDate = start.Format("2006-01-02")
		endDate = end.Format("2006-01-02")
	}

	analytics, err := ac.analyticsService.GenerateCardAnalytics(uint(cardID), period, startDate, endDate)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to generate card analytics",
			Message: err.Error(),
			Code:    "ANALYTICS_ERROR",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    analytics,
	})
}

// GetSystemAnalytics returns system-wide analytics
// @Summary Get system analytics
// @Description Get system-wide analytics and metrics
// @Tags Analytics
// @Accept json
// @Produce json
// @Param period query string false "Period (daily, weekly, monthly)" default(weekly)
// @Param start_date query string false "Start date (YYYY-MM-DD)"
// @Param end_date query string false "End date (YYYY-MM-DD)"
// @Success 200 {object} services.AnalyticsResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/analytics/system [get]
func (ac *AnalyticsController) GetSystemAnalytics(c *gin.Context) {
	period := c.DefaultQuery("period", "weekly")
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")

	// Set default dates if not provided
	if startDate == "" || endDate == "" {
		end := time.Now()
		var start time.Time
		switch period {
		case "daily":
			start = end.AddDate(0, 0, -7)
		case "weekly":
			start = end.AddDate(0, 0, -30)
		case "monthly":
			start = end.AddDate(0, -6, 0)
		default:
			start = end.AddDate(0, 0, -30)
		}
		startDate = start.Format("2006-01-02")
		endDate = end.Format("2006-01-02")
	}

	analytics, err := ac.analyticsService.GenerateSystemAnalytics(period, startDate, endDate)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to generate system analytics",
			Message: err.Error(),
			Code:    "ANALYTICS_ERROR",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    analytics,
	})
}

// CreateAlert creates a new monitoring alert
// @Summary Create alert
// @Description Create a new monitoring alert
// @Tags Monitoring
// @Accept json
// @Produce json
// @Param alert body CreateAlertRequest true "Alert data"
// @Success 201 {object} SuccessResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/monitoring/alerts [post]
func (ac *AnalyticsController) CreateAlert(c *gin.Context) {
	var req CreateAlertRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid request data",
			Message: err.Error(),
			Code:    "INVALID_REQUEST",
		})
		return
	}

	err := ac.monitoringService.CreateAlert(
		services.AlertType(req.Type),
		services.AlertLevel(req.Level),
		req.Title,
		req.Message,
		req.Component,
		req.Metadata,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to create alert",
			Message: err.Error(),
			Code:    "ALERT_CREATION_ERROR",
		})
		return
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Success: true,
		Message: "Alert created successfully",
	})
}

// Request/Response structures

type CreateAlertRequest struct {
	Type      string                 `json:"type" binding:"required"`
	Level     string                 `json:"level" binding:"required"`
	Title     string                 `json:"title" binding:"required"`
	Message   string                 `json:"message" binding:"required"`
	Component string                 `json:"component" binding:"required"`
	Metadata  map[string]interface{} `json:"metadata"`
}

type ErrorResponse struct {
	Error   string `json:"error"`
	Message string `json:"message"`
	Code    string `json:"code"`
}

type SuccessResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}
