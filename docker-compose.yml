version: '3.8'

services:
  wallet-platform:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8086:8086"
    environment:
      - APP_ENVIRONMENT=development
    env_file:
      - .env.development
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    networks:
      - wallet-network

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_DATABASE: wallet_platform_dev
      MYSQL_USER: wallet_dev_user
      MYSQL_PASSWORD: dev_password_123
      MYSQL_ROOT_PASSWORD: root_password_123
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10
    restart: unless-stopped
    networks:
      - wallet-network

  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      timeout: 3s
      retries: 5
    restart: unless-stopped
    networks:
      - wallet-network

  # Optional: Database administration tool
  adminer:
    image: adminer
    ports:
      - "8080:8080"
    depends_on:
      - mysql
    restart: unless-stopped
    networks:
      - wallet-network

volumes:
  mysql_data:
  redis_data:

networks:
  wallet-network:
    driver: bridge
