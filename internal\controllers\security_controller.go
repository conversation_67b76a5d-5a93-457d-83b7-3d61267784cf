package controllers

import (
	"net/http"

	"wallet-platform/internal/services"

	"github.com/gin-gonic/gin"
)

// SecurityController handles security-related requests
type SecurityController struct {
	container *services.Container
}

// NewSecurityController creates a new security controller
func NewSecurityController(container *services.Container) *SecurityController {
	return &SecurityController{
		container: container,
	}
}

// DeviceRegistrationRequest represents device registration request payload
type DeviceRegistrationRequest struct {
	DeviceName string `json:"device_name" binding:"required"`
	DeviceType string `json:"device_type" binding:"required"`
	OSName     string `json:"os_name" binding:"required"`
	OSVersion  string `json:"os_version" binding:"required"`
	AppVersion string `json:"app_version" binding:"required"`
	UserAgent  string `json:"user_agent"`
	Location   string `json:"location"`
}

// DeviceVerificationRequest represents device verification request
type DeviceVerificationRequest struct {
	DeviceFingerprint string `json:"device_fingerprint" binding:"required"`
	VerificationCode  string `json:"verification_code" binding:"required"`
}

// Setup2FARequest represents 2FA setup request
type Setup2FARequest struct {
	Method      string `json:"method" binding:"required,oneof=sms email totp push"`
	PhoneNumber string `json:"phone_number"`
	Email       string `json:"email"`
}

// Verify2FARequest represents 2FA verification request
type Verify2FARequest struct {
	Code   string `json:"code" binding:"required"`
	Method string `json:"method" binding:"required"`
}

// SecuritySettingsRequest represents security settings update request
type SecuritySettingsRequest struct {
	RequireDeviceAuth     *bool    `json:"require_device_auth"`
	Require2FAForTransfer *bool    `json:"require_2fa_for_transfer"`
	Require2FAForCards    *bool    `json:"require_2fa_for_cards"`
	SessionTimeout        *int     `json:"session_timeout"`
	TransferNotifications *bool    `json:"transfer_notifications"`
	LoginNotifications    *bool    `json:"login_notifications"`
	SuspiciousActivity    *bool    `json:"suspicious_activity"`
	MaxDailyTransfers     *int     `json:"max_daily_transfers"`
	MaxDailyAmount        *float64 `json:"max_daily_amount"`
	RequireApprovalAbove  *float64 `json:"require_approval_above"`
	AllowedCountries      []string `json:"allowed_countries"`
	BlockedCountries      []string `json:"blocked_countries"`
	QuietHoursStart       string   `json:"quiet_hours_start"`
	QuietHoursEnd         string   `json:"quiet_hours_end"`
}

// RegisterDevice registers a new device for a user
func (sc *SecurityController) RegisterDevice(c *gin.Context) {
	var req DeviceRegistrationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	// Get wallet ID from context (set by auth middleware)
	walletID, exists := c.Get("wallet_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "Authentication required",
			Code:    "401",
		})
		return
	}

	// Prepare device info
	deviceInfo := map[string]interface{}{
		"device_name": req.DeviceName,
		"device_type": req.DeviceType,
		"os_name":     req.OSName,
		"os_version":  req.OSVersion,
		"app_version": req.AppVersion,
		"user_agent":  req.UserAgent,
		"location":    req.Location,
		"ip_address":  c.ClientIP(),
	}

	// Register device
	device, err := sc.container.SecurityService.RegisterDevice(walletID.(uint), deviceInfo)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "REGISTRATION_FAILED",
			Message: err.Error(),
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "Device registered successfully. Please verify with the code sent to your phone.",
		"data": gin.H{
			"device_id":          device.ID,
			"device_fingerprint": device.DeviceFingerprint,
		},
	})
}

// VerifyDevice verifies a registered device
func (sc *SecurityController) VerifyDevice(c *gin.Context) {
	var req DeviceVerificationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	// Verify device
	err := sc.container.SecurityService.VerifyDevice(req.DeviceFingerprint, req.VerificationCode)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "VERIFICATION_FAILED",
			Message: err.Error(),
			Code:    "400",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Device verified successfully",
	})
}

// Setup2FA sets up two-factor authentication for a user
func (sc *SecurityController) Setup2FA(c *gin.Context) {
	var req Setup2FARequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	// Get wallet ID from context
	walletID, exists := c.Get("wallet_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "Authentication required",
			Code:    "401",
		})
		return
	}

	// Determine contact based on method
	var contact string
	switch req.Method {
	case "sms":
		contact = req.PhoneNumber
	case "email":
		contact = req.Email
	case "totp", "push":
		contact = "" // Not needed for TOTP/push
	}

	// Setup 2FA
	err := sc.container.SecurityService.Setup2FA(walletID.(uint), req.Method, contact)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "SETUP_FAILED",
			Message: err.Error(),
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "2FA setup completed successfully",
	})
}

// Verify2FA verifies a 2FA code
func (sc *SecurityController) Verify2FA(c *gin.Context) {
	var req Verify2FARequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	// Get wallet ID from context
	walletID, exists := c.Get("wallet_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "Authentication required",
			Code:    "401",
		})
		return
	}

	// Verify 2FA code
	err := sc.container.SecurityService.Verify2FA(walletID.(uint), req.Code)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "VERIFICATION_FAILED",
			Message: err.Error(),
			Code:    "400",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "2FA verification successful",
	})
}

// GetSecuritySettings retrieves security settings for a user
func (sc *SecurityController) GetSecuritySettings(c *gin.Context) {
	// Get wallet ID from context
	walletID, exists := c.Get("wallet_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "Authentication required",
			Code:    "401",
		})
		return
	}

	// Get security settings
	settings, err := sc.container.SecurityService.GetSecuritySettings(walletID.(uint))
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "FETCH_FAILED",
			Message: err.Error(),
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    settings,
	})
}

// UpdateSecuritySettings updates security settings for a user
func (sc *SecurityController) UpdateSecuritySettings(c *gin.Context) {
	var req SecuritySettingsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Code:    "400",
		})
		return
	}

	// Get wallet ID from context
	walletID, exists := c.Get("wallet_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "Authentication required",
			Code:    "401",
		})
		return
	}

	// Convert request to map for service
	updates := make(map[string]interface{})
	if req.RequireDeviceAuth != nil {
		updates["require_device_auth"] = *req.RequireDeviceAuth
	}
	if req.Require2FAForTransfer != nil {
		updates["require_2fa_for_transfer"] = *req.Require2FAForTransfer
	}
	if req.Require2FAForCards != nil {
		updates["require_2fa_for_cards"] = *req.Require2FAForCards
	}
	if req.SessionTimeout != nil {
		updates["session_timeout"] = *req.SessionTimeout
	}
	if req.TransferNotifications != nil {
		updates["transfer_notifications"] = *req.TransferNotifications
	}
	if req.LoginNotifications != nil {
		updates["login_notifications"] = *req.LoginNotifications
	}
	if req.SuspiciousActivity != nil {
		updates["suspicious_activity"] = *req.SuspiciousActivity
	}
	if req.MaxDailyTransfers != nil {
		updates["max_daily_transfers"] = *req.MaxDailyTransfers
	}
	if req.MaxDailyAmount != nil {
		updates["max_daily_amount"] = *req.MaxDailyAmount
	}
	if req.RequireApprovalAbove != nil {
		updates["require_approval_above"] = *req.RequireApprovalAbove
	}
	if req.AllowedCountries != nil {
		updates["allowed_countries"] = req.AllowedCountries
	}
	if req.BlockedCountries != nil {
		updates["blocked_countries"] = req.BlockedCountries
	}
	if req.QuietHoursStart != "" {
		updates["quiet_hours_start"] = req.QuietHoursStart
	}
	if req.QuietHoursEnd != "" {
		updates["quiet_hours_end"] = req.QuietHoursEnd
	}

	// Update security settings
	err := sc.container.SecurityService.UpdateSecuritySettings(walletID.(uint), updates)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "UPDATE_FAILED",
			Message: err.Error(),
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Security settings updated successfully",
	})
}

// GetFraudAlerts retrieves fraud alerts for a user
func (sc *SecurityController) GetFraudAlerts(c *gin.Context) {
	// Get wallet ID from context
	_, exists := c.Get("wallet_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "Authentication required",
			Code:    "401",
		})
		return
	}

	// TODO: Implement GetFraudAlerts in SecurityService
	// For now, return a placeholder response
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Fraud alerts retrieval not yet implemented",
		"data":    []interface{}{},
	})
}

// CheckTransactionFraud checks a transaction for fraud
func (sc *SecurityController) CheckTransactionFraud(c *gin.Context) {
	// Get wallet ID from context
	walletID, exists := c.Get("wallet_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "Authentication required",
			Code:    "401",
		})
		return
	}

	// Get transaction data from request body
	var transactionData map[string]interface{}
	if err := c.ShouldBindJSON(&transactionData); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid transaction data",
			Code:    "400",
		})
		return
	}

	// Add context information
	transactionData["ip_address"] = c.ClientIP()
	transactionData["user_agent"] = c.GetHeader("User-Agent")
	transactionData["timestamp"] = c.GetHeader("X-Timestamp")

	// Check for fraud
	alert, err := sc.container.SecurityService.CheckFraud(walletID.(uint), transactionData)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "FRAUD_CHECK_FAILED",
			Message: err.Error(),
			Code:    "500",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    alert,
	})
}

// GetSecurityEvents retrieves security events for a user
func (sc *SecurityController) GetSecurityEvents(c *gin.Context) {
	// Get wallet ID from context
	_, exists := c.Get("wallet_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "Authentication required",
			Code:    "401",
		})
		return
	}

	// TODO: Implement security events retrieval
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Security events retrieved successfully",
		"data":    []interface{}{},
	})
}
