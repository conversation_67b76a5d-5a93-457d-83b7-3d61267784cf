package redis

import (
	"context"
	"fmt"
	"time"

	"wallet-platform/internal/config"

	"github.com/redis/go-redis/v9"
)

// Client wraps the Redis client with additional functionality
type Client struct {
	*redis.Client
}

// NewClient creates a new Redis client
func NewClient(cfg config.RedisConfig) (*Client, error) {
	rdb := redis.NewClient(&redis.Options{
		Addr:         fmt.Sprintf("%s:%d", cfg.Host, cfg.Port),
		Password:     cfg.Password,
		DB:           cfg.Database,
		PoolSize:     cfg.PoolSize,
		MinIdleConns: cfg.MinIdleConns,
		MaxRetries:   cfg.MaxRetries,
	})

	// Test the connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := rdb.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("failed to connect to Redis: %w", err)
	}

	return &Client{Client: rdb}, nil
}

// SetWithExpiration sets a key-value pair with expiration
func (c *Client) SetWithExpiration(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	return c.Set(ctx, key, value, expiration).Err()
}

// GetString gets a string value by key
func (c *Client) GetString(ctx context.Context, key string) (string, error) {
	val, err := c.Get(ctx, key).Result()
	if err == redis.Nil {
		return "", fmt.Errorf("key not found: %s", key)
	}
	return val, err
}

// Increment increments a counter
func (c *Client) Increment(ctx context.Context, key string) (int64, error) {
	return c.Incr(ctx, key).Result()
}

// IncrementWithExpiration increments a counter and sets expiration if it's a new key
func (c *Client) IncrementWithExpiration(ctx context.Context, key string, expiration time.Duration) (int64, error) {
	pipe := c.Pipeline()
	incr := pipe.Incr(ctx, key)
	pipe.Expire(ctx, key, expiration)
	
	_, err := pipe.Exec(ctx)
	if err != nil {
		return 0, err
	}
	
	return incr.Val(), nil
}

// SetNX sets a key only if it doesn't exist (used for locks)
func (c *Client) SetNX(ctx context.Context, key string, value interface{}, expiration time.Duration) (bool, error) {
	return c.Client.SetNX(ctx, key, value, expiration).Result()
}

// DeleteKeys deletes multiple keys
func (c *Client) DeleteKeys(ctx context.Context, keys ...string) error {
	if len(keys) == 0 {
		return nil
	}
	return c.Del(ctx, keys...).Err()
}

// Exists checks if a key exists
func (c *Client) Exists(ctx context.Context, key string) (bool, error) {
	count, err := c.Client.Exists(ctx, key).Result()
	return count > 0, err
}

// GetTTL gets the time to live for a key
func (c *Client) GetTTL(ctx context.Context, key string) (time.Duration, error) {
	return c.TTL(ctx, key).Result()
}

// SetHash sets a hash field
func (c *Client) SetHash(ctx context.Context, key, field string, value interface{}) error {
	return c.HSet(ctx, key, field, value).Err()
}

// GetHash gets a hash field
func (c *Client) GetHash(ctx context.Context, key, field string) (string, error) {
	val, err := c.HGet(ctx, key, field).Result()
	if err == redis.Nil {
		return "", fmt.Errorf("hash field not found: %s.%s", key, field)
	}
	return val, err
}

// GetAllHash gets all fields in a hash
func (c *Client) GetAllHash(ctx context.Context, key string) (map[string]string, error) {
	return c.HGetAll(ctx, key).Result()
}

// DeleteHashField deletes a hash field
func (c *Client) DeleteHashField(ctx context.Context, key, field string) error {
	return c.HDel(ctx, key, field).Err()
}

// AddToSet adds a member to a set
func (c *Client) AddToSet(ctx context.Context, key string, members ...interface{}) error {
	return c.SAdd(ctx, key, members...).Err()
}

// RemoveFromSet removes a member from a set
func (c *Client) RemoveFromSet(ctx context.Context, key string, members ...interface{}) error {
	return c.SRem(ctx, key, members...).Err()
}

// IsSetMember checks if a value is a member of a set
func (c *Client) IsSetMember(ctx context.Context, key string, member interface{}) (bool, error) {
	return c.SIsMember(ctx, key, member).Result()
}

// GetSetMembers gets all members of a set
func (c *Client) GetSetMembers(ctx context.Context, key string) ([]string, error) {
	return c.SMembers(ctx, key).Result()
}

// AddToSortedSet adds a member to a sorted set with score
func (c *Client) AddToSortedSet(ctx context.Context, key string, score float64, member interface{}) error {
	return c.ZAdd(ctx, key, redis.Z{Score: score, Member: member}).Err()
}

// GetSortedSetRange gets members from a sorted set by rank range
func (c *Client) GetSortedSetRange(ctx context.Context, key string, start, stop int64) ([]string, error) {
	return c.ZRange(ctx, key, start, stop).Result()
}

// GetSortedSetRangeByScore gets members from a sorted set by score range
func (c *Client) GetSortedSetRangeByScore(ctx context.Context, key string, min, max string) ([]string, error) {
	return c.ZRangeByScore(ctx, key, &redis.ZRangeBy{
		Min: min,
		Max: max,
	}).Result()
}

// RemoveFromSortedSet removes a member from a sorted set
func (c *Client) RemoveFromSortedSet(ctx context.Context, key string, members ...interface{}) error {
	return c.ZRem(ctx, key, members...).Err()
}

// PushToList pushes values to the left of a list
func (c *Client) PushToList(ctx context.Context, key string, values ...interface{}) error {
	return c.LPush(ctx, key, values...).Err()
}

// PopFromList pops a value from the right of a list
func (c *Client) PopFromList(ctx context.Context, key string) (string, error) {
	val, err := c.RPop(ctx, key).Result()
	if err == redis.Nil {
		return "", fmt.Errorf("list is empty: %s", key)
	}
	return val, err
}

// GetListRange gets a range of elements from a list
func (c *Client) GetListRange(ctx context.Context, key string, start, stop int64) ([]string, error) {
	return c.LRange(ctx, key, start, stop).Result()
}

// GetListLength gets the length of a list
func (c *Client) GetListLength(ctx context.Context, key string) (int64, error) {
	return c.LLen(ctx, key).Result()
}

// Publish publishes a message to a channel
func (c *Client) Publish(ctx context.Context, channel string, message interface{}) error {
	return c.Client.Publish(ctx, channel, message).Err()
}

// Subscribe subscribes to channels
func (c *Client) Subscribe(ctx context.Context, channels ...string) *redis.PubSub {
	return c.Client.Subscribe(ctx, channels...)
}

// HealthCheck performs a Redis health check
func (c *Client) HealthCheck(ctx context.Context) error {
	return c.Ping(ctx).Err()
}

// GetStats returns Redis connection statistics
func (c *Client) GetStats() map[string]interface{} {
	stats := c.PoolStats()
	return map[string]interface{}{
		"hits":         stats.Hits,
		"misses":       stats.Misses,
		"timeouts":     stats.Timeouts,
		"total_conns":  stats.TotalConns,
		"idle_conns":   stats.IdleConns,
		"stale_conns":  stats.StaleConns,
	}
}

// FlushDB flushes the current database (use with caution)
func (c *Client) FlushDB(ctx context.Context) error {
	return c.Client.FlushDB(ctx).Err()
}

// Keys returns all keys matching a pattern (use with caution in production)
func (c *Client) Keys(ctx context.Context, pattern string) ([]string, error) {
	return c.Client.Keys(ctx, pattern).Result()
}
