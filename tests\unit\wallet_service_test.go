package unit

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"

	"wallet-platform/internal/models"
	"wallet-platform/internal/services"
	"wallet-platform/pkg/logger"
)

type WalletServiceTestSuite struct {
	suite.Suite
	db      *gorm.DB
	service *services.WalletService
	logger  *logger.Logger
}

func (suite *WalletServiceTestSuite) SetupTest() {
	// Setup in-memory SQLite database for testing
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	assert.NoError(suite.T(), err)

	// Auto-migrate the schema
	err = db.AutoMigrate(
		&models.Wallet{},
		&models.WalletTransaction{},
		&models.WalletLimit{},
		&models.WalletSecurity{},
	)
	assert.NoError(suite.T(), err)

	suite.db = db
	suite.logger = logger.New()
	suite.service = services.NewWalletService(db, suite.logger)
}

func (suite *WalletServiceTestSuite) TearDownTest() {
	// Clean up database
	sqlDB, _ := suite.db.DB()
	sqlDB.Close()
}

func (suite *WalletServiceTestSuite) TestCreateWallet_Success() {
	// Test data
	phoneNumber := "+************"
	walletType := "individual"

	// Create wallet
	wallet, err := suite.service.CreateWallet(phoneNumber, walletType)

	// Assertions
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), wallet)
	assert.Equal(suite.T(), phoneNumber, wallet.PhoneNumber)
	assert.Equal(suite.T(), walletType, wallet.WalletType)
	assert.NotEmpty(suite.T(), wallet.AccountNumber)
	assert.Equal(suite.T(), float64(0), wallet.Balance)
	assert.Equal(suite.T(), "active", wallet.Status)
}

func (suite *WalletServiceTestSuite) TestCreateWallet_DuplicatePhoneNumber() {
	phoneNumber := "+************"
	walletType := "individual"

	// Create first wallet
	_, err := suite.service.CreateWallet(phoneNumber, walletType)
	assert.NoError(suite.T(), err)

	// Try to create duplicate wallet
	_, err = suite.service.CreateWallet(phoneNumber, walletType)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "wallet already exists")
}

func (suite *WalletServiceTestSuite) TestGetWallet_Success() {
	// Create test wallet
	phoneNumber := "+************"
	wallet, err := suite.service.CreateWallet(phoneNumber, "individual")
	assert.NoError(suite.T(), err)

	// Get wallet by ID
	retrievedWallet, err := suite.service.GetWallet(wallet.ID)

	// Assertions
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), retrievedWallet)
	assert.Equal(suite.T(), wallet.ID, retrievedWallet.ID)
	assert.Equal(suite.T(), phoneNumber, retrievedWallet.PhoneNumber)
}

func (suite *WalletServiceTestSuite) TestGetWallet_NotFound() {
	// Try to get non-existent wallet
	_, err := suite.service.GetWallet(999)

	// Assertions
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "wallet not found")
}

func (suite *WalletServiceTestSuite) TestGetWalletByPhone_Success() {
	// Create test wallet
	phoneNumber := "+************"
	wallet, err := suite.service.CreateWallet(phoneNumber, "individual")
	assert.NoError(suite.T(), err)

	// Get wallet by phone number
	retrievedWallet, err := suite.service.GetWalletByPhone(phoneNumber)

	// Assertions
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), retrievedWallet)
	assert.Equal(suite.T(), wallet.ID, retrievedWallet.ID)
	assert.Equal(suite.T(), phoneNumber, retrievedWallet.PhoneNumber)
}

func (suite *WalletServiceTestSuite) TestTopupWallet_Success() {
	// Create test wallet
	phoneNumber := "+************"
	wallet, err := suite.service.CreateWallet(phoneNumber, "individual")
	assert.NoError(suite.T(), err)

	// Topup wallet
	amount := 100.0
	reference := "TOPUP123"
	err = suite.service.TopupWallet(wallet.ID, amount, "mobile_money", reference)

	// Assertions
	assert.NoError(suite.T(), err)

	// Verify balance updated
	updatedWallet, err := suite.service.GetWallet(wallet.ID)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), amount, updatedWallet.Balance)
}

func (suite *WalletServiceTestSuite) TestTransferFunds_Success() {
	// Create sender and receiver wallets
	senderWallet, err := suite.service.CreateWallet("+************", "individual")
	assert.NoError(suite.T(), err)
	receiverWallet, err := suite.service.CreateWallet("+256701234568", "individual")
	assert.NoError(suite.T(), err)

	// Topup sender wallet
	err = suite.service.TopupWallet(senderWallet.ID, 200.0, "mobile_money", "TOPUP123")
	assert.NoError(suite.T(), err)

	// Transfer funds
	amount := 50.0
	reference := "TRANSFER123"
	err = suite.service.TransferFunds(senderWallet.ID, receiverWallet.ID, amount, reference)

	// Assertions
	assert.NoError(suite.T(), err)

	// Verify balances
	updatedSender, err := suite.service.GetWallet(senderWallet.ID)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), 150.0, updatedSender.Balance)

	updatedReceiver, err := suite.service.GetWallet(receiverWallet.ID)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), 50.0, updatedReceiver.Balance)
}

func (suite *WalletServiceTestSuite) TestTransferFunds_InsufficientBalance() {
	// Create sender and receiver wallets
	senderWallet, err := suite.service.CreateWallet("+************", "individual")
	assert.NoError(suite.T(), err)
	receiverWallet, err := suite.service.CreateWallet("+256701234568", "individual")
	assert.NoError(suite.T(), err)

	// Try to transfer without sufficient balance
	amount := 100.0
	reference := "TRANSFER123"
	err = suite.service.TransferFunds(senderWallet.ID, receiverWallet.ID, amount, reference)

	// Assertions
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "insufficient balance")
}

func (suite *WalletServiceTestSuite) TestGetWalletTransactions_Success() {
	// Create test wallet
	phoneNumber := "+************"
	wallet, err := suite.service.CreateWallet(phoneNumber, "individual")
	assert.NoError(suite.T(), err)

	// Create test transactions
	err = suite.service.TopupWallet(wallet.ID, 100.0, "mobile_money", "TOPUP1")
	assert.NoError(suite.T(), err)
	err = suite.service.TopupWallet(wallet.ID, 50.0, "mobile_money", "TOPUP2")
	assert.NoError(suite.T(), err)

	// Get transactions
	transactions, err := suite.service.GetWalletTransactions(wallet.ID, 1, 10)

	// Assertions
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), transactions, 2)
	assert.Equal(suite.T(), "topup", transactions[0].TransactionType)
	assert.Equal(suite.T(), "topup", transactions[1].TransactionType)
}

func (suite *WalletServiceTestSuite) TestUpdateWalletStatus_Success() {
	// Create test wallet
	phoneNumber := "+************"
	wallet, err := suite.service.CreateWallet(phoneNumber, "individual")
	assert.NoError(suite.T(), err)

	// Update status
	newStatus := "suspended"
	err = suite.service.UpdateWalletStatus(wallet.ID, newStatus)

	// Assertions
	assert.NoError(suite.T(), err)

	// Verify status updated
	updatedWallet, err := suite.service.GetWallet(wallet.ID)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), newStatus, updatedWallet.Status)
}

func (suite *WalletServiceTestSuite) TestSetWalletLimits_Success() {
	// Create test wallet
	phoneNumber := "+************"
	wallet, err := suite.service.CreateWallet(phoneNumber, "individual")
	assert.NoError(suite.T(), err)

	// Set limits
	dailyLimit := 1000.0
	monthlyLimit := 10000.0
	err = suite.service.SetWalletLimits(wallet.ID, dailyLimit, monthlyLimit)

	// Assertions
	assert.NoError(suite.T(), err)

	// Verify limits set
	var walletLimit models.WalletLimit
	err = suite.db.Where("wallet_id = ?", wallet.ID).First(&walletLimit).Error
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), dailyLimit, walletLimit.DailyLimit)
	assert.Equal(suite.T(), monthlyLimit, walletLimit.MonthlyLimit)
}

func (suite *WalletServiceTestSuite) TestCheckDailySpendingLimit_Success() {
	// Create test wallet
	phoneNumber := "+************"
	wallet, err := suite.service.CreateWallet(phoneNumber, "individual")
	assert.NoError(suite.T(), err)

	// Set limits
	err = suite.service.SetWalletLimits(wallet.ID, 1000.0, 10000.0)
	assert.NoError(suite.T(), err)

	// Check spending limit
	canSpend, err := suite.service.CheckDailySpendingLimit(wallet.ID, 500.0)

	// Assertions
	assert.NoError(suite.T(), err)
	assert.True(suite.T(), canSpend)
}

func TestWalletServiceTestSuite(t *testing.T) {
	suite.Run(t, new(WalletServiceTestSuite))
}
