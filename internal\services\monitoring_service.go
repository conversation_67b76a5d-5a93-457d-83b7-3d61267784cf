package services

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"wallet-platform/internal/models"
	"wallet-platform/pkg/logger"
	"wallet-platform/pkg/redis"

	"gorm.io/gorm"
)

// MonitoringService provides system monitoring and alerting capabilities
type MonitoringService struct {
	db     *gorm.DB
	redis  *redis.Client
	logger *logger.Logger
}

// NewMonitoringService creates a new monitoring service
func NewMonitoringService(db *gorm.DB, redisClient *redis.Client, log *logger.Logger) *MonitoringService {
	return &MonitoringService{
		db:     db,
		redis:  redisClient,
		logger: log,
	}
}

// SystemHealthMetrics represents system health status
type SystemHealthMetrics struct {
	Timestamp          time.Time `json:"timestamp"`
	DatabaseStatus     string    `json:"database_status"`
	RedisStatus        string    `json:"redis_status"`
	APIResponseTime    float64   `json:"api_response_time"`
	TransactionSuccess float64   `json:"transaction_success_rate"`
	ErrorRate          float64   `json:"error_rate"`
	ActiveConnections  int       `json:"active_connections"`
	MemoryUsage        float64   `json:"memory_usage"`
	CPUUsage           float64   `json:"cpu_usage"`
	DiskUsage          float64   `json:"disk_usage"`
	OverallStatus      string    `json:"overall_status"`
}

// AlertLevel represents alert severity levels
type AlertLevel string

const (
	AlertLevelInfo     AlertLevel = "info"
	AlertLevelWarning  AlertLevel = "warning"
	AlertLevelCritical AlertLevel = "critical"
)

// AlertType represents different types of alerts
type AlertType string

const (
	AlertTypeSystem      AlertType = "system"
	AlertTypeSecurity    AlertType = "security"
	AlertTypePerformance AlertType = "performance"
	AlertTypeBusiness    AlertType = "business"
)

// MonitoringAlert represents a system alert
type MonitoringAlert struct {
	ID         uint                   `json:"id"`
	Type       AlertType              `json:"type"`
	Level      AlertLevel             `json:"level"`
	Title      string                 `json:"title"`
	Message    string                 `json:"message"`
	Component  string                 `json:"component"`
	Metadata   map[string]interface{} `json:"metadata"`
	CreatedAt  time.Time              `json:"created_at"`
	ResolvedAt *time.Time             `json:"resolved_at,omitempty"`
	IsResolved bool                   `json:"is_resolved"`
}

// PerformanceMetrics represents system performance data
type PerformanceMetrics struct {
	Timestamp           time.Time `json:"timestamp"`
	RequestsPerSecond   float64   `json:"requests_per_second"`
	AverageResponseTime float64   `json:"average_response_time"`
	P95ResponseTime     float64   `json:"p95_response_time"`
	P99ResponseTime     float64   `json:"p99_response_time"`
	ErrorRate           float64   `json:"error_rate"`
	ThroughputMBps      float64   `json:"throughput_mbps"`
	ConcurrentUsers     int       `json:"concurrent_users"`
	DatabaseQueryTime   float64   `json:"database_query_time"`
	CacheHitRate        float64   `json:"cache_hit_rate"`
}

// BusinessMetrics represents business-related metrics
type BusinessMetrics struct {
	Timestamp              time.Time `json:"timestamp"`
	ActiveWallets          int       `json:"active_wallets"`
	ActiveCards            int       `json:"active_cards"`
	TransactionsToday      int       `json:"transactions_today"`
	VolumeToday            float64   `json:"volume_today"`
	NewWalletsToday        int       `json:"new_wallets_today"`
	NewCardsToday          int       `json:"new_cards_today"`
	SuccessfulTransactions int       `json:"successful_transactions"`
	FailedTransactions     int       `json:"failed_transactions"`
	FraudAlertsToday       int       `json:"fraud_alerts_today"`
	RevenueToday           float64   `json:"revenue_today"`
}

// GetSystemHealth returns current system health status
func (m *MonitoringService) GetSystemHealth() (*SystemHealthMetrics, error) {
	metrics := &SystemHealthMetrics{
		Timestamp: time.Now(),
	}

	// Check database connectivity
	metrics.DatabaseStatus = m.checkDatabaseHealth()

	// Check Redis connectivity
	metrics.RedisStatus = m.checkRedisHealth()

	// Calculate transaction success rate (last hour)
	metrics.TransactionSuccess = m.calculateTransactionSuccessRate()

	// Calculate error rate (last hour)
	metrics.ErrorRate = m.calculateErrorRate()

	// Get active connections (simulated)
	metrics.ActiveConnections = m.getActiveConnections()

	// Calculate overall status
	metrics.OverallStatus = m.calculateOverallStatus(metrics)

	// Cache health metrics
	healthJSON, _ := json.Marshal(metrics)
	m.redis.Set(context.Background(), "system_health", string(healthJSON), 1*time.Minute)

	m.logger.LogSystem("system_health_check", "monitoring", "",
		fmt.Sprintf("Overall status: %s", metrics.OverallStatus))

	return metrics, nil
}

// GetPerformanceMetrics returns current performance metrics
func (m *MonitoringService) GetPerformanceMetrics() (*PerformanceMetrics, error) {
	metrics := &PerformanceMetrics{
		Timestamp: time.Now(),
	}

	// Calculate requests per second (last 5 minutes)
	metrics.RequestsPerSecond = m.calculateRequestsPerSecond()

	// Calculate response times
	metrics.AverageResponseTime = m.calculateAverageResponseTime()
	metrics.P95ResponseTime = m.calculateP95ResponseTime()
	metrics.P99ResponseTime = m.calculateP99ResponseTime()

	// Calculate error rate
	metrics.ErrorRate = m.calculateErrorRate()

	// Get concurrent users
	metrics.ConcurrentUsers = m.getConcurrentUsers()

	// Calculate database performance
	metrics.DatabaseQueryTime = m.calculateDatabaseQueryTime()

	// Calculate cache hit rate
	metrics.CacheHitRate = m.calculateCacheHitRate()

	// Cache performance metrics
	perfJSON, _ := json.Marshal(metrics)
	m.redis.Set(context.Background(), "performance_metrics", string(perfJSON), 30*time.Second)

	return metrics, nil
}

// GetBusinessMetrics returns current business metrics
func (m *MonitoringService) GetBusinessMetrics() (*BusinessMetrics, error) {
	metrics := &BusinessMetrics{
		Timestamp: time.Now(),
	}

	today := time.Now().Format("2006-01-02")

	// Get active wallets and cards
	var activeWallets, activeCards int64
	m.db.Model(&models.Wallet{}).Where("status = 'active'").Count(&activeWallets)
	m.db.Model(&models.PayCard{}).Where("status = 'active'").Count(&activeCards)

	metrics.ActiveWallets = int(activeWallets)
	metrics.ActiveCards = int(activeCards)

	// Get today's transactions
	var transactionsToday, successfulTxns, failedTxns int64
	m.db.Model(&models.WalletTransaction{}).Where("DATE(created_at) = ?", today).Count(&transactionsToday)
	m.db.Model(&models.WalletTransaction{}).Where("DATE(created_at) = ? AND status = 'completed'", today).Count(&successfulTxns)
	failedTxns = transactionsToday - successfulTxns

	metrics.TransactionsToday = int(transactionsToday)
	metrics.SuccessfulTransactions = int(successfulTxns)
	metrics.FailedTransactions = int(failedTxns)

	// Get today's volume
	var volumeToday float64
	m.db.Model(&models.WalletTransaction{}).Where("DATE(created_at) = ?", today).
		Select("COALESCE(SUM(amount), 0)").Scan(&volumeToday)
	metrics.VolumeToday = volumeToday

	// Get new wallets and cards today
	var newWallets, newCards int64
	m.db.Model(&models.Wallet{}).Where("DATE(created_at) = ?", today).Count(&newWallets)
	m.db.Model(&models.PayCard{}).Where("DATE(created_at) = ?", today).Count(&newCards)

	metrics.NewWalletsToday = int(newWallets)
	metrics.NewCardsToday = int(newCards)

	// Get fraud alerts today
	var fraudAlerts int64
	m.db.Model(&models.FraudAlert{}).Where("DATE(created_at) = ?", today).Count(&fraudAlerts)
	metrics.FraudAlertsToday = int(fraudAlerts)

	// Calculate revenue (simplified - could be more complex)
	metrics.RevenueToday = volumeToday * 0.01 // Assuming 1% fee

	// Cache business metrics
	bizJSON, _ := json.Marshal(metrics)
	m.redis.Set(context.Background(), "business_metrics", string(bizJSON), 5*time.Minute)

	return metrics, nil
}

// CreateAlert creates a new monitoring alert
func (m *MonitoringService) CreateAlert(alertType AlertType, level AlertLevel, title, message, component string, metadata map[string]interface{}) error {
	// Convert metadata to JSON
	metadataJSON, _ := json.Marshal(metadata)

	alert := models.Alert{
		Type:      string(alertType),
		Level:     string(level),
		Title:     title,
		Message:   message,
		Component: component,
		Metadata:  metadataJSON,
		CreatedAt: time.Now(),
	}

	if err := m.db.Create(&alert).Error; err != nil {
		return fmt.Errorf("failed to create alert: %w", err)
	}

	// Log the alert
	m.logger.LogSecurity("alert_created", component, "",
		fmt.Sprintf("%s: %s - %s", level, title, message))

	// Send real-time notification (could integrate with webhook service)
	m.sendAlertNotification(&alert)

	return nil
}

// CheckSystemThresholds monitors system thresholds and creates alerts
func (m *MonitoringService) CheckSystemThresholds() error {
	health, err := m.GetSystemHealth()
	if err != nil {
		return err
	}

	// Check database health
	if health.DatabaseStatus != "healthy" {
		m.CreateAlert(AlertTypeSystem, AlertLevelCritical,
			"Database Health Issue",
			"Database connectivity issues detected",
			"database", map[string]interface{}{"status": health.DatabaseStatus})
	}

	// Check transaction success rate
	if health.TransactionSuccess < 95.0 {
		level := AlertLevelWarning
		if health.TransactionSuccess < 90.0 {
			level = AlertLevelCritical
		}
		m.CreateAlert(AlertTypePerformance, level,
			"Low Transaction Success Rate",
			fmt.Sprintf("Transaction success rate is %.2f%%", health.TransactionSuccess),
			"transactions", map[string]interface{}{"success_rate": health.TransactionSuccess})
	}

	// Check error rate
	if health.ErrorRate > 5.0 {
		level := AlertLevelWarning
		if health.ErrorRate > 10.0 {
			level = AlertLevelCritical
		}
		m.CreateAlert(AlertTypePerformance, level,
			"High Error Rate",
			fmt.Sprintf("Error rate is %.2f%%", health.ErrorRate),
			"api", map[string]interface{}{"error_rate": health.ErrorRate})
	}

	return nil
}

// Helper methods for calculations

func (m *MonitoringService) checkDatabaseHealth() string {
	if err := m.db.Exec("SELECT 1").Error; err != nil {
		return "unhealthy"
	}
	return "healthy"
}

func (m *MonitoringService) checkRedisHealth() string {
	if err := m.redis.Ping(context.Background()).Err(); err != nil {
		return "unhealthy"
	}
	return "healthy"
}

func (m *MonitoringService) calculateTransactionSuccessRate() float64 {
	oneHourAgo := time.Now().Add(-1 * time.Hour)

	var total, successful int64
	m.db.Model(&models.WalletTransaction{}).Where("created_at >= ?", oneHourAgo).Count(&total)
	m.db.Model(&models.WalletTransaction{}).Where("created_at >= ? AND status = 'completed'", oneHourAgo).Count(&successful)

	if total == 0 {
		return 100.0
	}
	return float64(successful) / float64(total) * 100.0
}

func (m *MonitoringService) calculateErrorRate() float64 {
	// This would typically come from application logs or metrics
	// For now, return a simulated value based on failed transactions
	oneHourAgo := time.Now().Add(-1 * time.Hour)

	var total, failed int64
	m.db.Model(&models.WalletTransaction{}).Where("created_at >= ?", oneHourAgo).Count(&total)
	m.db.Model(&models.WalletTransaction{}).Where("created_at >= ? AND status = 'failed'", oneHourAgo).Count(&failed)

	if total == 0 {
		return 0.0
	}
	return float64(failed) / float64(total) * 100.0
}

func (m *MonitoringService) getActiveConnections() int {
	// This would typically come from database connection pool metrics
	// Return a simulated value for now
	return 25
}

func (m *MonitoringService) calculateOverallStatus(metrics *SystemHealthMetrics) string {
	if metrics.DatabaseStatus != "healthy" || metrics.RedisStatus != "healthy" {
		return "critical"
	}
	if metrics.TransactionSuccess < 95.0 || metrics.ErrorRate > 5.0 {
		return "warning"
	}
	return "healthy"
}

func (m *MonitoringService) calculateRequestsPerSecond() float64 {
	// Simulated value - would come from actual metrics
	return 150.0
}

func (m *MonitoringService) calculateAverageResponseTime() float64 {
	// Simulated value - would come from actual metrics
	return 120.0 // milliseconds
}

func (m *MonitoringService) calculateP95ResponseTime() float64 {
	return 250.0 // milliseconds
}

func (m *MonitoringService) calculateP99ResponseTime() float64 {
	return 500.0 // milliseconds
}

func (m *MonitoringService) getConcurrentUsers() int {
	fiveMinutesAgo := time.Now().Add(-5 * time.Minute)
	var activeUsers int64
	m.db.Model(&models.WalletTransaction{}).
		Where("created_at >= ?", fiveMinutesAgo).
		Distinct("wallet_id").Count(&activeUsers)
	return int(activeUsers)
}

func (m *MonitoringService) calculateDatabaseQueryTime() float64 {
	// Simulated value - would come from database metrics
	return 15.0 // milliseconds
}

func (m *MonitoringService) calculateCacheHitRate() float64 {
	// Simulated value - would come from Redis metrics
	return 85.0 // percentage
}

func (m *MonitoringService) sendAlertNotification(alert *models.Alert) {
	// This would integrate with the webhook service or notification system
	// For now, just log the alert
	m.logger.LogSystem("alert_notification", "monitoring", "",
		fmt.Sprintf("Alert sent: %s - %s", alert.Title, alert.Message))
}

// EnableMaintenanceMode enables system maintenance mode
func (m *MonitoringService) EnableMaintenanceMode(message string, durationMinutes int) error {
	// Store maintenance mode in Redis with expiration
	maintenanceData := map[string]interface{}{
		"enabled":    true,
		"message":    message,
		"duration":   durationMinutes,
		"started_at": time.Now(),
		"expires_at": time.Now().Add(time.Duration(durationMinutes) * time.Minute),
	}

	maintenanceJSON, _ := json.Marshal(maintenanceData)
	err := m.redis.Set(context.Background(), "maintenance_mode", string(maintenanceJSON), time.Duration(durationMinutes)*time.Minute).Err()
	if err != nil {
		m.logger.LogError(err, map[string]interface{}{
			"action":   "enable_maintenance_mode",
			"message":  message,
			"duration": durationMinutes,
		})
		return fmt.Errorf("failed to enable maintenance mode: %w", err)
	}

	m.logger.WithFields(map[string]interface{}{
		"action":   "maintenance_mode_enabled",
		"message":  message,
		"duration": durationMinutes,
	}).Info("Maintenance mode enabled")

	return nil
}

// DisableMaintenanceMode disables system maintenance mode
func (m *MonitoringService) DisableMaintenanceMode() error {
	err := m.redis.Del(context.Background(), "maintenance_mode").Err()
	if err != nil {
		m.logger.LogError(err, map[string]interface{}{
			"action": "disable_maintenance_mode",
		})
		return fmt.Errorf("failed to disable maintenance mode: %w", err)
	}

	m.logger.WithFields(map[string]interface{}{
		"action": "maintenance_mode_disabled",
	}).Info("Maintenance mode disabled")

	return nil
}

// IsMaintenanceModeEnabled checks if maintenance mode is currently enabled
func (m *MonitoringService) IsMaintenanceModeEnabled() (bool, string, error) {
	result, err := m.redis.Get(context.Background(), "maintenance_mode").Result()
	if err != nil {
		// If key doesn't exist, maintenance mode is disabled
		return false, "", nil
	}

	var maintenanceData map[string]interface{}
	if err := json.Unmarshal([]byte(result), &maintenanceData); err != nil {
		return false, "", fmt.Errorf("failed to parse maintenance data: %w", err)
	}

	enabled, ok := maintenanceData["enabled"].(bool)
	if !ok {
		return false, "", nil
	}

	message, _ := maintenanceData["message"].(string)
	return enabled, message, nil
}
