apiVersion: v1
kind: Namespace
metadata:
  name: wallet-platform
  labels:
    name: wallet-platform
    environment: production
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: wallet-platform-config
  namespace: wallet-platform
data:
  APP_ENVIRONMENT: "production"
  SERVER_PORT: "8086"
  SERVER_HOST: "0.0.0.0"
  DATABASE_DRIVER: "mysql"
  DATABASE_HOST: "mysql-service"
  DATABASE_PORT: "3306"
  DATABASE_DATABASE: "wallet_platform"
  REDIS_HOST: "redis-service"
  REDIS_PORT: "6379"
  REDIS_ENABLED: "true"
  LOG_LEVEL: "info"
  LOG_FORMAT: "json"
  RATE_LIMIT_ENABLED: "true"
  SECURITY_REQUIRE_HTTPS: "true"
---
apiVersion: v1
kind: Secret
metadata:
  name: wallet-platform-secrets
  namespace: wallet-platform
type: Opaque
data:
  # Base64 encoded values - REPLACE WITH ACTUAL SECURE VALUES
  DATABASE_USERNAME: d2FsbGV0X3Byb2RfdXNlcg==  # wallet_prod_user
  DATABASE_PASSWORD: UkVQTEFDRV9XSVRIX1NFQ1VSRV9QQVNTV09SRA==  # REPLACE_WITH_SECURE_PASSWORD
  REDIS_PASSWORD: UkVQTEFDRV9XSVRIX1NFQ1VSRV9SRURJU19QQVNTV09SRA==  # REPLACE_WITH_SECURE_REDIS_PASSWORD
  JWT_SECRET_KEY: UkVQTEFDRV9XSVRIX1NFQ1VSRV9KV1RfU0VDUkVU  # REPLACE_WITH_SECURE_JWT_SECRET
  SECURITY_ENCRYPTION_KEY: UkVQTEFDRV9XSVRIX1NFQ1VSRV9FTkNSWVBUSU9OX0tFWQ==  # REPLACE_WITH_SECURE_ENCRYPTION_KEY
  SECURITY_HASH_SALT: UkVQTEFDRV9XSVRIX1NFQ1VSRV9IQVNIX1NBTFQ=  # REPLACE_WITH_SECURE_HASH_SALT
  INTERNAL_API_KEY: UkVQTEFDRV9XSVRIX1NFQ1VSRV9JTlRFUk5BTF9BUElfS0VZ  # REPLACE_WITH_SECURE_INTERNAL_API_KEY
