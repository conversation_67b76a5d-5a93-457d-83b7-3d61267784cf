package services

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"wallet-platform/internal/models"
	"wallet-platform/pkg/logger"
	"wallet-platform/pkg/redis"

	"gorm.io/gorm"
)

// AnalyticsJobsService handles scheduled analytics and monitoring tasks
type AnalyticsJobsService struct {
	db                       *gorm.DB
	redis                    *redis.Client
	logger                   *logger.Logger
	monitoringService        *MonitoringService
	enhancedAnalyticsService *EnhancedAnalyticsService
	isRunning                bool
	stopChannel              chan bool
}

// NewAnalyticsJobsService creates a new analytics jobs service
func NewAnalyticsJobsService(db *gorm.DB, redisClient *redis.Client, log *logger.Logger,
	monitoringService *MonitoringService, enhancedAnalyticsService *EnhancedAnalyticsService) *AnalyticsJobsService {
	return &AnalyticsJobsService{
		db:                       db,
		redis:                    redisClient,
		logger:                   log,
		monitoringService:        monitoringService,
		enhancedAnalyticsService: enhancedAnalyticsService,
		isRunning:                false,
		stopChannel:              make(chan bool),
	}
}

// JobType represents different types of analytics jobs
type JobType string

const (
	JobTypeSystemHealth       JobType = "system_health"
	JobTypeMetricsAggregation JobType = "metrics_aggregation"
	JobTypeReportGeneration   JobType = "report_generation"
	JobTypeDataCleanup        JobType = "data_cleanup"
	JobTypeAlertCheck         JobType = "alert_check"
)

// ScheduledJob represents a scheduled analytics job
type ScheduledJob struct {
	ID        uint                   `json:"id"`
	Type      JobType                `json:"type"`
	Name      string                 `json:"name"`
	Schedule  string                 `json:"schedule"` // cron expression
	IsEnabled bool                   `json:"is_enabled"`
	LastRun   *time.Time             `json:"last_run"`
	NextRun   *time.Time             `json:"next_run"`
	RunCount  int                    `json:"run_count"`
	FailCount int                    `json:"fail_count"`
	Config    map[string]interface{} `json:"config"`
	CreatedAt time.Time              `json:"created_at"`
	UpdatedAt time.Time              `json:"updated_at"`
}

// JobResult represents the result of a job execution
type JobResult struct {
	JobID       uint                   `json:"job_id"`
	JobType     JobType                `json:"job_type"`
	StartTime   time.Time              `json:"start_time"`
	EndTime     time.Time              `json:"end_time"`
	Duration    time.Duration          `json:"duration"`
	Success     bool                   `json:"success"`
	Error       string                 `json:"error,omitempty"`
	Output      map[string]interface{} `json:"output"`
	ProcessedAt time.Time              `json:"processed_at"`
}

// Start begins the analytics jobs scheduler
func (a *AnalyticsJobsService) Start() error {
	if a.isRunning {
		return fmt.Errorf("analytics jobs service is already running")
	}

	a.isRunning = true
	a.logger.LogSystem("analytics_jobs_started", "scheduler", "", "Analytics jobs scheduler started")

	// Start the main scheduler loop
	go a.schedulerLoop()

	// Start individual job runners
	go a.systemHealthRunner()
	go a.metricsAggregationRunner()
	go a.alertCheckRunner()

	return nil
}

// Stop stops the analytics jobs scheduler
func (a *AnalyticsJobsService) Stop() error {
	if !a.isRunning {
		return fmt.Errorf("analytics jobs service is not running")
	}

	a.isRunning = false
	a.stopChannel <- true
	a.logger.LogSystem("analytics_jobs_stopped", "scheduler", "", "Analytics jobs scheduler stopped")

	return nil
}

// schedulerLoop is the main scheduler loop
func (a *AnalyticsJobsService) schedulerLoop() {
	ticker := time.NewTicker(1 * time.Minute) // Check every minute
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			a.processScheduledJobs()
		case <-a.stopChannel:
			return
		}
	}
}

// processScheduledJobs processes all scheduled jobs
func (a *AnalyticsJobsService) processScheduledJobs() {
	var jobs []models.ScheduledJob
	err := a.db.Where("is_enabled = ? AND (next_run IS NULL OR next_run <= ?)",
		true, time.Now()).Find(&jobs).Error
	if err != nil {
		a.logger.LogError(err, map[string]interface{}{"component": "job_scheduler"})
		return
	}

	for _, job := range jobs {
		go a.executeJob(&job)
	}
}

// executeJob executes a specific job
func (a *AnalyticsJobsService) executeJob(job *models.ScheduledJob) {
	startTime := time.Now()
	result := &JobResult{
		JobID:       job.ID,
		JobType:     JobType(job.Type),
		StartTime:   startTime,
		Output:      make(map[string]interface{}),
		ProcessedAt: time.Now(),
	}

	a.logger.LogSystem("job_started", "scheduler", "",
		fmt.Sprintf("Starting job: %s (%s)", job.Name, job.Type))

	// Execute the job based on type
	var err error
	switch JobType(job.Type) {
	case JobTypeSystemHealth:
		err = a.executeSystemHealthJob(job, result)
	case JobTypeMetricsAggregation:
		err = a.executeMetricsAggregationJob(job, result)
	case JobTypeReportGeneration:
		err = a.executeReportGenerationJob(job, result)
	case JobTypeDataCleanup:
		err = a.executeDataCleanupJob(job, result)
	case JobTypeAlertCheck:
		err = a.executeAlertCheckJob(job, result)
	default:
		err = fmt.Errorf("unknown job type: %s", job.Type)
	}

	// Update job result
	result.EndTime = time.Now()
	result.Duration = result.EndTime.Sub(result.StartTime)
	result.Success = err == nil

	if err != nil {
		result.Error = err.Error()
		job.FailureCount++
		a.logger.LogError(err, map[string]interface{}{
			"job_id":   job.ID,
			"job_type": job.Type,
			"job_name": job.Name,
		})
	} else {
		job.RunCount++
		a.logger.LogSystem("job_completed", "scheduler", "",
			fmt.Sprintf("Completed job: %s in %v", job.Name, result.Duration))
	}

	// Update job record
	now := time.Now()
	job.LastRun = &now
	job.NextRun = a.calculateNextRun(job.Schedule, now)
	a.db.Save(job)

	// Store job result
	a.storeJobResult(result)
}

// systemHealthRunner runs continuous system health monitoring
func (a *AnalyticsJobsService) systemHealthRunner() {
	ticker := time.NewTicker(30 * time.Second) // Check every 30 seconds
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if a.isRunning {
				a.performSystemHealthCheck()
			}
		case <-a.stopChannel:
			return
		}
	}
}

// metricsAggregationRunner runs metrics aggregation tasks
func (a *AnalyticsJobsService) metricsAggregationRunner() {
	ticker := time.NewTicker(5 * time.Minute) // Aggregate every 5 minutes
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if a.isRunning {
				a.performMetricsAggregation()
			}
		case <-a.stopChannel:
			return
		}
	}
}

// alertCheckRunner runs alert threshold monitoring
func (a *AnalyticsJobsService) alertCheckRunner() {
	ticker := time.NewTicker(1 * time.Minute) // Check every minute
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if a.isRunning {
				a.performAlertChecks()
			}
		case <-a.stopChannel:
			return
		}
	}
}

// Job execution methods

func (a *AnalyticsJobsService) executeSystemHealthJob(job *models.ScheduledJob, result *JobResult) error {
	health, err := a.monitoringService.GetSystemHealth()
	if err != nil {
		return err
	}

	result.Output["system_status"] = health.OverallStatus
	result.Output["database_status"] = health.DatabaseStatus
	result.Output["redis_status"] = health.RedisStatus
	result.Output["transaction_success_rate"] = health.TransactionSuccess
	result.Output["error_rate"] = health.ErrorRate

	// Cache health data
	healthJSON, _ := json.Marshal(health)
	a.redis.Set(context.Background(), "latest_system_health", string(healthJSON), 5*time.Minute)

	return nil
}

func (a *AnalyticsJobsService) executeMetricsAggregationJob(job *models.ScheduledJob, result *JobResult) error {
	// Aggregate hourly metrics
	err := a.aggregateHourlyMetrics()
	if err != nil {
		return err
	}

	// Aggregate daily metrics
	err = a.aggregateDailyMetrics()
	if err != nil {
		return err
	}

	result.Output["hourly_aggregation"] = "completed"
	result.Output["daily_aggregation"] = "completed"

	return nil
}

func (a *AnalyticsJobsService) executeReportGenerationJob(job *models.ScheduledJob, result *JobResult) error {
	// Generate daily reports
	endDate := time.Now()
	startDate := endDate.AddDate(0, 0, -1)

	// This would generate comprehensive reports
	// For now, just log the action
	result.Output["report_type"] = "daily"
	result.Output["start_date"] = startDate.Format("2006-01-02")
	result.Output["end_date"] = endDate.Format("2006-01-02")

	a.logger.LogAnalytics("daily_report_generated", "daily", 0, 0)

	return nil
}

func (a *AnalyticsJobsService) executeDataCleanupJob(job *models.ScheduledJob, result *JobResult) error {
	// Clean up old analytics data (older than 90 days)
	cutoffDate := time.Now().AddDate(0, 0, -90)

	var deletedCount int64
	err := a.db.Where("created_at < ?", cutoffDate).Delete(&models.WalletAnalytics{}).Error
	if err != nil {
		return err
	}

	result.Output["deleted_records"] = deletedCount
	result.Output["cutoff_date"] = cutoffDate.Format("2006-01-02")

	return nil
}

func (a *AnalyticsJobsService) executeAlertCheckJob(job *models.ScheduledJob, result *JobResult) error {
	err := a.monitoringService.CheckSystemThresholds()
	if err != nil {
		return err
	}

	result.Output["alert_check"] = "completed"
	return nil
}

// Helper methods

func (a *AnalyticsJobsService) performSystemHealthCheck() {
	health, err := a.monitoringService.GetSystemHealth()
	if err != nil {
		a.logger.LogError(err, map[string]interface{}{"component": "health_check"})
		return
	}

	// Store health metrics in time series
	healthData := map[string]interface{}{
		"timestamp":           time.Now(),
		"overall_status":      health.OverallStatus,
		"database_status":     health.DatabaseStatus,
		"redis_status":        health.RedisStatus,
		"transaction_success": health.TransactionSuccess,
		"error_rate":          health.ErrorRate,
	}

	healthJSON, _ := json.Marshal(healthData)
	key := fmt.Sprintf("health_metrics:%d", time.Now().Unix())
	a.redis.Set(context.Background(), key, string(healthJSON), 24*time.Hour)
}

func (a *AnalyticsJobsService) performMetricsAggregation() {
	// Get real-time metrics
	ctx := context.Background()
	metricsMap, err := a.enhancedAnalyticsService.GetRealTimeMetrics(ctx)
	if err != nil {
		a.logger.LogError(err, map[string]interface{}{"component": "metrics_aggregation"})
		return
	}

	// Store aggregated metrics (simplified)
	metricsData := map[string]interface{}{
		"timestamp":            time.Now(),
		"active_users":         0, // placeholder
		"transactions_per_min": 0, // placeholder
		"volume_per_min":       0, // placeholder
		"success_rate":         0, // placeholder
		"error_rate":           0, // placeholder
		"fraud_rate":           0, // placeholder
	}

	// Use metricsMap to avoid unused variable
	_ = metricsMap

	metricsJSON, _ := json.Marshal(metricsData)
	key := fmt.Sprintf("aggregated_metrics:%d", time.Now().Unix())
	a.redis.Set(context.Background(), key, string(metricsJSON), 24*time.Hour)
}

func (a *AnalyticsJobsService) performAlertChecks() {
	err := a.monitoringService.CheckSystemThresholds()
	if err != nil {
		a.logger.LogError(err, map[string]interface{}{"component": "alert_checks"})
	}
}

func (a *AnalyticsJobsService) aggregateHourlyMetrics() error {
	// Aggregate transaction metrics by hour
	now := time.Now()
	hourStart := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), 0, 0, 0, now.Location())
	hourEnd := hourStart.Add(1 * time.Hour)

	var totalTxns, successfulTxns int64
	var totalVolume float64

	a.db.Model(&models.WalletTransaction{}).
		Where("created_at >= ? AND created_at < ?", hourStart, hourEnd).Count(&totalTxns)

	a.db.Model(&models.WalletTransaction{}).
		Where("created_at >= ? AND created_at < ? AND status = 'completed'", hourStart, hourEnd).Count(&successfulTxns)

	a.db.Model(&models.WalletTransaction{}).
		Where("created_at >= ? AND created_at < ?", hourStart, hourEnd).
		Select("COALESCE(SUM(amount), 0)").Scan(&totalVolume)

	// Store hourly analytics
	hourlyAnalytics := models.WalletAnalytics{
		WalletID:         0, // System-wide analytics
		PeriodType:       "hourly",
		PeriodStart:      hourStart,
		PeriodEnd:        hourEnd,
		TransactionCount: int(totalTxns),
		TotalAmount:      totalVolume,
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}

	return a.db.Create(&hourlyAnalytics).Error
}

func (a *AnalyticsJobsService) aggregateDailyMetrics() error {
	// Aggregate transaction metrics by day
	now := time.Now()
	dayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	dayEnd := dayStart.Add(24 * time.Hour)

	var totalTxns, successfulTxns int64
	var totalVolume float64

	a.db.Model(&models.WalletTransaction{}).
		Where("created_at >= ? AND created_at < ?", dayStart, dayEnd).Count(&totalTxns)

	a.db.Model(&models.WalletTransaction{}).
		Where("created_at >= ? AND created_at < ? AND status = 'completed'", dayStart, dayEnd).Count(&successfulTxns)

	a.db.Model(&models.WalletTransaction{}).
		Where("created_at >= ? AND created_at < ?", dayStart, dayEnd).
		Select("COALESCE(SUM(amount), 0)").Scan(&totalVolume)

	// Store daily analytics
	dailyAnalytics := models.WalletAnalytics{
		WalletID:         0, // System-wide analytics
		PeriodType:       "daily",
		PeriodStart:      dayStart,
		PeriodEnd:        dayEnd,
		TransactionCount: int(totalTxns),
		TotalAmount:      totalVolume,
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}

	return a.db.Create(&dailyAnalytics).Error
}

func (a *AnalyticsJobsService) calculateNextRun(schedule string, lastRun time.Time) *time.Time {
	// Simple schedule calculation - in production, use a proper cron parser
	var nextRun time.Time
	switch schedule {
	case "*/5 * * * *": // Every 5 minutes
		nextRun = lastRun.Add(5 * time.Minute)
	case "0 * * * *": // Every hour
		nextRun = lastRun.Add(1 * time.Hour)
	case "0 0 * * *": // Daily at midnight
		nextRun = lastRun.Add(24 * time.Hour)
	default:
		nextRun = lastRun.Add(1 * time.Hour) // Default to hourly
	}
	return &nextRun
}

func (a *AnalyticsJobsService) storeJobResult(result *JobResult) {
	// Store job execution result
	endTime := result.EndTime
	resultJSON, _ := json.Marshal(result.Output)

	resultData := models.JobExecution{
		JobID:     result.JobID,
		StartTime: result.StartTime,
		EndTime:   &endTime,
		Duration:  int64(result.Duration.Milliseconds()),
		Status: func() string {
			if result.Success {
				return "success"
			} else {
				return "failed"
			}
		}(),
		Error:            result.Error,
		Result:           resultJSON,
		RecordsProcessed: 0, // placeholder
		CreatedAt:        time.Now(),
	}

	a.db.Create(&resultData)
}
