package services

import (
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"wallet-platform/internal/models"
	"wallet-platform/pkg/logger"

	"gorm.io/gorm"
)

// DeviceManagementService handles device registration and management
type DeviceManagementService struct {
	db     *gorm.DB
	logger *logger.Logger
}

// DeviceInfo represents device information for registration
type DeviceInfo struct {
	DeviceType string                 `json:"device_type"` // mobile, desktop, tablet
	OS         string                 `json:"os"`          // iOS, Android, Windows, macOS, Linux
	OSVersion  string                 `json:"os_version"`  // OS version
	AppVersion string                 `json:"app_version"` // Application version
	Model      string                 `json:"model"`       // Device model
	Brand      string                 `json:"brand"`       // Device brand
	UserAgent  string                 `json:"user_agent"`  // Browser user agent
	ScreenSize string                 `json:"screen_size"` // Screen resolution
	Timezone   string                 `json:"timezone"`    // Device timezone
	Language   string                 `json:"language"`    // Device language
	IPAddress  string                 `json:"ip_address"`  // Current IP address
	Location   string                 `json:"location"`    // Geographic location
	Metadata   map[string]interface{} `json:"metadata"`    // Additional device data
}

// DeviceRegistrationResult represents the result of device registration
type DeviceRegistrationResult struct {
	DeviceID             uint      `json:"device_id"`
	DeviceFingerprint    string    `json:"device_fingerprint"`
	VerificationCode     string    `json:"verification_code"`
	VerificationExpiry   time.Time `json:"verification_expiry"`
	RequiresVerification bool      `json:"requires_verification"`
	TrustLevel           string    `json:"trust_level"`
}

// NewDeviceManagementService creates a new device management service
func NewDeviceManagementService(db *gorm.DB, log *logger.Logger) *DeviceManagementService {
	return &DeviceManagementService{
		db:     db,
		logger: log,
	}
}

// RegisterDevice registers a new device for a wallet
func (d *DeviceManagementService) RegisterDevice(walletID uint, deviceInfo *DeviceInfo) (*DeviceRegistrationResult, error) {
	// Generate device fingerprint
	fingerprint := d.generateDeviceFingerprint(deviceInfo)

	// Check if device already exists
	var existingDevice models.DeviceRegistration
	if err := d.db.Where("wallet_id = ? AND device_fingerprint = ?", walletID, fingerprint).First(&existingDevice).Error; err == nil {
		// Device already registered, update last used
		now := time.Now()
		existingDevice.LastUsed = &now
		existingDevice.UsageCount++
		d.db.Save(&existingDevice)

		return &DeviceRegistrationResult{
			DeviceID:             existingDevice.ID,
			DeviceFingerprint:    existingDevice.DeviceFingerprint,
			RequiresVerification: false,
			TrustLevel:           existingDevice.TrustLevel,
		}, nil
	}

	// Generate verification code
	verificationCode := d.generateVerificationCode()
	verificationExpiry := time.Now().Add(15 * time.Minute) // 15 minutes expiry

	// Determine initial trust level
	trustLevel := d.calculateInitialTrustLevel(walletID, deviceInfo)

	// Create device registration
	metadataJSON, _ := json.Marshal(deviceInfo.Metadata)
	now := time.Now()
	device := models.DeviceRegistration{
		WalletID:           walletID,
		DeviceFingerprint:  fingerprint,
		DeviceName:         d.generateDeviceName(deviceInfo),
		DeviceType:         deviceInfo.DeviceType,
		OSVersion:          deviceInfo.OSVersion,
		AppVersion:         deviceInfo.AppVersion,
		UserAgent:          deviceInfo.UserAgent,
		IPAddress:          deviceInfo.IPAddress,
		Location:           deviceInfo.Location,
		TrustLevel:         trustLevel,
		VerificationCode:   verificationCode,
		VerificationExpiry: &verificationExpiry,
		IsVerified:         false,
		LastUsed:           &now,
		UsageCount:         1,
		Status:             "pending_verification",
		Metadata:           metadataJSON,
	}

	if err := d.db.Create(&device).Error; err != nil {
		d.logger.LogError(err, map[string]interface{}{
			"action":             "register_device",
			"wallet_id":          walletID,
			"device_fingerprint": fingerprint,
		})
		return nil, fmt.Errorf("failed to register device: %w", err)
	}

	// Log device registration
	d.logger.LogSecurity("device_registered", fmt.Sprintf("%d", walletID), deviceInfo.IPAddress,
		fmt.Sprintf("New device registered: %s (%s)", device.DeviceName, fingerprint))

	return &DeviceRegistrationResult{
		DeviceID:             device.ID,
		DeviceFingerprint:    fingerprint,
		VerificationCode:     verificationCode,
		VerificationExpiry:   verificationExpiry,
		RequiresVerification: true,
		TrustLevel:           trustLevel,
	}, nil
}

// VerifyDevice verifies a device using the verification code
func (d *DeviceManagementService) VerifyDevice(deviceFingerprint, verificationCode string) error {
	var device models.DeviceRegistration
	if err := d.db.Where("device_fingerprint = ? AND verification_code = ?", deviceFingerprint, verificationCode).First(&device).Error; err != nil {
		return fmt.Errorf("invalid verification code or device not found")
	}

	// Check if verification code has expired
	if device.VerificationExpiry != nil && time.Now().After(*device.VerificationExpiry) {
		return fmt.Errorf("verification code has expired")
	}

	// Verify the device
	device.IsVerified = true
	device.Status = "active"
	device.VerificationCode = ""
	device.VerificationExpiry = nil

	// Increase trust level after verification
	if device.TrustLevel == "unknown" {
		device.TrustLevel = "low"
	} else if device.TrustLevel == "low" {
		device.TrustLevel = "medium"
	}

	if err := d.db.Save(&device).Error; err != nil {
		return fmt.Errorf("failed to verify device: %w", err)
	}

	// Log device verification
	d.logger.LogSecurity("device_verified", fmt.Sprintf("%d", device.WalletID), device.IPAddress,
		fmt.Sprintf("Device verified: %s", deviceFingerprint))

	return nil
}

// GetDevicesByWallet gets all devices for a wallet
func (d *DeviceManagementService) GetDevicesByWallet(walletID uint) ([]models.DeviceRegistration, error) {
	var devices []models.DeviceRegistration
	if err := d.db.Where("wallet_id = ?", walletID).Order("last_used DESC").Find(&devices).Error; err != nil {
		return nil, fmt.Errorf("failed to get devices: %w", err)
	}
	return devices, nil
}

// UpdateDeviceTrustLevel updates the trust level of a device
func (d *DeviceManagementService) UpdateDeviceTrustLevel(deviceFingerprint, trustLevel string) error {
	validTrustLevels := []string{"unknown", "low", "medium", "high"}
	if !d.contains(validTrustLevels, trustLevel) {
		return fmt.Errorf("invalid trust level: %s", trustLevel)
	}

	result := d.db.Model(&models.DeviceRegistration{}).
		Where("device_fingerprint = ?", deviceFingerprint).
		Update("trust_level", trustLevel)

	if result.Error != nil {
		return fmt.Errorf("failed to update trust level: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("device not found")
	}

	return nil
}

// RevokeDevice revokes a device (marks it as revoked)
func (d *DeviceManagementService) RevokeDevice(walletID uint, deviceFingerprint string) error {
	result := d.db.Model(&models.DeviceRegistration{}).
		Where("wallet_id = ? AND device_fingerprint = ?", walletID, deviceFingerprint).
		Updates(map[string]interface{}{
			"status":      "revoked",
			"is_trusted":  false,
			"trust_level": "unknown",
		})

	if result.Error != nil {
		return fmt.Errorf("failed to revoke device: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("device not found")
	}

	// Log device revocation
	d.logger.LogSecurity("device_revoked", fmt.Sprintf("%d", walletID), "",
		fmt.Sprintf("Device revoked: %s", deviceFingerprint))

	return nil
}

// IsDeviceTrusted checks if a device is trusted for a wallet
func (d *DeviceManagementService) IsDeviceTrusted(walletID uint, deviceFingerprint string) (bool, error) {
	var device models.DeviceRegistration
	if err := d.db.Where("wallet_id = ? AND device_fingerprint = ? AND status = 'active'", walletID, deviceFingerprint).First(&device).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return false, nil
		}
		return false, fmt.Errorf("failed to check device trust: %w", err)
	}

	return device.IsVerified && (device.TrustLevel == "medium" || device.TrustLevel == "high"), nil
}

// generateDeviceFingerprint generates a unique fingerprint for a device
func (d *DeviceManagementService) generateDeviceFingerprint(deviceInfo *DeviceInfo) string {
	// Combine key device characteristics
	data := fmt.Sprintf("%s|%s|%s|%s|%s|%s|%s",
		deviceInfo.DeviceType,
		deviceInfo.OS,
		deviceInfo.Model,
		deviceInfo.Brand,
		deviceInfo.ScreenSize,
		deviceInfo.Timezone,
		deviceInfo.UserAgent,
	)

	// Create SHA256 hash
	hash := sha256.Sum256([]byte(data))
	return hex.EncodeToString(hash[:])
}

// generateVerificationCode generates a 6-digit verification code
func (d *DeviceManagementService) generateVerificationCode() string {
	bytes := make([]byte, 3)
	rand.Read(bytes)

	// Convert to 6-digit number
	code := int(bytes[0])<<16 | int(bytes[1])<<8 | int(bytes[2])
	return fmt.Sprintf("%06d", code%1000000)
}

// generateDeviceName generates a human-readable device name
func (d *DeviceManagementService) generateDeviceName(deviceInfo *DeviceInfo) string {
	if deviceInfo.Brand != "" && deviceInfo.Model != "" {
		return fmt.Sprintf("%s %s", deviceInfo.Brand, deviceInfo.Model)
	}
	if deviceInfo.OS != "" {
		return fmt.Sprintf("%s Device", deviceInfo.OS)
	}
	return fmt.Sprintf("%s Device", strings.Title(deviceInfo.DeviceType))
}

// calculateInitialTrustLevel calculates initial trust level for a new device
func (d *DeviceManagementService) calculateInitialTrustLevel(walletID uint, deviceInfo *DeviceInfo) string {
	// Check if user has other verified devices
	var verifiedCount int64
	d.db.Model(&models.DeviceRegistration{}).
		Where("wallet_id = ? AND is_verified = true AND status = 'active'", walletID).
		Count(&verifiedCount)

	// If user has verified devices, start with low trust
	if verifiedCount > 0 {
		return "low"
	}

	// For first device, check device characteristics
	if deviceInfo.DeviceType == "mobile" && (deviceInfo.OS == "iOS" || deviceInfo.OS == "Android") {
		return "low" // Mobile devices get slightly higher initial trust
	}

	return "unknown"
}

// contains checks if a slice contains a string
func (d *DeviceManagementService) contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}
