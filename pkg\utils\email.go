package utils

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
)

// SendEmailViaAPI sends an email using the Centurion Email API
func SendEmailViaAPI(to, subject, body string) error {
	apiKey := os.Getenv("CENTURION_EMAIL_API_KEY")
	if apiKey == "" {
		return fmt.Errorf("CENTURION_EMAIL_API_KEY not set in environment")
	}

	url := "https://auth.centurionbd.com/api/v1/email/send"
	payload := map[string]string{
		"to":      to,
		"subject": subject,
		"body":    body,
	}
	jsonData, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("failed to marshal email payload: %w", err)
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create email request: %w", err)
	}

	req.Header.Set("X-API-Key", apiKey)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send email request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return fmt.Errorf("Email API returned status %d", resp.StatusCode)
	}

	return nil
}
